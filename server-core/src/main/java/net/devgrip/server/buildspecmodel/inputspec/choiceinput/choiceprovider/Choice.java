package net.devgrip.server.buildspecmodel.inputspec.choiceinput.choiceprovider;

import net.devgrip.commons.codeassist.InputSuggestion;
import net.devgrip.server.annotation.Color;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.web.util.SuggestionUtils;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Editable(name="Value")
public class Choice implements Serializable {

	private static final long serialVersionUID = 1L;

	private String value;
	
	private String color = "#0d87e9";
	
	private String applicableProjects;

	@Editable(order=100,name = "Value")
	@NotEmpty
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Editable(order=200, name = "Color")
	@NotEmpty
	@Color
	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	@Editable(order=300, name = "Applicable_Projects", placeholder="All_Projects", description="Applicable_Projects_Desc")
	@Patterns(suggester="suggestProjects", path=true)
	public String getApplicableProjects() {
		return applicableProjects;
	}

	public void setApplicableProjects(String applicableProjects) {
		this.applicableProjects = applicableProjects;
	}

	@SuppressWarnings("unused")
	private static List<InputSuggestion> suggestProjects(String matchWith) {
		return SuggestionUtils.suggestProjectPaths(matchWith);
	}
	
}

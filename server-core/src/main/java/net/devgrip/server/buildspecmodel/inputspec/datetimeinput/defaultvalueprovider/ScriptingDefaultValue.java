package net.devgrip.server.buildspecmodel.inputspec.datetimeinput.defaultvalueprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.annotation.ScriptChoice;
import net.devgrip.server.util.GroovyUtils;

import javax.validation.constraints.NotEmpty;
import java.util.Date;

@Editable(order=400, name="ScriptingDefaultValue.name")
public class ScriptingDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private String scriptName;

	@Editable(placeholder = "ScriptingDefaultValue.script.name", description="ScriptingDateDefaultValue.script.desc")
	@ScriptChoice
	@OmitName
	@NotEmpty
	public String getScriptName() {
		return scriptName;
	}

	public void setScriptName(String scriptName) {
		this.scriptName = scriptName;
	}

	@Override
	public Date getDefaultValue() {
		return (Date) GroovyUtils.evalScriptByName(scriptName);
	}

}

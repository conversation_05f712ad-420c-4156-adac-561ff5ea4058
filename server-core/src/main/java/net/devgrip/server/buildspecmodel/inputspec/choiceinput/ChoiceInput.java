package net.devgrip.server.buildspecmodel.inputspec.choiceinput;

import edu.emory.mathcs.backport.java.util.Collections;
import net.devgrip.server.AppServer;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.buildspecmodel.inputspec.choiceinput.choiceprovider.ChoiceProvider;

import javax.validation.ValidationException;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ChoiceInput {
	
	public static List<String> getPossibleValues(ChoiceProvider choiceProvider) {
		List<String> possibleValues = new ArrayList<>();
		if (AppServer.getInstance(Validator.class).validate(choiceProvider).isEmpty())
			possibleValues.addAll(choiceProvider.getChoices(true).keySet());
		return possibleValues;
	}

	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes, 
			ChoiceProvider choiceProvider, DefaultValueProvider defaultValueProvider, 
			DefaultMultiValueProvider defaultMultiValueProvider) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, inputSpec.isAllowMultiple()? "List<String>": "String");
		inputSpec.appendCommonAnnotations(buffer, index);
		if (!inputSpec.isAllowEmpty()) {
			if (inputSpec.isAllowMultiple())
				buffer.append("    @Size(min=1, message=\"{AtLeastOneOptionNeedsToBeSelected}\")\n");
			else
				buffer.append("    @NotEmpty\n");
		}
		inputSpec.appendChoiceProvider(buffer, index, "@ChoiceProvider");
		
		if (inputSpec.isAllowMultiple())
			inputSpec.appendMethods(buffer, index, "List<String>", choiceProvider, defaultMultiValueProvider);
		else 
			inputSpec.appendMethods(buffer, index, "String", choiceProvider, defaultValueProvider);
		
		return buffer.toString();
	}

	public static Object convertToObject(InputSpec inputSpec, List<String> strings) {
		if (inputSpec.isAllowMultiple()) {
			List<String> possibleValues = inputSpec.getPossibleValues();
			if (!possibleValues.isEmpty()) {
				List<String> copyOfStrings = new ArrayList<>(strings);
				copyOfStrings.removeAll(possibleValues);
				if (!copyOfStrings.isEmpty())
					throw new ValidationException("Invalid choice values: " + copyOfStrings);
				else
					return strings;
			} else {
				return strings;
			}
		} else if (strings.size() == 0) {
			return null;
		} else if (strings.size() == 1) {
			String value = strings.iterator().next();
			List<String> possibleValues = inputSpec.getPossibleValues();
			if (!possibleValues.isEmpty()) {
				if (!possibleValues.contains(value))
					throw new ValidationException("Invalid choice value");
				else
					return value;
			} else {
				return value;
			}
		} else {
			throw new ValidationException("Not eligible for multi-value");
		}
	}

	@SuppressWarnings("unchecked")
	public static List<String> convertToStrings(InputSpec inputSpec, Object value) {
		List<String> strings = new ArrayList<>();
		if (inputSpec.isAllowMultiple()) {
			if (inputSpec.checkListElements(value, String.class))
				strings.addAll((List<String>) value);
			Collections.sort(strings);
		} else if (value instanceof String) {
			strings.add((String) value);
		} 
		return strings;
	}

}

package net.devgrip.server.buildspecmodel.inputspec.userchoiceinput.choiceprovider;

import com.google.common.collect.Sets;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.entitymanager.UserManager;
import net.devgrip.server.model.User;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.util.facade.UserCache;
import net.devgrip.server.web.page.project.issues.detail.IssueDetailPage;
import net.devgrip.server.web.util.WicketUtils;

import java.util.ArrayList;
import java.util.List;

@Editable(order=130, name="AllUsers.name")
public class AllUsers implements ChoiceProvider {

	private static final long serialVersionUID = 1L;

	@Override
	public List<User> getChoices(boolean allPossible) {
		UserCache cache = AppServer.getInstance(UserManager.class).cloneCache();
		
		if (WicketUtils.getPage() instanceof IssueDetailPage) {
			IssueDetailPage issueDetailPage = (IssueDetailPage) WicketUtils.getPage();
			List<User> users = new ArrayList<>(cache.getUsers());
			users.sort(cache.comparingDisplayName(issueDetailPage.getIssue().getParticipants()));
			return users;
		} else if (SecurityUtils.getAuthUser() != null) {
			List<User> users = new ArrayList<>(cache.getUsers());
			users.sort(cache.comparingDisplayName(Sets.newHashSet(SecurityUtils.getAuthUser())));
			return users;
		} else {
			List<User> users = new ArrayList<>(cache.getUsers());
			users.sort(cache.comparingDisplayName(Sets.newHashSet()));
			return users;
		}
	}

}

package net.devgrip.server.buildspecmodel.inputspec.integerinput.defaultvalueprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

@Editable(order=100, name="SpecifiedDefaultValue.name")
public class SpecifiedDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private int value;

	@Editable(name="SpecifiedDefaultValue.value.name")
	@OmitName
	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	@Override
	public int getDefaultValue() {
		return getValue();
	}

}

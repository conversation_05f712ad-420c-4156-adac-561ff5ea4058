package net.devgrip.server.buildspecmodel.inputspec;

import java.util.List;

import javax.annotation.Nullable;

import net.devgrip.server.util.ComponentContext;
import net.devgrip.server.web.util.WicketUtils;

public interface InputContext {

	List<String> getInputNames();
	
	@Nullable
	InputSpec getInputSpec(String inputName);

	@Nullable
	public static InputContext get() {
		ComponentContext componentContext = ComponentContext.get();
		if (componentContext != null)
			return WicketUtils.findInnermost(componentContext.getComponent(), InputContext.class);
		else
			return null;
	}
	
}

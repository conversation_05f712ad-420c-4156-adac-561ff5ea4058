package net.devgrip.server.buildspecmodel.inputspec.dateinput.defaultvalueprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Editable(order=100, name="SpecifiedDefaultValue.name")
public class SpecifiedDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private Date value;

	@Editable(name="SpecifiedDefaultValue.value.name")
	@NotNull(message="{MayNotBeEmpty.message}")
	@OmitName
	public Date getValue() {
		return value;
	}

	public void setValue(Date value) {
		this.value = value;
	}

	@Override
	public Date getDefaultValue() {
		return getValue();
	}

}

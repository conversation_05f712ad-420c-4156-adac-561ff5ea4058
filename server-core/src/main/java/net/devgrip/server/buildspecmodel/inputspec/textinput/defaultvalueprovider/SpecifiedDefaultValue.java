package net.devgrip.server.buildspecmodel.inputspec.textinput.defaultvalueprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

import javax.validation.constraints.NotEmpty;

@Editable(order=100, name="SpecifiedDefaultValue.name")
public class SpecifiedDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private String value;

	@Editable(name="SpecifiedDefaultValue.value.name")
	@NotEmpty
	@OmitName
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String getDefaultValue() {
		return getValue();
	}

}

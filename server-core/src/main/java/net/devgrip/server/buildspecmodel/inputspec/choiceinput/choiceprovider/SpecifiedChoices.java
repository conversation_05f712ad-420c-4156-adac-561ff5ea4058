package net.devgrip.server.buildspecmodel.inputspec.choiceinput.choiceprovider;

import net.devgrip.commons.utils.match.Matcher;
import net.devgrip.commons.utils.match.PathMatcher;
import net.devgrip.server.annotation.ClassValidating;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.buildspec.param.spec.choiceparam.ChoiceParam;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.support.issue.field.spec.choicefield.ChoiceField;
import net.devgrip.server.util.patternset.PatternSet;
import net.devgrip.server.util.usage.Usage;
import net.devgrip.server.validation.Validatable;

import javax.annotation.Nullable;
import javax.validation.ConstraintValidatorContext;
import javax.validation.constraints.Size;
import java.util.*;
import java.util.stream.Collectors;

@Editable(order=100, name="SpecifiedChoices.name")
@ClassValidating
public class SpecifiedChoices extends ChoiceProvider implements Validatable {

	private static final long serialVersionUID = 1L;

	private List<Choice> choices = new ArrayList<>();

	@Editable(name="SpecifiedChoices.choices.name")
	@Size(min=1, message="{AtLeastOneChoiceNeedToSpecified}")
	@OmitName
	public List<Choice> getChoices() {
		return choices;
	}

	public void setChoices(List<Choice> choices) {
		this.choices = choices;
	}

	@Override
	public Map<String, String> getChoices(boolean allPossible) {
		Map<String, String> choices = new LinkedHashMap<>();
		Project project = Project.get();
		Matcher matcher = new PathMatcher();
		for (Choice choice: getChoices()) {
			if (project == null || choice.getApplicableProjects() == null 
					|| PatternSet.parse(choice.getApplicableProjects()).matches(matcher, project.getPath())) {
				choices.put(choice.getValue(), choice.getColor());
			}
		}
		return choices;
	}
	
	public List<String> getChoiceValues() {
		return choices.stream().map(it->it.getValue()).collect(Collectors.toList());
	}
	
	@Nullable
	public static SpecifiedChoices of(@Nullable InputSpec inputSpec) {
		if (inputSpec instanceof ChoiceField) { 
			ChoiceField choiceField = (ChoiceField) inputSpec;
			if (choiceField.getChoiceProvider() instanceof SpecifiedChoices) 
				return (SpecifiedChoices) choiceField.getChoiceProvider();
		} else if (inputSpec instanceof ChoiceParam) { 
			ChoiceParam choiceParam = (ChoiceParam) inputSpec;
			if (choiceParam.getChoiceProvider() instanceof SpecifiedChoices) 
				return (SpecifiedChoices) choiceParam.getChoiceProvider();
		} 
 
		return null;
	}

	@Override
	public boolean isValid(ConstraintValidatorContext context) {
		Set<String> existing = new HashSet<>();
		for (Choice choice: choices) {
			if (existing.contains(choice.getValue())) {
				context.disableDefaultConstraintViolation();
				context.buildConstraintViolationWithTemplate("Duplicate choice: " + choice.getValue()).addConstraintViolation();
				return false;
			} else {
				existing.add(choice.getValue());
			}
		}
		return true;
	}

	@Override
	public void onMoveProject(String oldPath, String newPath) {
		for (Choice choice: choices) 
			choice.setApplicableProjects(Project.substitutePath(choice.getApplicableProjects(), oldPath, newPath));
	}
	
	@Override
	public Usage onDeleteProject(String projectPath) {
		Usage usage = new Usage();
		int index = 1;
		for (Choice choice: choices) {
			if (Project.containsPath(choice.getApplicableProjects(), projectPath))
				usage.add("applicable projects").prefix("available choice #" + index);
			index++;
		}
		return usage;
	}
	
}

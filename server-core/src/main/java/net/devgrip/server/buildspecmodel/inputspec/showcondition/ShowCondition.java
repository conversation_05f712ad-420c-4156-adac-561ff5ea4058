package net.devgrip.server.buildspecmodel.inputspec.showcondition;

import com.google.common.base.Preconditions;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.buildspecmodel.inputspec.InputContext;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.util.EditContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Editable
public class ShowCondition implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private static final Logger logger = LoggerFactory.getLogger(ShowCondition.class);
	
	private String inputName;
	
	private ValueMatcher valueMatcher = new ValueIsOneOf();
	
	@Editable(order=100, name="ShowCondition.when.name", placeholder = "Please_Choose")
	@ChoiceProvider("getNameChoices")
	@NotEmpty
	public String getInputName() {
		return inputName;
	}

	public void setInputName(String inputName) {
		this.inputName = inputName;
	}

	@Editable(order=200)
	@OmitName
	@NotNull(message="{MayNotBeEmpty.message}")
	public ValueMatcher getValueMatcher() {
		return valueMatcher;
	}

	public void setValueMatcher(ValueMatcher valueMatcher) {
		this.valueMatcher = valueMatcher;
	}

	@SuppressWarnings("unused")
	private static List<String> getNameChoices() {
		return new ArrayList<>(Preconditions.checkNotNull(InputContext.get().getInputNames()));
	}
	
	public boolean isVisible() {
		InputSpec inputSpec = Preconditions.checkNotNull(InputContext.get()).getInputSpec(getInputName());
		if (inputSpec != null) {
			Object inputValue = EditContext.get().getInputValue(getInputName());
			return getValueMatcher().matches(inputSpec.convertToStrings(inputValue));
		} else {
			logger.error("Unable to find input spec: " + getInputName());
			return false;
		}
	}
	
}

package net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.defaultvalueprovider;

import com.google.common.collect.Lists;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.GroupChoice;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.choiceprovider.ChoiceProvider;
import net.devgrip.server.model.Group;
import net.devgrip.server.util.EditContext;

import javax.validation.Validator;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Editable(order=100, name="SpecifiedDefaultValue.name")
public class SpecifiedDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private String value;

	@Editable(name="DefaulValue.value.name", placeholder = "Please_Choose")
	@GroupChoice("getValueChoices")
	@NotEmpty
	@OmitName
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	@Override
	public String getDefaultValue() {
		return getValue();
	}

	@SuppressWarnings("unused")
	private static List<Group> getValueChoices() {
		ChoiceProvider choiceProvider = (ChoiceProvider) EditContext.get(1).getInputValue("choiceProvider");
		if (choiceProvider != null && AppServer.getInstance(Validator.class).validate(choiceProvider).isEmpty())
			return choiceProvider.getChoices(true);
		else
			return Lists.newArrayList();
	}
	
}

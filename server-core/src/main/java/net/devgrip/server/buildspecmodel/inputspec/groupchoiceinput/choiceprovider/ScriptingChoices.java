package net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.choiceprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.annotation.ScriptChoice;
import net.devgrip.server.model.Group;
import net.devgrip.server.util.GroovyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Editable(order=300, name="ScriptingChoices.name")
public class ScriptingChoices implements ChoiceProvider {

	private static final long serialVersionUID = 1L;
	
	private static final Logger logger = LoggerFactory.getLogger(ScriptingChoices.class);

	private String scriptName;

	@Editable(placeholder = "Please_Choose", description="ScriptingGroupChoice.script.name")
	@ScriptChoice
	@OmitName
	@NotEmpty
	public String getScriptName() {
		return scriptName;
	}

	public void setScriptName(String scriptName) {
		this.scriptName = scriptName;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Group> getChoices(boolean allPossible) {
		Map<String, Object> variables = new HashMap<>();
		variables.put("allPossible", allPossible);
		try {
			return (List<Group>) GroovyUtils.evalScriptByName(scriptName, variables);
		} catch (RuntimeException e) {
			if (allPossible) {
				logger.error("Error getting all possible choices", e);
				return new ArrayList<>();
			} else {
				throw e;
			}
		}
	}

}

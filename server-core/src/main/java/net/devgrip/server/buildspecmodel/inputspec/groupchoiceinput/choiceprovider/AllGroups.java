package net.devgrip.server.buildspecmodel.inputspec.groupchoiceinput.choiceprovider;

import edu.emory.mathcs.backport.java.util.Collections;
import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.entitymanager.GroupManager;
import net.devgrip.server.model.Group;

import java.util.Comparator;
import java.util.List;

@Editable(order=100, name="AllGroups.name")
public class AllGroups implements ChoiceProvider {

	private static final long serialVersionUID = 1L;

	@Override
	public List<Group> getChoices(boolean allPossible) {
		List<Group> groups = AppServer.getInstance(GroupManager.class).query();
		Collections.sort(groups, new Comparator<Group>() {

			@Override
			public int compare(Group o1, Group o2) {
				return o1.getName().compareTo(o2.getName());
			}
			
		});
		return groups;
	}

}

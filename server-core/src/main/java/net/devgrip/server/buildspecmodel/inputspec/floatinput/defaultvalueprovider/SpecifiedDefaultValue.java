package net.devgrip.server.buildspecmodel.inputspec.floatinput.defaultvalueprovider;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;

@Editable(order=100, name="SpecifiedDefaultValue.name")
public class SpecifiedDefaultValue implements DefaultValueProvider {

	private static final long serialVersionUID = 1L;

	private float value;

	@Editable(name="SpecifiedDefaultValue.value.name")
	@OmitName
	public float getValue() {
		return value;
	}

	public void setValue(float value) {
		this.value = value;
	}

	@Override
	public float getDefaultValue() {
		return getValue();
	}

}

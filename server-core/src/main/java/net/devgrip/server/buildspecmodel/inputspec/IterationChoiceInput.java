package net.devgrip.server.buildspecmodel.inputspec;

import edu.emory.mathcs.backport.java.util.Collections;
import net.devgrip.server.model.Iteration;
import net.devgrip.server.model.Project;

import javax.validation.ValidationException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class IterationChoiceInput {
	
	public static String getPropertyDef(InputSpec inputSpec, Map<String, Integer> indexes) {
		int index = indexes.get(inputSpec.getName());
		StringBuffer buffer = new StringBuffer();
		inputSpec.appendField(buffer, index, inputSpec.isAllowMultiple()? "List<String>": "String");
		inputSpec.appendCommonAnnotations(buffer, index);
		if (!inputSpec.isAllowEmpty()) {
			if (inputSpec.isAllowMultiple())
				buffer.append("    @Size(min=1, message=\"{AtLeastOneIterationNeedsToBeSelected}\")\n");
			else
				buffer.append("    @NotEmpty\n");
		}
		
		buffer.append("    @IterationChoice\n");
		
		if (inputSpec.isAllowMultiple())
			inputSpec.appendMethods(buffer, index, "List<String>", null, null);
		else 
			inputSpec.appendMethods(buffer, index, "String", null, null);
		
		return buffer.toString();
	}

	public static Object convertToObject(InputSpec inputSpec, List<String> strings) {
		if (inputSpec.isAllowMultiple()) 
			return strings;
		else if (strings.size() == 0) 
			return null;
		else if (strings.size() == 1)
			return strings.iterator().next();
		else 
			throw new ValidationException("Not eligible for multi-value");
	}

	@SuppressWarnings("unchecked")
	public static List<String> convertToStrings(InputSpec inputSpec, Object value) {
		List<String> strings = new ArrayList<>();
		if (inputSpec.isAllowMultiple()) {
			if (inputSpec.checkListElements(value, String.class))
				strings.addAll((List<String>) value);
			Collections.sort(strings);
		} else if (value instanceof String) {
			strings.add((String) value);
		} 
		return strings;
	}

	public static int getOrdinal(String fieldValue) {
		int ordinal = -1;
		Project project = Project.get();
		if (project != null) {
			List<Iteration> iterations = new ArrayList<>(project.getHierarchyIterations());
			Collections.sort(iterations);
			for (Iteration iteration: iterations) {
				ordinal++;
				if (iteration.getName().equals(fieldValue))
					break;
			}
		} 
		return ordinal;
	}
	
}

package net.devgrip.server.buildspecmodel.inputspec.showcondition;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.buildspecmodel.inputspec.InputContext;
import net.devgrip.server.buildspecmodel.inputspec.InputSpec;
import net.devgrip.server.util.EditContext;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.Size;
import java.util.List;

@Editable(order=200, name="ValueIsNotAnyOf.name")
public class ValueIsNotAnyOf implements ValueMatcher {

	private static final long serialVersionUID = 1L;
	
	private static final Logger logger = LoggerFactory.getLogger(ValueIsNotAnyOf.class);

	private List<String> values;

	@Editable(placeholder = "ShowCondition.values.placeholder")
	@ChoiceProvider("getValueChoices")
	@OmitName
	@Size(min=1, message="{AtLeastOneValueNeedToSpecified}")
	public List<String> getValues() {
		return values;
	}

	public void setValues(List<String> values) {
		this.values = values;
	}

	@SuppressWarnings("unused")
	private static List<String> getValueChoices() {
		// Access on-screen value of ShowCondition.fiedName
		String inputName = (String) EditContext.get(1).getInputValue("inputName");
		if (inputName != null) {
			InputSpec inputSpec = Preconditions.checkNotNull(InputContext.get()).getInputSpec(inputName);
			if (inputSpec != null) 
				return inputSpec.getPossibleValues();
			else
				logger.error("Unable to find input spec: " + inputName);
		}
		return Lists.newArrayList();
	}

	@Override
	public boolean matches(List<String> values) {
		return !CollectionUtils.containsAny(getValues(), values);
	}
	
}

#### Begin, 这些绝对不能删,因为它们都是代码中动态拼接出key的
RevisionDiffPanel.Unified=Unified view
RevisionDiffPanel.Split=Split view
IGNORE_TRAILING=Ignore trailing whitespace
IGNORE_LEADING=Ignore leading whitespace
IGNORE_CHANGE=Ignore change whitespace
IGNORE_ALL=Ignore all whitespace
DO_NOT_IGNORE=Do not ignore whitespace
Build.Status.waiting=waiting
Build.Status.pending=pending
Build.Status.running=running
Build.Status.failed=failed
Build.Status.cancelled=cancelled
Build.Status.timed_out=timed out
Build.Status.successful=successful
Build.Status.upper.waiting=Waiting
Build.Status.upper.pending=Pending
Build.Status.upper.running=Running
Build.Status.upper.failed=Failed
Build.Status.upper.cancelled=Cancelled
Build.Status.upper.timed_out=Timed Out
Build.Status.upper.successful=Successful
UploadStrategy.upload_if_not_hit=Upload if not hit
UploadStrategy.upload_if_changed=Upload if changed
ExecuteCondition.successful=Successful
ExecuteCondition.always=Always
ExecuteCondition.never=Never
PullRequest.Status.open=open
PullRequest.Status.merged=merged
PullRequest.Status.discarded=discarded
PullRequest.Status.upper.open=Open
PullRequest.Status.upper.merged=Merged
PullRequest.Status.upper.discarded=Discarded
CodePrivilege.write=Write
CodePrivilege.read=Read
CodePrivilege.none=None
PackPrivilege.write=Write
PackPrivilege.read=Read
PackPrivilege.none=None
DateRangeType.month=Month
DateRangeType.week=Week
RowType.users=Users
RowType.issues=Issues
StatsGroup.by_day=By day
StatsGroup.by_week=By week
StatsGroup.by_month=By month
FixType.change_to_another_value=Change to another value
FixType.delete_this_value=Delete this value
FixType.change_to_another_state=Change to another value
FixType.delete_this_state=Delete this value
FixType.change_to_another_field=Change to another value
FixType.delete_this_field=Delete this value
EventType.code_push=Code push
EventType.pull_request=Pull request
EventType.issue=issue
EventType.code_comment=Code comment
EventType.build=Build
EventType.package=Package
UnitTestReport.Status.not_passed=not passed
UnitTestReport.Status.other=other
UnitTestReport.Status.not_run=not run
UnitTestReport.Status.passed=passed
Severity.critical=CRITICAL
Severity.high=HIGH
Severity.medium=MEDIUM
Severity.low=LOW
#### End, 这些绝对不能删,因为它们都是代码中动态拼接出key的
Create\ Administrator\ Account=Create Administrator Account
Specify\ System\ Setting=Specify System Setting
Select\ Language=Select Language
Specify\ Service\ Desk\ Setting=Specify Service Desk Setting
Specify\ Backup\ Setting=Specify Backup Setting
Server\ is\ Starting=Server is Starting...
Server\ Setup=Server Setup
Please\ Wait=Please Wait
IssueLink.relatesTo=Relates To
IssueLink.duplicates=Duplicates
IssueLink.childIssue=Child Issue
IssueLink.parentIssue=Parent Issue
IssueNamedQuery.open=Open
IssueNamedQuery.assignedToMeAndOpen=Assigned to me & Open
IssueNamedQuery.submittedByMeAndOpen=Submitted by me & Open
IssueNamedQuery.assignedToMe=Assigned to me
IssueNamedQuery.submittedByMe=Submitted by me
IssueNamedQuery.submittedRecently=Submitted recently
IssueNamedQuery.mentionedMe=Mentioned me
IssueNamedQuery.hasActivityRecently=Has activity recently
IssueNamedQuery.openAndCritical=Open & Critical
IssueNamedQuery.openAndUnassigned=Open & Unassigned
IssueNamedQuery.openAndUnscheduled=Open & Unscheduled
IssueNamedQuery.closed=Closed
IssueNamedQuery.all=All
DefaultIssueFieldName.type=Type
DefaultIssueFieldName.assignee=Assignees
DefaultIssueFieldName.priority=Priority
DefaultIssueFieldValue.type.newFeature=New Feature
DefaultIssueFieldValue.type.bug=Bug
DefaultIssueFieldValue.type.task=Task
DefaultIssueFieldValue.type.improvement=Improvement
DefaultIssueFieldValue.priority.minor=Minor
DefaultIssueFieldValue.priority.normal=Normal
DefaultIssueFieldValue.priority.major=Major
DefaultIssueFieldValue.priority.critical=Critical
DefaultIssueFieldValue.assignee.empty=Not assigned
DefaultIssueFieldValue.state.open=Open
DefaultIssueFieldValue.state.closed=Closed
DefaultIssueFieldValue.state.inProgress=In progress
DefaultIssueBoard.name=Default
PullRequestNamedQuery.open=Open
PullRequestNamedQuery.needMyAction=Need my action
PullRequestNamedQuery.toBeReviewedByMe=To be reviewed by me
PullRequestNamedQuery.toBeChangedByMe=To be changed by me
PullRequestNamedQuery.toBeMergedByMe=To be merged by me
PullRequestNamedQuery.requestedForChangesByMe=requested for changes by me
PullRequestNamedQuery.assignedToMe=Assigned to me
PullRequestNamedQuery.approvedByMe=Approved by me
PullRequestNamedQuery.submittedByMe=Submitted by me
PullRequestNamedQuery.submittedRecently=Submitted recently
PullRequestNamedQuery.updatedRecently=Updated recently
PullRequestNamedQuery.merged=Merged
PullRequestNamedQuery.discarded=Discarded
PullRequestNamedQuery.all=All
ProjectNamedQuery.my=My projects
ProjectNamedQuery.all=All
PackNamedQuery.all=All
AgentNamedQuery.all=All agents
AgentNamedQuery.online=Online agents
AgentNamedQuery.offline=Offline agents
AgentNamedQuery.linux=Linux agents
AgentNamedQuery.windows=Windows agents
AgentNamedQuery.macos=macOS agents
AgentNamedQuery.freebsd=FreeBSD agents
AgentNamedQuery.paused=Paused agents
AgentNamedQuery.notUsedFor1Month=Not used for 1 month
BuildNamedQuery.all=All
BuildNamedQuery.successful=Successful
BuildNamedQuery.failed=Failed
BuildNamedQuery.cancelled=Cancelled
BuildNamedQuery.timedout=Timed out
BuildNamedQuery.running=Running
BuildNamedQuery.waiting=Waiting
BuildNamedQuery.pending=Pending
BuildNamedQuery.buildRecently=Build recently
CommitNamedQuery.all=All
CommitNamedQuery.defaultBranch=Default branch
CommitNamedQuery.committedByMe=Committed by me
CommitNamedQuery.committedRecently=Committed recently
CodeCommentNamedQuery.all=All
CodeCommentNamedQuery.unresolved=Unresolved
CodeCommentNamedQuery.resolved=Resolved
CodeCommentNamedQuery.createdByMe=Created by me
CodeCommentNamedQuery.mentionedMe=Mentioned me
CodeCommentNamedQuery.createdRecently=Created recently
CodeCommentNamedQuery.updatedRecently=Updated recently
Please_Wait=Please wait...
WebsocketError=Page is in error, reload to recover
Step_Indicator=Step ${index} of ${len}: ${title}
Login_Name=Login Name
Password=Password
Login_Name_Or_Email=Login name or email
Verify=Verify
Verify_by_Recovery_Code=Verify by recovery code
TOTP_Not_Access=Can not access your TOTP authenticator?
Full_Name=Full Name
Name=Name
Type=Type
Value=Value
Previous=Previous
Next=Next
Finish=Finish
Reset=Reset
Ok=Ok
Cancel=Cancel
Submit=Submit
Error=Error
Password_Reset_Url_Hint=The password reset url is invalid or obsolete
Password_Holder=Type password here
Confirm_Password_Holder=Confirm password here
SystemSetting.validateError.1=Protocol is not specified
SystemSetting.validateError.2=Protocol should be either http or https
SystemSetting.validateError.3=Host is not specified
SystemSetting.validateError.4=Path should not be specified
SystemSetting.validateError.5=Should start with either http:// or https://
SystemSetting.validateError.6=Malformed url
SystemSetting.validateError.7=This url should start with ssh://
SystemSetting.asl.name=Avatar Service Url
SystemSetting.asl.desc=User avatar will be requested by appending a hash to this url
SystemSetting.uas.name=Use Avatar Service
SystemSetting.uas.desc=Whether to use user avatar from a public service
DisableDashboard=Disable Dashboard
Server_Url=Server URL
Server_Url_Desc=Specify root URL to access this server
SSH_Root_URL=SSH Root URL
SSH_Root_Desc=Optionally specify SSH root URL, which will be used to construct project clone url via SSH protocol. Leave empty to derive from server url
disableAutoUpdateCheck=Disable AutoUpdate Check
Auto_Update_Check_Desc=Auto update check is performed by requesting an image in your browser from devgrip.net indicating new version availability, with color indicating severity of the update. Red for security/critical update, yellow for bug fix, blue for feature update. If disabled, you are highly recommended to check update manually from time to time to see if there are any security/critical fixes
Git_Location=Git Command Line
Git_Location_Desc=DevGrip requires git command line to manage repositories. The minimum required version is 2.11.1. Also make sure that git-lfs is installed if you want to retrieve LFS files in build job
Curl_Location=curl Command Line
Curl_Location_Desc=DevGrip configures git hooks to communicate with itself via curl
Use_Specified_Curl=Use Specified curl
Use_Specified_Git=Use Specified Git
Use_System_Path_Curl=Use curl in System Path
Use_System_Path_Git=Use Git in System Path
Specified_Git_Path_Name=Git Path
Specified_Curl_Path_Name=curl path
Specified_Git_Path_Desc=Specify path to git executable, for instance: <tt>/usr/bin/git</tt>
Specified_Curl_Path_Desc=Specify path to curl executable, for instance: <tt>/usr/bin/curl</tt>
Language=Language
Language_Desc=Please select language
System_Setting=System Setting
Settings_Saved=The settings have been Saved
security=Security
security.enableAnonymousAccess=Enable Anonymous Access
security.enableAnonymousAccess.description=Whether to allow anonymous users to access this server
security.enableSelfRegister.name=Enable Account Self Sign-Up
security.enableSelfRegister.description=User can sign up if this option is enabled
security.allowedSelfRegisterEmailDomain.name=Allowed Self Sign-Up Email Domain
security.allowedSelfRegisterEmailDomain.placeholder=Any domain
security.allowedSelfRegisterEmailDomain.description=Optionally specify allowed email domain for self sign-up users. Use '*' or '?' for pattern match
security.defaultGroup.name=Default Group
security.defaultGroup.description=Optionally add new users to specified default group
security.enableSelfDeregister.name=Enable Account Self Removal
security.enableSelfDeregister.description=Whether user can remove own account
security.enforce2FA.name=Enforce Two-factor Authentication
security.enforce2FA.description=Check this to enforce two-factor authentication for all users in the system
security.enforcePwdRule.name=Enforce More Flexible Password Rules
security.enforcePwdRule.desc=Enforce more flexible password rules for new users
security.usage.prefix=security settings
security.usage.desc=default group for sign-in users
security.corsAllowedOrigins.name=CORS Allowed Origins
security.corsAllowedOrigins.placeholder=Input allowed CORS origin, hit ENTER to add
security.corsAllowedOrigins.description=Optionally specify allowed <a href='https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS' target='_blank'>CORS</a> origins. For a CORS simple or preflight request, if value of request header <code>Origin</code> is included here, the response header <code>Access-Control-Allow-Origin</code> will be set to the same value
um.filterByNameOrEmail=Filter by name or email address
um.impersonateThisUser=Impersonate This User
um.enableSelectedUsers=Enable Selected Users
um.enableSelectedUsers.success=Users enabled successfully
um.enableSelectedUsers.noUserSelected=Please select users to enable
um.disableSelectedUsers=Disable Selected Users
um.disableSelectedUsers.success=Users disabled successfully
um.disableSelectedUsers.confirm=Disabling accounts will reset password, clear access tokens, and remove all references from other entities except for past activities. Type <code>yes</code> to confirm
um.disableSelectedUsers.canNotDisableRoot=Can not disable root account
um.disableSelectedUsers.canNotDisableYourself=Can not disable yourself
um.disableSelectedUsers.noUserSelected=Please select users to disable
um.enableAllQueriedUsers=Enable All Queried Users
um.enableAllQueriedUsers.success=Users enabled successfully
um.enableAllQueriedUsers.noUser=No users to enable
um.disableAllQueriedUsers=Disable All Queried Users
um.disableAllQueriedUsers.noUser=No users to disable
um.botAccount.label=bot
um.disabledAccount.label=disabled
um.notApplicableWithI=<i>N/A</i>
um.includeDisabledUsers=Include Disabled Users
um.foundUsersCount=found ${userCount} users
um.loginName=Login Name
um.fullName=Full Name
um.primaryEmail=Primary Email
um.authSource=Auth Source
um.internalDatabase=Internal Database
um.externalSystem=External System
um.emailUnverified=Unverified
um.continueCreate=Continue to add other user after create
um.userCreated=New user created
um.emailAlreadyUsed=Email address already used by another user
um.nameAlreadyUsed=Login name already used by another account
um.createUserTitle= Create User
um.email.name=Email Address
um.notifyOwnEvents.name=Notify Own Events
um.notifyOwnEvents.description=Whether to send notifications for events generated by yourself
um.botAccount.name=Bot Account
um.botAccount.desc1=Whether to create as a bot account for task automation purpose. Bot account does not have password and email addresses, and will not generate notifications for its activities. <b class='text-warning'>NOTE:</b> Bot account is an enterprise feature. <a href='https://devgrip.net/pricing' target='_blank'>Try free</a> for 30 days
um.botAccount.desc2=Whether to create as a bot account for task automation purpose. Bot account does not have password and email addresses, and will not generate notifications for its activities
userList.title=Users
userList.deleteSelectedUsers=Delete Selected Users
userList.cannotDeleteRoot=Cannot delete root account
userList.cannotDeleteYourself=Cannot delete yourself
userList.deleteUsersSuccess=Users deleted successfully
userList.deleteUsersConfirm=Type <code>yes</code> below to confirm deleting selected users
userList.deleteAllQueriedUsersConfirm=Type <code>yes</code> below to confirm deleting all queried users
userList.selectUsersToDelete=Please select users to delete
userList.noUserFound=No user found
userList.deleteAllQueriedUsers=Delete All Queried Users
UserAccessTokenPage.desc=Access token is intended for api access and repository pull/push. It can not be used to sign in to web ui 
UserProfilePage.authWithInternalDatabase.desc=This user is authenticating via internal database.
UserProfilePage.authWithInternalDatabase.removeLink=Remove password
UserProfilePage.authWithInternalDatabase.useExternalSystem=to force the user to authenticate via external system
UserProfilePage.authWithExternalSystem.desc=This user is authenticating via external system. To authenticate via internal database, 
UserProfilePage.authWithExternalSystem.setLink=set password for user
UserProfilePage.authWithExternalSystem.tellUserResetLink=tell user to reset password
UserProfilePage.removeSuccess=Password has been removed
UserProfilePage.removePassword.confirm=Do you really want to remove password of this user?
UserProfilePage.tellUserResetPassword.mailSubject=[Reset Password] Please Reset Your Password
UserProfilePage.tellUserResetPassword.success=Password reset request has been sent
UserProfilePage.tellUserResetPassword.noVerifiedPrimaryEmail=No verified primary email address
UserProfilePage.tellUserResetPassword.unableNotify=Unable to notify user as mail service is not configured
UserProfilePage.botAccountDesc=This is a service account for task automation purpose
UserProfilePage.thisAccountIsDisabled=This account is disabled
UserProfilePage.disabledBotAccount=This is a disabled service account
UserPasswordPage.desc=This user is currently authenticating via external system. Setting password will switch to use internal database
UserPage.Profile=Profle
UserPage.EmailAddress=Email Addresses
UserPage.EditAvatar=Edit_Avatar
UserPage.Password=Password
UserPage.BelongingGroups=Belonging Groups
UserPage.AuthorizedProjects=Authorized Projects
UserPage.SSHKeys=SSH Keys
UserPage.GPGKeys=GPG Keys
UserPage.AccessTokens=Access Tokens
UserPage.TwofactorAuthentication=Two-factor Authentication
UserPage.queryWatches=Query Watches
UserMembershipsPage.filterGroups.placeholder=Filter by name...
UserMembershipsPage.deleteLink=Delete
UserMembershipsPage.addToGroup.placeholder=Add to group...
UserMembershipsPage.addToGroup.success=Group added
UserMembershipsPage.deleteSelected.confirm=Type <code>yes</code> below to delete selected memberships
UserMembershipsPage.deleteSelected.title=Please select memberships to delete
UserMembershipsPage.deleteAllQueried.confirm=Type <code>yes</code> below to delete all queried memberships
UserMembershipsPage.noQueriedFound=No memberships to delete
UserMembershipsPage.groupsColumn.name=Name
UserMembershipsPage.groupsColumn.desc=Description
UserAuthorizationsPage.desc=When authorize a project, all child projects will also be authorized with the role
UserAuthorizationsPage.save.success=Project authorizations updated
UserAuthorizationsPage.save.fail=Duplicate authorizations found: ${project}
UserGpgKeysPage.desc=Add GPG keys here to verify commits/tags signed by this user
UserGpgKeysPage.howToUse=Check <a href="https://docs.github.com/en/authentication/managing-commit-signature-verification/about-commit-signature-verification#gpg-commit-signature-verification" target="_blank">GitHub's guide</a> on how to generate and use GPG keys to sign your commits
UserGpgKeysPage.notBelongTo=Email addresses with <span class="badge badge-warning badge-sm">ineffective</span> mark are those not belong to or not verified by key owner
UserGpgKeysPage.plusButtonTitle=Add GPG Key
RoleListPage.deleteConfirm=Do you really want to delete role ${name}?
RoleListPage.deleteSuccess=Role ${name} deleted
RoleListPage.cannotDelete=This is a built-in role and can not be deleted
RoleDetailPage.projectOwner.desc=is a built-in role with full permission over projects
RoleDetailPage.addFail=This name has already been used by another role.
RoleDetailPage.submitSuccess=Role updated
RoleDetailPage.deleteSuccess=Role ${name} deleted
RoleDetailPage.deleteConfirm=Do you really want to delete role ${name}?
GroupListPage.columnName=Name
GroupListPage.columnIsAdmin=Is Admin
GroupListPage.columnCanCreateRootProjects=Can Create Root Projects
GroupListPage.deleteConfirm=Do you really want to delete group ${name}?
GroupListPage.deleteSuccess=Group ${name} deleted
NewGroupPage.createGroup=Create Group
NewGroupPage.createSuccess=Group created
NewGroupPage.createFail=This name has already been used by another group
GroupPage.Profile=Profile
GroupPage.Members=Members
GroupPage.Authorized=Authorized Projects
GroupProfilePage.saveFail=This name has already been used by another group.
GroupProfilePage.saveSuccess=Profile updated
GroupProfilePage.deleteConfirm=Do you really want to delete this group?
GroupProfilePage.deleteSuccess=Group deleted
GroupMembershipsPage.addMember.placeholder=Add member...
GroupMembershipsPage.addMember.success=Member added
GroupMembershipsPage.deleteSelected.confirm=Type <code>yes</code> below to delete selected memberships
GroupMembershipsPage.deleteSelected.title=Please select memberships to delete
GroupMembershipsPage.deleteAllQueried.confirm=Type <code>yes</code> below to delete all queried memberships
GroupMembershipsPage.deleteAllQueried.title=No memberships to delete
GroupMembershipsPage.deleteMembers=Delete
GroupMembershipsPage.name=Name
GroupMembershipsPage.primaryEmail=Primary Email
GroupMembershipsPage.notSpecified=<i>Not specified</i>
GroupAuthorizationsPage.desc=When authorize a project, all child projects will also be authorized with assigned roles
GroupAuthorizationsPage.addFail=Duplicate authorizations found: ${name}
GroupAuthorizationsPage.addSuccess=Project authorizations updated
Group.description.desc=Optionally describe the group
Group.isAdmin.name=Is Administrator
Group.canCreateRootProjects.name= Can Create Root Projects
Group.canCreateRootProjects.desc=Whether to allow creating root projects (project without parent)
Group.enable2fa.name=Enforce Two-factor Authentication
Group.enable2fa.desc=Check this to enforce two-factor authentication for all users in this group
BrandSettingEditBean.url.name=Url
BrandSettingEditBean.url.desc=Specify url for your brand
BrandSettingEditBean.light.name=Logo for Light Mode
BrandSettingEditBean.light.desc=Upload a 128x128 transparent png file to be used as logo for light mode
BrandSettingEditBean.dark.name=Logo for Dark Mode
BrandSettingEditBean.dark.desc=Upload a 128x128 transparent png file to be used as logo for dark mode
BrandingSettingPage.updateSuccess=Branding settings updated
BrandingSettingPage.useDefault=Default branding settings restored
StorageSetting.lfs.name=Git LFS Storage
StorageSetting.lfs.desc=Optionally specify separate directory to store git lfs files. Non-absolute directory is considered to be relative to site directory
StorageSetting.artifact.name=Build Artifact Storage
StorageSetting.artifact.desc=Optionally specify separate directory to store build artifacts. Non-absolute directory is considered to be relative to site directory
StorageSetting.pack.name=Package Storage
StorageSetting.pack.desc=Optionally specify separate directory to store package files. Non-absolute directory is considered to be relative to site directory
ClusterManagementPage.saveAndRedistributeProjects=Save Settings & Redistribute Projects
ClusterManagementPage.replicas=Project Replicas
ClusterManagementPage.servers=Clustered Servers
ClusterManagementPage.desc=Projects need to be redistributed when cluster members are added/removed. DevGrip does not do this automatically as this is resource intensive, and you may only want to do it after cluster is finalized and stable.
ClusterManagementPage.saveSuccess=Settings saved and project redistribution scheduled
ClusterManagementPage.leadBadage= <span class='badge badge-info badge-sm ml-1'>lead</span>
ClusterSetting.replicaCount.name=Replica Count
ClusterSetting.replicaCount.desc=Number of project replicas, including primary and backups
ClusterSetting.tooManyReplicasCount=No enough servers to store replicas
PerformanceSettingPage.saveSuccess=Performance settings have been saved
PerformanceSetting.cpuTask.name=CPU Intensive Task Concurrency
PerformanceSetting.cpuTask.desc=Specify max number of CPU intensive tasks the server can run concurrently, such as Git repository pull/push, repository index, etc.
PerformanceSetting.lfsSize.name=Max Git LFS File Size (MB)
PerformanceSetting.lfsSize.desc=Specify max git LFS file size in mega bytes
PerformanceSetting.uploadSize.name=Max Upload File Size (MB)
PerformanceSetting.uploadSize.desc=Specify max size of uploaded file in mega bytes via web interface. This applies to file uploaded to repository, markdown content (issue comment etc), and build artifacts
PerformanceSetting.codeSearchItems.name=Max Code Search Entries
PerformanceSetting.codeSearchItems.desc=Maximum number of entries to return when search code in repository
LabelManagementPage.desc=Define labels to be assigned to project, build or pull request. For issues, custom fields can be used which is much more powerful than labels
LabelManagementPage.updateSuccess=Labels have been updated
LabelManagementBean.nameDup=Duplicated label: 
LabelSpec.name=Name
LabelSpec.colorName=Color
AlertSettingPage.updateSuccess=Alert settings have been updated
AlertSetting.notifyUsers.name=Notify Users
AlertSetting.notifyUsers.desc=Select users to send alert email upon events such as database auto-backup failure, cluster node unreachable etc
SubscriptionManagementPage.desc=This installation does not have an active subscription.To access <a href="https://devgrip.net/pricing">all features</a>, an active subscription is required
SubscriptionManagementPage.order=Order Subscription
SubscriptionManagementPage.installKey=Install Subscription Key
SubscriptionManagementPage.extend=Extend Trial Subscription
SubscriptionManagementPage.licensedTo=Licensed To: 
SubscriptionManagementPage.licensedGroup=License Group: 
SubscriptionManagementPage.licensedGroup.desc=<b>NOTE:</b> By using this license, you agree that all staff users shall be added to this group for user-month check
SubscriptionManagementPage.expiration=Expiration Date: 
SubscriptionManagementPage.notFound=not found
SubscriptionManagementPage.requestChange=request to change
SubscriptionManagementPage.remain=Remaining User Months: 
SubscriptionManagementPage.orderMore=order more
SubscriptionManagementPage.orderMoreUserMonths=Order More User Months
SubscriptionManagementPage.requestTrialSubscription=Request Trial Subscription
SubscriptionManagementPage.trial.expireTitle1=This installation has a trial subscription and all functions are available
SubscriptionManagementPage.trial.expireTitle2=This installation has an expired trial subscription and functionality is limited
SubscriptionManagementPage.trial.expireTitle3=** functionality is limited as the trial subscription was expired, order subscription to enable <NAME_EMAIL> if you need to extend your trial **
SubscriptionManagementPage.subscription.expireTitle1=This installation has an expired subscription, and functionality is limited
SubscriptionManagementPage.subscription.expireTitle2=** functionality is limited as there is no remaining user months. Order more to enable **
SubscriptionManagementPage.subscription.expireTitle3=This installation has an active subscription and all functions are available
SubscriptionManagementPage.subscription.msg1=With current number of users (${userCount}) in group '${group}', the subscription will be active until <b>${expDate}</b>
SubscriptionManagementPage.subscription.msg2=With current number of non-guest users ('${userCount}'), the subscription will be active until <b>${expDate}</b>
SubscriptionManagementPage.subscription.installSuccess=Subscription key installed successfully
SubscriptionManagementPage.subscription.deactiveConfirm=This will deactivate current subscription and functionality is limited, do you want to continue?
SubscriptionManagementPage.disabledUserAreExcludeUserMonthCheck=Disabled users are excluded from user-month check
SubscriptionKeyEditBean.name=Install Subscription Key
SubscriptionKeyEditBean.installKey.placeholder=Paste subscription key here
SubscriptionKeyEditBean.installError.1=Subscription key not applicable: this key is intended to activate a trial subscription
SubscriptionKeyEditBean.installError.2=Trial subscription key not applicable for this installation
SubscriptionKeyEditBean.installError.3=Subscription key not applicable: this key is intended to update licensee of an existing subscription
SubscriptionKeyEditBean.installError.expire=This subscription key was expired
SubscriptionKeyEditBean.installError.alreadyUsed=This subscription key was already used
SubscriptionKeyEditBean.installError.invalidKey=Invalid subscription key
SupportRequestPanel.title=Submit Support Request
SupportRequestPanel.contactEmail=Contact Email
SupportRequestPanel.contactName=Contact Name
SupportRequestPanel.notEmpty=Must not be empty
SupportRequestPanel.description.placeholder=Plain text expected
SupportRequestPanel.summary=Summary
SupportRequestPanel.description=Description
SupportRequestPanel.infoToSent=Below information will also be sent
SupportRequestPanel.basic=Basic Info
SupportRequestPanel.pv=Product Version
SupportRequestPanel.ac=Agent Count
SupportRequestPanel.luc=License User Count
SupportRequestPanel.ted=Trial Expiration Date
SupportRequestPanel.rum=Remaining User Months
XSubscriptionManager.alert1=Enterprise features are disabled as trial subscription was expired. Check <a href='/~administration/subscription-management'>subscription management</a> for details
XSubscriptionManager.alert2=Trial subscription will expire in one week. Check <a href='/~administration/subscription-management'>subscription management</a> for details
XSubscriptionManager.alert3=Enterprise features are disabled as subscription was expired. Check <a href='/~administration/subscription-management'>subscription management</a> for details
XSubscriptionManager.alert4=Subscription will expire in one week with current number of non-guest users. Check <a href='/~administration/subscription-management'>subscription management</a> for details
XSubscriptionManager.alert5=Subscription will expire in one month with current number of non-guest users. Check <a href='/~administration/subscription-management'>subscription management</a> for details
SiteInfo.clusterMembers=Cluster Members
SiteInfo.keyUuids=Subscription Key UUIDs
ServerInfo.systemDate=System Date
ServerInfo.mem=Used Memory
ServerInfo.totalMem=Total Memory
ServerInfo.jvm=JVM
ServerInfo.os=OS
ServerInfo.installType=Install Type
ServerInfo.installType.bareMetal=Bare Metal
ServerLogPage.tooManyEntry=Too many log entries, displaying recent ${count}
ServerInformationPage.heapUsage=Heap Memory Usage
ServerInformationPage.total=Total Heap Memory
ServerInformationPage.usedHeap=Used Heap Memory
ServerInformationPage.Jvm=JVM
ServerInformationPage.OS=OS
ServerInformationPage.OsUserName=OS User Name
ServerInformationPage.forceGC=Force Garbage Collection
ServerInformationPage.SystemDate=System Date
DatabaseBackupPage.desc=If enabled, scheduled backup will run on lead server, current lead server is 
DatabaseBackupPage.backupNow=Backup Now
DatabaseBackupPage.backupSettingsUpdated=Backup settings updated
BackupSetting.schedule.name=Backup Schedule
BackupSetting.schedule.desc=specify a cron expression to schedule database auto-backup. The cron expression format is <em>&lt;seconds&gt; &lt;minutes&gt; &lt;hours&gt; &lt;day-of-month&gt; &lt;month&gt; &lt;day-of-week&gt;</em>. For example, <em>0 0 1 * * ?</em> means 1:00am every day. For details of the format, refer to <a href='http://www.quartz-scheduler.org/documentation/quartz-2.3.0/tutorials/crontrigger.html#format' target='_blank'>Quartz tutorial</a>. The backup files will be placed into <em>db-backup</em> folder under DevGrip installation directory. In case multiple servers connect to form a cluster, auto-backup takes place on the <a href='https://docs.devgrip.net/concepts#lead-server' target='_blank'>lead server</a>.
BackupSettingHolder.enableAutoSchedule.name=Enable Auto Backup
ContributedAdministrationSettingPage.saveSuccess=Setting has been saved
ServiceDeskSettingPage.desc1=The service desk feature enables user to create issues by sending emails to DevGrip. Issues can be discussed over email completely, without the need of logging to DevGrip. 
ServiceDeskSettingPage.desc2=System email address defined in mail setting should be used as recipient of such email, and project name may be appended to this address using to indicate where to create issues.For instance, if system email address is specified as <tt><EMAIL></tt>, sending email to <tt><EMAIL></tt> will create issue in <tt>myproject</tt>. If project name is not appended, DevGrip will look up the project using project designation information below
ServiceDeskSettingPage.desc3=<b>NOTE: </b> Service desk only takes effect if 
ServiceDeskSettingPage.desc4=\ is defined and its <tt>check incoming email</tt> option is enabled. Also <a href='https://en.wikipedia.org/wiki/Email_address#Subaddressing' target='_blank'>sub addressing</a> needs to be enabled for the system email address. Check <a href='https://docs.devgrip.net/' target='_blank'>docs</a> for details
ServiceDeskSettingPage.saveSuccess=Service desk settings have been saved
ServiceDeskSettingHolder.enable.name=Enable
ServiceDeskSetting.issueCreate.name=Issue Creation Settings
ServiceDeskSetting.issueCreate.desc=Specify issue creation settings. For a particular sender and project, the first matching entry will take effect. Issue creation will be disallowed if no matching entry found
DefaultProjectSetting.se.name=Applicable Senders
DefaultProjectSetting.se.placeholder=Any sender
DefaultProjectSetting.se.desc=Specify space-separated sender email addresses applicable for this entry. Use '*' or '?' for wildcard match. Prefix with '-' to exclude. Leave empty to match all senders
DefaultProjectSetting.project.name=Default Project
IssueCreationSetting.ap.name=Applicable Senders
IssueCreationSetting.ap.desc=Specify space-separated projects applicable for this entry. Use '*' or '?' for wildcard match. Prefix with '-' to exclude. Leave empty to match all projects
IssueCreationSetting.confidential.name=Confidential
IssueCreationSetting.confidential.desc=Whether created issue should be confidential
IssueCreationSettingEditPanel.title=Issue Creation Setting
IssueCreationSettingListEditPanel.projects=Applicable Projects
SshServerKeyPage.desc=Specify the private key (in PEM format) used by SSH server to establish connections with client
SshServerKeyPage.regen=Regenerate Private Key
SshServerKeyPage.saveSuccess=SSH settings have been saved and SSH server restarted
SshServerKeyPage.saveConfirm=This will restart SSH server. Do you want to continue?
SshServerKeyPage.reKeySuccess=Private key regenerated and SSH server restarted
GpgServerKeyPage.desc=GPG signing key will be used to sign commits generated by System, including pull request merge commits, user commits created via web UI or RESTful api.
GpgServerKeyPage.gen=Generate
GpgServerKeyPage.email=Email Address
GpgServerKeyPage.pk=Public Key
GpgServerKeyPage.keyId=Key ID
GpgServerKeyPage.copy=Copy public key
GpgServerKeyPage.deleteConfirm=Commits generated by System previously will be shown as unverified if this key is deleted. Type <code>yes</code> below if you want to continue.
GpgTrustedKeysPage.desc=Add GPG public keys to be trusted here. Commits signed with trusted keys will be shown as verified. 
GpgTrustedKeysPage.title=Add a GPG Public Key
GpgTrustedKeysPage.addFail=This key or one of its sub key is already added
GpgTrustedKeysPage.subKey=Sub Keys
GpgTrustedKeysPage.deleteSuccess=GPG key deleted
GpgTrustedKeysPage.deleteConfirm=Do you really want to delete this GPG key?
GroovyScriptListPage.canBeUsed=Can Be Used By Jobs
GroovyScriptListPage.jobAuth=Job Authorization
AnyJobWithI=<i>Any job</i>
GroovyScriptListPage.na=<i>N/A</i>
GroovyScriptListPage.deleteConfirm=Do you really want to delete this script?
GroovyScriptEditPanel.title=Groovy Script
GroovyScriptEditPanel.saveFail=This name has already been used by another script
GroovyScript.content=Content
GroovyScript.canUsedByJobs.name=Can Be Used By Jobs
GroovyScript.canUsedByJobs.desc=Whether this script can be used in CI/CD jobs
GroovyScript.anyJob.name=Authorization
AnyJob=Any Jobs
GroovyScript.anyJob.desc=Optionally specify jobs allowed to use this script
Test_Settings=Test Settings
Save_Settings=Save Settings
AutenticatorPage.authTestTitle=Authentication Test
AutenticatorPage.saveSuccess=External authenticator settings saved
AutenticatorPage.testMsg.fullName=Full Name: 
AutenticatorPage.testMsg.email=Email: 
AutenticatorPage.testMsg.groups=Groups: 
AutenticatorPage.testMsg.sshKeys=Number of SSH Keys: 
AutenticatorPage.testMsg.testPassed=Test successful: authentication passed
AutenticatorPage.testMsg.retrieved= with below information retrieved:
AuthenticationToken.username.name=UserName
AuthenticationToken.username.desc=Specify username to authenticate with
AuthenticationToken.password.name=Password
AuthenticationToken.password.desc=Specify password to authenticate with
AuthenticatorBean.noAuth=No external authenticator
Authenticator.defaultGroup.placeholder=No default group
Authenticator.defaultGroup.desc=Optionally add newly authenticated user to specified group if membership information is not retrieved
Authenticator.defaultGroup.name=Default Group
Authenticator.timeout.name=Timeout
Authenticator.timeout.desc=Specify network timeout in seconds when authenticate through this system
LdapAuthenticator.name=Generic LDAP
LdapAuthenticator.ldapUrl.name=LDAP URL
LdapAuthenticator.ldapUrl.desc=Specifies LDAP URL, for example: <i>ldap://localhost</i>, or <i>ldaps://localhost</i>. In case your ldap server is using a self-signed certificate for ldaps connection, you will need to <a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates' target='_blank'>configure DevGrip to trust the certificate</a>
LdapAuthenticator.authRequired.name=Authentication Required
LdapAuthenticator.authRequired.desc=DevGrip needs to search and determine user DN, as well as searching user group information if group retrieval is enabled. Tick this option and specify 'manager' DN and password if these operations needs to be authenticated
LdapAuthenticator.managerDN.name=Manager DN
LdapAuthenticator.managerDN.desc=Specify manager DN to authenticate DevGrip itself to LDAP server
LdapAuthenticator.managerPwd.name=Manager Password
LdapAuthenticator.managerPwd.desc=Specifies password of above manager DN
LdapAuthenticator.usb.name=User Search Bases
LdapAuthenticator.usb.desc=Specifies base nodes for user search. For example: <i>ou=users, dc=example, dc=com</i>
LdapAuthenticator.usb.placeholder=Input user search base. Hit ENTER to add
LdapAuthenticator.usf.desc=This filter is used to determine the LDAP entry for current user. For example: <i>(&(uid={0})(objectclass=person))</i>. In this example, <i>{0}</i> represents login name of current user.
LdapAuthenticator.usf.name=User Search Filter
LdapAuthenticator.ufna.name=User Full Name Attribute
LdapAuthenticator.ufna.desc=Optionally specifies name of the attribute inside the user LDAP entry whose value will be taken as user full name. This field is normally set to <i>displayName</i> according to RFC 2798. If left empty, full name of the user will not be retrieved
LdapAuthenticator.uea.name=User Email Attribute
LdapAuthenticator.uea.desc=Optionally specifies name of the attribute inside the user LDAP entry whose value will be taken as user email. This field is normally set to <i>mail</i> according to RFC 2798
LdapAuthenticator.uska.name=User SSH Key Attribute
LdapAuthenticator.uska.desc=Optionally specify name of the attribute inside the user LDAP entry whose values will be taken as user SSH keys. SSH keys will be managed by LDAP only if this field is set
LdapAuthenticator.gr.name=Group Retrieval
LdapAuthenticator.gr.desc=Specify the strategy to retrieve group membership information. To give appropriate permissions to a LDAP group, a DevGrip group with same name should be defined. Use strategy <tt>Do Not Retrieve Groups</tt> if you want to manage group memberships at DevGrip side
DoNotRetrieveGroups.name=Do Not Retrieve Groups
GetGroupsUsingAttribute.name=Get Groups Using Attribute
GetGroupsUsingAttribute.uga.name=User Groups Attribute
GetGroupsUsingAttribute.uga.desc=Specifies name of the attribute inside the user LDAP entry whose value contains distinguished names of belonging groups. For instance some LDAP servers uses attribute <i>memberOf</i> to list groups
GetGroupsUsingAttribute.gna.name=Group Name Attribute
GetGroupsUsingAttribute.gna.desc=Specifies the attribute containing group name inside the found group LDAP entry. Value of this attribute will be mapped to a DevGrip group. This attribute is normally set to <i>cn</i>
SearchGroupsUsingFilter.name=Search Groups Using Filter
SearchGroupsUsingFilter.gsb.name=Group Search Base
SearchGroupsUsingFilter.gsb.desc=In case user group membership maintained at group side, this property specifies base node for group search. For example: <i>ou=groups, dc=example, dc=com</i>
SearchGroupsUsingFilter.gsf.name=Group Search Filter
SearchGroupsUsingFilter.gsf.desc=In case user group relationship maintained at group side, this filter is used to determine belonging groups of current user. For example: <i>(&(uniqueMember={0})(objectclass=groupOfUniqueNames))</i>. In this example, <i>{0}</i> represents DN of current user
SearchGroupsUsingFilter.gna.name=Group Name Attribute
SearchGroupsUsingFilter.gna.desc=Specifies the attribute containing group name inside the found group LDAP entry. Value of this attribute will be mapped to a DevGrip group. This attribute is normally set to <i>cn</i>
ActiveDirectoryAuthenticator.name=Active Directory
ActiveDirectoryAuthenticator.ldapUrl.name=LDAP URL
ActiveDirectoryAuthenticator.ldapUrl.desc=Specifies LDAP URL of the Active Directory server, for example: <i>ldap://ad-server</i>, or <i>ldaps://ad-server</i>. In case your ldap server is using a self-signed certificate for ldaps connection, you will need to <a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates' target='_blank'>configure DevGrip to trust the certificate</a>
ActiveDirectoryAuthenticator.mdn.name=Manager DN
ActiveDirectoryAuthenticator.mdn.desc=Specify manager DN to authenticate DevGrip itself to Active Directory. The manager DN should be specified in form of <i>&lt;account name&gt;@&lt;domain&gt;</i>, for instance: <i><EMAIL></i>
ActiveDirectoryAuthenticator.usb.name=User Search Bases
ActiveDirectoryAuthenticator.usb.desc=Specify base nodes for user search. For example: cn=Users, dc=example, dc=com
ActiveDirectoryAuthenticator.usb.placeholder=Input user search base. Hit ENTER to add
ActiveDirectoryAuthenticator.gsb.placeholder=Do not retrieve groups
ActiveDirectoryAuthenticator.gsb.name=Group Search Base
ActiveDirectoryAuthenticator.gsb.desc=Optionally specify group search base if you want to retrieve group membership information of the user. For example: <i>cn=Users, dc=example, dc=com</i>. To give appropriate permissions to a Active Directory group, a DevGrip group with same name should be defined. Leave empty to manage group memberships at DevGrip side
SsoConnectorListPage.addTitle=Add SSO Provider
SsoConnectorListPage.colName=Name
SsoConnectorListPage.colCallback=Callback URL
SsoConnectorListPage.deleteConfirm=Do you really want to delete this connector?
SsoConnectorEditPanel.h5=Single Sign On Provider
SsoConnectorEditPanel.saveFail=This name has already been used by another provider
SsoConnector.name.name=Name
SsoConnector.name.desc=Name of the provider will be displayed on login button
SsoConnector.defaultGroup.name=Default Group
SsoConnector.defaultGroup.placeholder=No default group
SsoConnector.defaultGroup.desc=Optionally add newly authenticated user to specified group if membership information is not available
OpenIdConnector.desc=Refer to this <a href='https://docs.devgrip.net/tutorials/security/sso-with-okta' target='_blank'>tutorial</a> for an example setup
OpenIdConnector.name.name=Name
OpenIdConnector.name.desc=Name of the provider will serve two purpose: <ul><li>Display on login button<li>Form the authorization callback url which will be <i>&lt;server url&gt;/~sso/callback/&lt;name&gt;</i></ul>
OpenIdConnector.cdu.name=Configuration Discovery Url
OpenIdConnector.cdu.desc=Specify configuration discovery url of your OpenID provider,for instance: <code>https://openid.example.com/.well-known/openid-configuration</code>. Make sure to use HTTPS protocol as DevGrip relies on TLS encryption to ensure token validity
OpenIdConnector.clientId.name=Client Id
OpenIdConnector.clientId.desc=OpenID client identification will be assigned by your OpenID provider when registering this DevGrip instance as client application
OpenIdConnector.clientSecret.name=Client Secret
OpenIdConnector.clientSecret.desc=OpenID client secret will be generated by your OpenID provider when registering this DevGrip instance as client application
OpenIdConnector.rs.name=Request Scopes
OpenIdConnector.rs.desc=Specify OpenID scopes to request
OpenIdConnector.group=More Settings
OpenIdConnector.gc.name=Groups Claim
OpenIdConnector.gc.desc=Optionally specify the OpenID claim to retrieve groups of authenticated user. Depending on the provider, you may need to request additional scopes above to make this claim available
OpenIdConnector.biu.name=Button Image Url
OpenIdConnector.biu.desc=Specify image on the login button
MicrosoftEntraId.clientId.name=Application (client) ID
MicrosoftEntraId.clientId.desc=Specify application (client) ID of the app registered in Entra ID
MicrosoftEntraId.tenantId.name=Directory (tenant) ID
MicrosoftEntraId.tenantId.desc=Specify directory (tenant) ID of the app registered in Entra ID
MicrosoftEntraId.clientSecret.name=Client Secret
MicrosoftEntraId.clientSecret.desc=Specify client secret of the app registered in Entra ID
MicrosoftEntraId.groups.name=Retrieve Groups
MicrosoftEntraId.groups.desc=Whether to retrieve groups of login user. Make sure to add groups claim via token configuration of the app registered in Entra ID if this option is enabled. The groups claim should return group id (the default option) via various token types in this case
ProjectListPanel.queryPlaceholder=Query/order projects
ProjectListPanel.toggleMoreInfo=Toggle More Info
ProjectListPanel.snf=Storage not found
ProjectListPanel.childProjectsCount=${count} child projects
Qns=Query not submitted
CanNotSaveMq=Can not save malformed query
ProjectListPanel.moveError.noPrivilage=Project manage privilege required to move '${project}'
ProjectListPanel.moveError.underItself=Can not move project '${project}' to be under itself or its descendants
ProjectListPanel.moveError.nameAlreadyUsed=A child project with name '${child}' already exists under '${project}'
ProjectListPanel.moveSuccess=Projects moved
ProjectListPanel.moveTitle=Please select projects to move
ProjectListPanel.moveConfirm=Type <code>yes</code> below to move selected projects to be under '${project}'
ProjectListPanel.selectAsRoot.noPrivilage=Project manage privilege required to modify '${project}'
ProjectListPanel.selectAsRoot.nameAlreadyUsed=A root project with name '${project}' already exists
ProjectListPanel.selectAsRootSuccess=Projects modified
ProjectListPanel.selectAsRootConfirm=Type <code>yes</code> below to set selected as root projects
ProjectListPanel.selectAsRootTitle=Please select projects to modify
ProjectListPanel.deleteSelected.noPrivilage=Project manage privilege required to delete '${project}'
ProjectListPanel.deleteSelectedSuccess=Projects deleted
ProjectListPanel.deleteSelectedConfirm=Type <code>yes</code> below to delete selected projects
ProjectListPanel.deleteSelectedTitle=Please select projects to delete
ProjectListPanel.moveAllQueriedError.noPrivilage=Project manage privilege required to move '${project}'
ProjectListPanel.moveAllQueriedError.underItself=Can not move project '${project}' to be under itself or its descendants
ProjectListPanel.moveAllQueriedError.nameAlreadyUsed=A child project with name '${child}' already exists under '${project}'
ProjectListPanel.moveAllQueriedSuccess=Projects moved
ProjectListPanel.moveAllQueriedConfirm=Type <code>yes</code> below to move all queried projects to be under '${project}'
ProjectListPanel.moveAllQueriedTitle=No projects to move
ProjectListPanel.selectAllQueriedAsRoot.noPrivilage=Project manage privilege required to modify '${project}'
ProjectListPanel.selectAllQueriedAsRoot.nameAlreadyUsed=A root project with name '${project}' already exists
ProjectListPanel.selectAllQueriedAsRootSuccess=Projects modified
ProjectListPanel.selectAllQueriedAsRootConfirm=Type <code>yes</code> below to set all queried as root projects
ProjectListPanel.selectAllQueriedAsRootTitle=No projects to modified
ProjectListPanel.deleteAllQueried.noPrivilage=Project manage privilege required to delete '${project}'
ProjectListPanel.deleteAllQueriedSuccess=Projects deleted
ProjectListPanel.deleteAllQueriedConfirm=Type <code>yes</code> below to delete all queried projects
ProjectListPanel.deleteAllQueriedTitle=No projects to delete
ProjectListPanel.helpMsg=Permission will be checked upon actual operation
ProjectListPanel.import.nameLable=Import From ${name}
ProjectListPanel.AddChildTitle=Add child project
ProjectListPanel.foundCount=found ${count} project
ProjectListPanel.hiddenMsg=Some projects might be hidden due to permission policy
StatsPanel.filesCount=${filesCount} files
StatsPanel.commitsCount=${commitsCount} commits
StatsPanel.branchesCount=${branchesCount} branches
StatsPanel.tagsCount=${tagsCount} tags
StatsPanel.issuesCount=${issuesCount} issues
StatsPanel.buildsCount=${buildsCount} builds
StatsPanel.packsCount=${packsCount} packs
StatsPanel.pullRequestsCount=${pullRequestsCount} pull requests
ConfirmDeleteModal.deleteSuccess=Project '${project}' deleted
ConfirmDeleteModal.deleteConfirm=Everything inside this project and all child projects will be deleted and can not be recovered,please type project path <code>${project}</code> below to confirm deletion.
ProjectSelector.search=Search project
ProjectSelector.noProject=No projects
ProjectInfoPanel.forkFrom=, forked from 
ProjectInfoPanel.forkNow=fork now
ProjectInfoPanel.serviceDeskDesc=Issues can be created in this project by sending email to this address
ProjectInfoPanel.replicasSync=request to sync
ProjectInfoPanel.forksCountTitle=${count} forks
ProjectInfoPanel.keyTitle=, key:${key}
ProjectInfoPanel.serverinfo.active=${server} <span class='badge badge-sm badge-info ml-1'>active</span>
ProjectInfoPanel.serverinfo.upToDate=${server} <span class='badge badge-sm badge-success ml-1'>up to date</span>
ProjectInfoPanel.serverinfo.outdate=${server} <span class='badge badge-sm badge-warning ml-1'>outdated</span>
ProjectInfoPanel.syncError=Error requestig to sync replica of project with id
ProjectInfoPanel.syncSuccess=Sync requested. Please check status after a while
GitProtocolPanel.desc1=No SSH keys configured in your account. You may 
GitProtocolPanel.desc2=add a key
GitProtocolPanel.desc3=or switch to 
GitProtocolPanel.sshCloneUrl=SSH Clone URL
GitProtocolPanel.httpCloneUrl=HTTP(S) Clone URL
GitProtocolPanel.switchToSsh=Switch to SSH
GitProtocolPanel.switchToHttp=Switch to HTTP(S)
GitProtocolPanel.fingerprint=Server fingerprint: ${fingerprint}
ForkOptionPanel.forkProject=Fork Project
ForkOptionPanel.forkFail.keyAlreadyUsed=This key has already been used by another project
ForkOptionPanel.forkFail.nameAlreadyUsed=This name has already been used by another project
ForkOptionPanel.forkSuccess=Project forked
ProjectChoice.choosePro.placeholder=Choose a project...
NoProjectStoragePage.noStoreFound=Project storage not found 
NoProjectStoragePage.psd=Project storage directory
NoProjectStoragePage.notFound=can not be found on any servers 
NoProjectStoragePage.noStore=No Storage
NewProjectPage.createSuccess=New project created
NewProjectPage.topbarTitle1=Create Child Project
NewProjectPage.topbarTitle2=Create Project
Project.name=Name
Project.key.name=Key
Project.key.desc=Optionally define a unique key for the project with two or more upper case letters. This key can be used to reference issues, builds, and pull requests with a stable and short form <code>&lt;project key&gt;-&lt;number&gt;</code> instead of <code>&lt;project path&gt;#&lt;number&gt;</code>
Project.description.name=Description
Project.codeManagement.name=Code Management
Project.codeManagement.desc=Whether to enable code management for the project
Project.issueManagement.name=Issue Management
Project.issueManagement.desc=Whether to enable issue management for the project
Project.timeTracking.name=Time Tracking
Project.timeTracking.desc1=<b class='text-warning'>NOTE: </b><a href='https://docs.devgrip.net/tutorials/issue/time-tracking' target='_blank'>Time tracking</a> is an enterprise feature. <a href='https://devgrip.net/pricing' target='_blank'>Try free</a> for 30 days
Project.timeTracking.desc2=Enable <a href='https://docs.devgrip.net/tutorials/issue/time-tracking' target='_blank'>time tracking</a> for this project to track progress and generate timesheets
Project.packManagement.name=Package Management
Project.packManagement.desc=Enable <a href='https://docs.devgrip.net/tutorials/package/working-with-packages' target='_blank'>package management</a> for this project
DefaultRolesBean.roleName.name=Default Role
DefaultRolesBean.roleName.rootPlaceholder=No default role
DefaultRolesBean.roleName.desc=Default roles affects default permissions granted to everyone in the system. The actual default permissions will be <b class='text-warning'>all permissions</b> contained in default roles of this project and all its parent projects
ParentBean.parentPath.name=Parent Project
ParentBean.parentPath.placeholder=No parent
ParentBean.parentPath.desc=Settings and permissions of parent project will be inherited by this project
GeneralProjectSettingPage.updateFail.noParent=Parent project not found
GeneralProjectSettingPage.updateFail.canNotUse=Can not use current or descendant project as parent
GeneralProjectSettingPage.updateFail.notAuthMove=Not authorized to move project under this parent
GeneralProjectSettingPage.updateFail.notAuthSetAsRoot=Not authorized to set as root project
GeneralProjectSettingPage.updateFail.nameAlreadyUsed=This name has already been used by another project
GeneralProjectSettingPage.updateFail.keyAlreadyUsed=This key has already been used by another project
GeneralProjectSettingPage.updateSuccess=General settings updated
GeneralProjectSettingPage.deleteSuccess=Requested to delete project
GeneralProjectSettingPage.deleteConfirm=Everything inside this project and all child projects will be deleted and can not be recovered, please type project path <code>${project}</code> below to confirm deletion.
NoCommitsPanel.h3=Project does not have any code yet
NoCommitsPanel.youMay=You can use the following methods to initialize the project
NoCommitsPanel.addFiles=adding files
NoCommitsPanel.settingUp=setting up build spec
NoCommitsPanel.pushing=pushing an existing repository
NoCommitsPanel.run=Run below commands from within your git repository:
NoCommitsPanel.runNoGit=Run below commands to add unversioned code to a repository:
NoCommitsPanel.gitinit=git init
NoCommitsPanel.gitadd=git add .
NoCommitsPanel.gitinitialcommit=git commit -m "initial commit"
NoCommitsPanel.gitremote=git remote add origin
NoCommitsPanel.gitpush=git push -u origin main
MailServicePage.title=Mail Service Test
MailServicePage.saveSuccess=Mail service settings saved
MailServicePage.testMail.htmlBody=[Test] Test Email From DevGrip
MailServicePage.testMail.textBody=This is a test email from DevGrip
MailServicePage.testMail.success=Great, your mail service configuration is working
MailServicePage.testMail.sent=Test mail has been sent to ${email}, please check your mailbox
MailServicePage.testMail.noPrimary=Primary email address of your account is not specified yet
TaskButton.pleaseWait=please wait...
MailServicePage.waitTestMail=Waiting for test mail to come back...
MailServicePage.receivedTestMail=Received test mail
MailServicePage.sendingTo=Sending test mail...
MailServiceBean.noMailService=No mail service
SmtpImapMailService.smtpHost.name=SMTP Host
SmtpImapMailService.smtpHost.desc=Specify your email smpt host, e.g: smtp.example.com
SmtpImapMailService.ssl.name=SSL Setting
SmtpImapMailService.ssl.desc=<ul><li>Implicit TLS establishes an encrypted connection on port 465 from the start, meaning the entire communication occurs over a secure channel.</li><li>Explicit SSL (StartTLS) begins with an unencrypted connection (often in plain text) on port 587, and then the client and server negotiate to upgrade to TLS encryption. </li><li>Regardless of whether implicit TLS or Explicit SSL (StartTLS) is used, the final connection will be encrypted with TLS, so there's no real difference in security.</li></ul>
SmtpImapMailService.smtpUser.name=SMTP User
SmtpImapMailService.smtpPwd.name=SMTP Password
MailService.systemUserAddress.name=System Email Address
MailService.systemUserAddress.desc=This address will be used as sender address of various email notifications. User can also reply to this address to post issue or pull request comments via email if <code>Check Incoming Email</code> option is enabled below
MailService.checkIncoming.name=Check Incoming Email
MailService.checkIncoming.desc=Enable this to process issue or pull request comments posted via email. <b class='text-danger'>NOTE:</b> <a href='https://en.wikipedia.org/wiki/Email_address#Subaddressing' target='_blank'>Sub addressing</a>, it is also known as plus addressing, needs to be enabled for system email address above, as DevGrip uses it to track issue and pull request contexts
MailService.timeout.name=Timeout
MailService.timeout.desc=Specify timeout in seconds when communicating with mail server
InboxPollSetting.imapHost.name=IMAP Host
InboxPollSetting.imapUser.name=IMAP User
InboxPollSetting.imapUser.desc=Specify IMAP username.<b class='text-danger'>NOTE: </b> This account should be able to receive emails sent to system email address specified above
InboxPollSetting.imapPwd.name=IMAP Password
InboxPollSetting.pollInterval.name=Poll Interval
InboxPollSetting.pollInterval.desc=Specify incoming email poll interval in seconds
SmtpImapSSL.noSSL.name=No SSL
SmtpImapSSL.port=Port
SmtpImapSSL.bcc.name=Bypass Certificate Check
SmtpImapSSL.bcc.desc=In case SMTP host certificate is self-signed or its CA root is not accepted, you may tell DevGrip to bypass certificate check. <b class='text-danger'>WARNING: </b> In an untrusted network, this may lead to man-in-the-middle attack, and you should <a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates#trust-self-signed-certificates-on-server' target='_blank'>import the certificate into DevGrip</a> instead
SmtpImapSSL.explicit.name=Explicit SSL (StartTLS)
SmtpImapSSL.implicit.name=implicit SSL
Office365MailService.upn.name=User Principal Name
Office365MailService.upn.desc=Principal name of the account to login into Office 365 mail server to send/receive emails. Make sure this account owns the registered application indicated by application id above
MailService.rt.name=Refresh Token
Office365MailService.rt.desc=Long-live refresh token of above account which will be used to generate access token to access Office 365 mail server. TIPS: you may use the button at right side of this field to login to your Office 365 account and generate refresh token. Note that whenever tenant id, client id, client secret, or user principal name is changed, refresh token should be re-generated
Office365MailService.checkIncoming.desc=Enable this to process issue or pull request comments posted via email.<b class='text-danger'>NOTE:</b> <a href='https://learn.microsoft.com/en-us/exchange/recipients-in-exchange-online/plus-addressing-in-exchange-online' target='_blank'>Sub addressing</a>, it is also known as plus addressing, needs to be enabled for system email address above, as DevGrip uses it to track issue and pull request contexts
RefreshTokenPropertyEditor.login=Login and generate refresh token
GmailMailService.clientId.name=Client Id
GmailMailService.clientId.desc=Client ID of this DevGrip instance registered in Google cloud
GmailMailService.clientSecret.name=Client Secret
GmailMailService.clientSecret.desc=Client secret of this DevGrip instance registered in Google cloud
GmailMailService.account.name=Account Name
GmailMailService.account.desc=Specify account name to login to Gmail to send/receive email
GmailMailService.rt.desc=Long-live refresh token of above account which will be used to generate access token to access Gmail. TIPS: you may use the button at right side of this field to login to your Office 365 account and generate refresh token. Note that whenever tenant id, client id, client secret, or user principal name is changed, refresh token should be re-generated
GmailMailService.checkIncoming.desc=Enable this to process issue or pull request comments posted via email
AgentsListPage.title=Agents can be used to execute jobs on remote machines. Once started it will update itself from server automatically when necessary 
AgentEditBean.editError=Duplicate attribute (%s)
AgentEditBean.attr=Attributes
AgentEditBean.attr.desc=The attributes of this agent, e.g: env=prod or env = test, This can be used for query agents
AgentDetailPage.colOverview=Overview
AgentDetailPage.colBuilds=Builds
AgentDetailPage.colLog=Log
AgentLogPage.tooManyLogs=Too many log entries, displaying recent ${count}
AgentLogPage.offlineAgent=Log not available for offline agent
AgentOverviewPage.restartSended=Restart command issued
AgentOverviewPage.restartConfirm=Do you really want to restart this agent?
AgentOverviewPage.removed=Agent removed
AgentOverviewPage.removeConfirm=Do you really want to remove this agent?
AgentOverviewPage.resume=Resume
AgentOverviewPage.pause=Pause
AgentOverviewPage.atr=Access token regenerated, make sure to update the token at agent side
AgentOverviewPage.attrSaved=Attributes saved
AgentOverviewPage.restartLabel=Restart
AgentOverviewPage.removeLabel=Remove
AgentOverviewPage.ip=IP Address
AgentOverviewPage.os=OS
AgentOverviewPage.arch=Arch
AgentOverviewPage.statusLabel=Status
AgentOverviewPage.atLabel=Access Token: 
AgentOverviewPage.regen=Regenerate
AgentOverviewPage.attrLabel=Attributes (can only be edited when agent is online)
AgentOverviewPage.noAttrLabel=No attributes defined (can only be edited when agent is online)
AgentListPanel.query.placeholder=Query/order agents
AgentListPanel.cpuLabel.desc=CPU capability in millis. This is normally (CPU cores)*1000
AgentListPanel.memLabel.desc=Physical memory in megabytes
AgentListPanel.pausedSuccess=Paused selected agents
AgentListPanel.pauseTitle=Please select agents to pause
AgentListPanel.resumedSuccess=Resumed selected agents
AgentListPanel.resumeTitle=Please select agents to resume
AgentListPanel.restartSended=Restart command issued to selected agents
AgentListPanel.restartConfirm=Type <code>yes</code> below to restart selected agents
AgentListPanel.restartTitle=Please select agents to restart
AgentListPanel.removeConfirm=Type <code>yes</code> below to confirm to remove selected agents
AgentListPanel.removeTitle=Please select agents to remove
AgentListPanel.pausedAllQueriedSuccess=Paused all queried agents
AgentListPanel.pauseAllQueriedConfirm=Type <code>yes</code> below to pause all queried agents
AgentListPanel.pauseAllQueriedTitle=No agents to pause
AgentListPanel.resumedAllQueriedSuccess=Resumed all queried agents
AgentListPanel.resumeAllQueriedConfirm=Type <code>yes</code> below to resume all queried agents
AgentListPanel.resumeAllQueriedTitle=No agents to resume
AgentListPanel.restartAllQueriedSended=Restart command issued to all queried agents
AgentListPanel.restartAllQueriedConfirm=Type <code>yes</code> below to restart selected agents
AgentListPanel.restartAllQueriedTitle=No agents to restart
AgentListPanel.removedAllQueriedConfirm=Remove all queried agents. Type <code>yes</code> below to confirm
AgentListPanel.removeAllQueriedTitle=No agents to remove
AgentListPanel.foundCount=found ${count} agents
TokenListPanel.h6=Available Agent Tokens
TokenListPanel.desc=Agent tokens are used to authorize agents. It should be configured via environment variable <tt>agentToken</tt> if agent runs as docker container, or property <tt>agentToken</tt> in file <tt>&lt;agent dir&gt;/conf/agent.properties</tt> if agent runs on bare metal/virtual machine.A token will be in-use and removed from this list if agent using it connects to server
TokenListPanel.gen=Generate New
TokenListPanel.deleteAllConfirm=Do you really want to delete unused tokens?
AddAgentPanel.h6=Connect New Agent
AddAgentPanel.showCmd=Show Command
AddAgentPanel.showCmdTips=a new agent token will be generated each time this button is pressed
AddAgentPanel.installOnBmTips=Follow below steps to install agent on remote machine (supports Linux/Windows/Mac OS X/FreeBSD):
AddAgentPanel.bmTips1=Make sure <a href="https://openjdk.java.net" target="_blank">Java 11 or higher</a> is installed
AddAgentPanel.bmTips2=Make sure docker engine is installed and docker command line is available in system path
AddAgentPanel.bmTips3=Make sure current user has permission to run docker containers
AddAgentPanel.bmTips4=Make sure git version 2.11.1 or higher is installed and available in system path
AddAgentPanel.bmTips5=Make sure git-lfs is installed and available in system path if you want to retrieve LFS files
AddAgentPanel.bmTips6=. A new agent token will be included in the package
AddAgentPanel.bmTips7=Extract the package into a folder. <b class="text-danger">Warning:</b> On Mac OS X, do not extract to Mac managed folders such as Downloads, Desktop, Documents; otherwise you may encounter permission issues starting agent
AddAgentPanel.bmTips8=Change property <code>serverUrl</code> in file <code>conf/agent.properties</code> if necessary. The default value is taken from server url specified in <i>Administration / System Setting</i> 
AddAgentPanel.bmTips9=From extracted folder, run <code>bin/agent.bat</code> as administrator on Windows or <code>bin/agent.sh start</code> on other OS
AddAgentPanel.agentTips=<li>Agent is designed to be maintenance free. Once connected to server, it will be updated automatically upon server upgrade</li><li>Check <a href="https://docs.devgrip.net/administration-guide/agent-management" target="_blank">agent management</a> for details, including instructions on how to run agent as service</li>
AddAgentPanel.dockerAgentTips=<li>Environment variable <code>serverUrl</code> in above command is taken from server url specified in <i>Administration / System Setting</i>. Change it if necessary</li>
AddAgentPanel.installOnDockerTips=Start agent on remote Linux machine by running below command: 
AddAgentPanel.runOnDocker=Run via Docker Container
AddAgentPanel.runOnBm=Run on Bare Metal/Virtual Machine
AbstractTemplatePage.saveTpl=Save Template
AbstractTemplatePage.saveTplSuccess=Template saved
AbstractTemplatePage.useDefaultConfirm=Do you really want to use default template?
AbstractTemplatePage.msgWhen=. When evaluating this template, below variables will be available:<ul class='mb-0'>
AbstractTemplatePage.htmlVersion=true for html version, false for text version
AbstractNotificationTemplatePage.event=event object triggering the notification
AbstractNotificationTemplatePage.eventSummary=a string representing summary of the event
AbstractNotificationTemplatePage.eventBody=a string representing body of the event. May be <code>null</code>
AbstractNotificationTemplatePage.eventUrl=a string representing event detail url
AbstractNotificationTemplatePage.replyable=a boolean indiciating whether topic comment can be created directly by replying the email
AbstractNotificationTemplatePage.unsubscribable=an object holding unsubscribe information. A <code>null</code> value means that the notification can not be unsubscribed
AlertTemplatePage.helpText=A ${groovyLink} used as body of system alert email
AlertTemplatePage.alert=alert to display
AlertTemplatePage.serverUrl=root url of DevGrip server
AlertTemplatePage.topbarTitle=System Alert Template
BuildNotificationTemplatePage.helpText=A ${groovyLink} used as body of build notification email
BuildNotificationTemplatePage.build=represents the build object to be notified
BuildNotificationTemplatePage.topbarTitle=Build Notification Template
CommitNotificationTemplatePage.helpText=A ${groovyLink} used as body of commit notification email
CommitNotificationTemplatePage.commit=represents the commit object to be notified
CommitNotificationTemplatePage.topbarTitle=Commit Notification Template
EmailVerificationTemplatePage.helpText=A ${groovyLink} used as body of address verification email
EmailVerificationTemplatePage.serverUrl=root url of DevGrip server
EmailVerificationTemplatePage.user=user to verify email for
EmailVerificationTemplatePage.emailAddress=Email address to verify
EmailVerificationTemplatePage.verificationUrl=url following which to verify email address
EmailVerificationTemplatePage.topbarTitle=Email Verification Template
IssueNotificationTemplatePage.helpText=A ${groovyLink} used as body of various issue notification emails
IssueNotificationTemplatePage.issue=represents the issue object to be notified
IssueNotificationTemplatePage.topbarTitle=Issue Notification Template
IssueNotificationUnsubscribedTemplatePage.helpText=A ${groovyLink}  used as body of feedback email when unsubscribed from issue notification
IssueNotificationUnsubscribedTemplatePage.issue=represents the unsubscribed issue
IssueNotificationUnsubscribedTemplatePage.topbarTitle=Issue Notification Template
PackNotificationTemplatePage.helpText=A ${groovyLink} used as body of package notification email
PackNotificationTemplatePage.pack=represents the package object to be notified
PackNotificationTemplatePage.topbarTitle=Package Notification Template
PasswordResetTemplatePage.helpText=A ${groovyLink} used as body of password reset email
PasswordResetTemplatePage.user=user to reset password for
PasswordResetTemplatePage.passwordResetUrl=url to reset password
PasswordResetTemplatePage.topbarTitle=Password Reset Template
PullRequestNotificationTemplatePage.helpText=A ${groovyLink} used as body of various pull request notification emails
PullRequestNotificationTemplatePage.pullRequest=represents the pull request object to be notified
PullRequestNotificationTemplatePage.topbarTitle=Pull Request Notification Template
PullRequestNotificationUnsubscribedTemplatePage.helpText=A ${groovyLink} used as body of feedback email when unsubscribed from pull request notification
PullRequestNotificationUnsubscribedTemplatePage.pullRequest=represents the unsubscribed pull request
PullRequestNotificationUnsubscribedTemplatePage.topbarTitle=Pull Request Notification Unsubscribed Template
ServiceDeskIssueOpenedTemplatePage.helpText=A ${groovyLink} used as body of feedback email when issue is opened via service desk
ServiceDeskIssueOpenedTemplatePage.issue=represents the issue being opened via service desk
ServiceDeskIssueOpenedTemplatePage.topbarTitle=Service Desk Issue Opened Template
ServiceDeskIssueOpenFailedTemplatePage.helpText=A ${groovyLink} used as body of feedback email when failed to open issue via service desk
ServiceDeskIssueOpenFailedTemplatePage.exception=represents the exception encountered when open issue via service desk
ServiceDeskIssueOpenFailedTemplatePage.topbarTitle=Service Desk Issue Open Failed Template
StopwatchOverdueTemplatePage.helpText=A ${groovyLink} used as body of issue stopwatch overdue notification email
StopwatchOverdueTemplatePage.stopwatch=Stopwatch overdue
StopwatchOverdueTemplatePage.topbarTitle=Issue Stopwatch Overdue Notification Template
UserInvitationTemplatePage.helpText=A ${groovyLink} used as body of user invitation email
UserInvitationTemplatePage.setupAccountUrl=the url to set up user account
UserInvitationTemplatePage.topbarTitle=User Invitation Template
JobExecutorBean.type.name=Type
JobExecutor.spe.name=Enable Site Publish
JobExecutor.spe.desc=Enable this to allow to run site publish step. DevGrip will serve project site files as is. To avoid XSS attack, make sure this executor can only be used by trusted jobs
JobExecutor.hrpe.name=Enable Html Report Publish
JobExecutor.hrpe.desc=Enable this to allow to run html report publish step. To avoid XSS attach, make sure this executor can only be used by trusted jobs
JobExecutor.jr.name=Applicable Jobs
JobExecutor.jr.desc=Optionally specify applicable jobs of this executor
ServerDockerExecutor.name=Server Docker Executor
ServerDockerExecutor.desc=This executor runs build jobs as docker containers on DevGrip server
JobExecutor.concurrency.name=Concurrency
JobExecutor.concurrency.placeholder=Number of CPU Cores
JobExecutor.concurrency.desc=Specify max number of jobs this executor can run concurrently. Leave empty to set as CPU cores
ServerDockerExecutor.rl.name=Registry Logins
ServerDockerExecutor.rl.desc=Specify registry logins if necessary. For built-in registry, use <code>@server_url@</code> for registry url, <code>@job_token@</code> for username, and access token for password
ServerDockerExecutor.mds.name=Mount Docker Sock
ServerDockerExecutor.mds.desc=Whether to mount docker sock into job container to support docker operations in job commands<br><b class='text-danger'>WARNING</b>: Malicious jobs can take control of whole DevGrip by operating the mounted docker sock. Make sure this executor can only be used by trusted jobs if this option is enabled
ServerDockerExecutor.alwasyPull.name=Always Pull Image
ServerDockerExecutor.alwasyPull.desc=Whether to always pull image when run container or build images. This option should be enabled to avoid images being replaced by malicious jobs running on same machine or same node
ServerDockerExecutor.concurrency.desc=Specify max number of jobs/services this executor can run concurrently. Leave empty to set as CPU cores
ServerDockerExecutor.resouceLimit.placeholder=No Limit
ServerDockerExecutor.cpuLimit.name=CPU Limit
ServerDockerExecutor.cpuLimit.desc=Optionally specify cpu limit of each job/service using this executor. This will be used as option <a href='https://docs.docker.com/config/containers/resource_constraints/#cpu' target='_blank'>--cpus</a> of relevant containers
ServerDockerExecutor.memLimit.name=Memory Limit
ServerDockerExecutor.memLimit.desc=Optionally specify memory limit of each job/service using this executor. This will be used as option <a href='https://docs.docker.com/config/containers/resource_constraints/#memory' target='_blank'>--memory</a> of relevant containers
ServerDockerExecutor.dsp.name=Docker Sock Path
ServerDockerExecutor.dsp.placeholder=Default
ServerDockerExecutor.dsp.desc=Optionally specify docker sock to use. Defaults to /var/run/docker.sock on Linux, and //./pipe/docker_engine on Windows
ServerDockerExecutor.bxb.name=Buildx Builder
ServerDockerExecutor.bxb.desc=Specify dockerx builder used to build docker image. DevGrip will create the builder automatically if it does not exist. Check <a href='https://docs.devgrip.net/tutorials/cicd/insecure-docker-registry' target='_blank'>this tutorial</a> on how to customize the builder for instance to allow publishing to insecure registries
ServerDockerExecutor.ro.name=Run Options
ServerDockerExecutor.ro.desc=Optionally specify docker options to run container. Multiple options should be separated by space, and single option containing spaces should be quoted
ServerDockerExecutor.networkOptions.name=Network Options
ServerDockerExecutor.networkOptions.desc=Optionally specify docker options to create network. Multiple options should be separated by space, and single option containing spaces should be quoted
ServerDockerExecutor.de.name=Docker Executable
ServerDockerExecutor.de.placeholder=Use Default
ServerDockerExecutor.de.desc=Optionally specify docker executable, for instance <i>/usr/local/bin/docker</i>. Leave empty to use docker executable in PATH
JobExecutor.group.ps=Privilege Settings
JobExecutor.group.ms=More Settings
JobExecutor.group.rs=Resouce Settings
ServerDockerExecutor.testData.name=Specify a Docker Image to Test Against
ServerDockerExecutor.dockerimage.placeholder=Docker Image
ServerDockerExecutor.validate.err1=Duplicate login entry for registry
ServerDockerExecutor.validate.err2=Duplicate login entry for official registry
ServerShellExecutor.name=Server Shell Executor
ServerShellExecutor.desc=This executor runs build jobs with DevGrip server's shell facility.<br><b class='text-danger'>WARNING</b>: Jobs running with this executor has same permission as DevGrip server process. Make sure it can only be used by trusted jobs
ServerShellExecutor.testData.name=Specify Shell/Batch Commands to Run
ServerShellExecutor.testData.commands.placeholder=Commands
RemoteDockerExecutor.name=Remote Docker Executor
RemoteDockerExecutor.desc=This executor runs build jobs as docker containers on remote machines via <a href='/~administration/agents' target='_blank'>agents</a>
RemoteExecutor.agentQuery.name=Agent Selector
RemoteExecutor.agentQuery.placeholder=Any agent
RemoteExecutor.agentQuery.desc=Specify agents applicable for this executor
RemoteDockerExecutor.concurrency.desc=Specify max number of jobs/services this executor can run concurrently on each matched agent. Leave empty to set as agent CPU cores
RemoteShellExecutor.name=Remote Shell Executor
RemoteShellExecutor.desc=This executor runs build jobs with remote machine's shell facility via <a href='/~administration/agents' target='_blank'>agents</a><br><b class='text-danger'>WARNING</b>: Jobs running with this executor has same permission as corresponding agent process. Make sure it can only be used by trusted jobs
RemoteShellExecutor.concurrency.desc=Specify max number of jobs this executor can run concurrently on each matched agent. Leave empty to set as agent CPU cores
K8sExecutor.name=Kubernetes Executor
K8sExecutor.desc=This executor runs build jobs as pods in a kubernetes cluster. No agents are required. <b class='text-danger'>Note:</b> Make sure server url is specified correctly in system settings as job pods need to access it to download source and artifacts
K8sExecutor.bpv.name=Build with Persistent Volume
K8sExecutor.bpv.desc=Enable this to place intermediate files required by job execution on dynamically allocated persistent volume instead of emptyDir
K8sExecutor.sc.name=Build Volume Storage Class
K8sExecutor.sc.placeholder=Use default storage class
K8sExecutor.sc.desc=Optionally specify a storage class to allocate build volume dynamically. Leave empty to use default storage class.<b class='text-warning'>NOTE:</b> Reclaim policy of the storage class should be set to <code>Delete</code>, as the volume is only used to hold temporary build files
K8sExecutor.ss.name=Build Volume Storage Size
K8sExecutor.ss.desc=Specify storage size to request for the build volume. The size should conform to <a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/#setting-requests-and-limits-for-local-ephemeral-storage' target='_blank'>Kubernetes resource capacity format</a>, for instance <i>10Gi</i>
K8sExecutor.cpuReq.name=CPU Request
K8sExecutor.cpuReq.desc=Specify cpu request for each job/service using this executor. Check <a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes resource management</a> for details
K8sExecutor.memReq.name=Memory Request
K8sExecutor.memReq.desc=Specify memory request for each job/service using this executor. Check <a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes resource management</a> for details
K8sExecutor.cpuLimit.name=CPU Limit
K8sExecutor.cpuLimit.desc=Optionally specify cpu limit for each job/service using this executor. Check <a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes resource management</a> for details
K8sExecutor.memLimit.name=Memory Limit
K8sExecutor.memLimit.desc=Optionally specify memory limit for each job/service using this executor. Check <a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes resource management</a> for details
K8sExecutor.ns.name=Node selector
K8sExecutor.ns.desc=Optionally specify node selector of the job pods
K8sExecutor.cr.name=Cluster Role
K8sExecutor.cr.desc=Optionally specify cluster role the job pods service account binding to. This is necessary if you want to do things such as running other Kubernetes pods in job command
K8sExecutor.sl.name=Service Locators
K8sExecutor.sl.desc=Optionally specify where to run service pods specified in job. The first matching locator will be used. If no any locators are found, node selector of the executor will be used
K8sExecutor.kcf.name=Kubectl Config File
K8sExecutor.kcf.desc=Specify absolute path to the config file used by kubectl to access the cluster. Leave empty to have kubectl determining cluster access information automatically
K8sExecutor.ptk.name=Path to kubectl
K8sExecutor.ptk.desc=Specify absolute path to the kubectl utility, for instance: <i>/usr/bin/kubectl</i>. If left empty, DevGrip will try to find the utility from system path
ServiceLocator.an.name=Applicable Names
ServiceLocator.an.placeholder=All
ServiceLocator.an.desc=Optionally specify space-separated service names applicable for this locator. Use '*' or '?' for wildcard match. Prefix with '-' to exclude. Leave empty to match all
ServiceLocator.ai.name=Applicable Images
ServiceLocator.ai.placeholder=All
ServiceLocator.ai.desc=Optionally specify space-separated service images applicable for this locator. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all
ServiceLocator.nl.name=Node Selector
ServiceLocator.nl.desc=Specify node selector of this locator
NodeSelectorEntry.ln.name=Label Name
NodeSelectorEntry.lv.name=Label Value
RegistryLogin.name=Registry Logins
RegistryLogin.ru.name=Registry Url
RegistryLogin.ru.placeholder=Docker hub
RegistryLogin.ru.desc=Specify registry url. Leave empty for official registry
RegistryLogin.un.name=User Name
RegistryLogin.un.desc=Specify username of specified registry
RegistryLogin.pwd.name=Password
RegistryLogin.pwd.desc=Specify password or access token of specified registry
RegistryLogin.pwdSecret.name=Password Secret
RegistryLogin.pwdSecret.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as password or access token of the registry
JobExecutor.log.exeJob=Executing job
JobExecutor.log.exeWith=Executing job with executor
JobExecutor.log.cpd=Copying job dependencies...
JobExecutor.log.pra=Pending resource allocation...
JobExecutor.log.cewc1=Command exited with code 
JobExecutor.log.cewc2=Container exited with code 
JobExecutor.log.coc=Checking out code...
ServerDockerExecutor.log.testDocker=Testing specified docker image...
ServerDockerExecutor.log.busybox=Checking busybox availability...
RemoteExecutor.test.testOn=Testing on agent
RemoteExecutor.test.paa=Pending agent allocation...
JobExecutorsPage.hasExecutorsTitle=For a particular job, the first applicable executor will be used
JobExecutorsPage.noExecutorsTitle=No executors defined. Jobs will use auto-discovered executor with default settings
JobExecutorsPage.editExecutor=Edit Executor
JobExecutorsPage.addExecutor=Add Executor
JobExecutorPanel.deleteConfirm=Do you really want to delete this executor?
JobExecutorEditPanel.saveFail=This name has already been used by another job executor
JobExecutorEditPanel.testSuccess=Job executor tested successfully
JobExecutorEditPanel.testingTitle=Testing Executor
IssueTemplateListPage.deleteConfirm=Do you really want to delete this template?
IssueTemplateListPage.title=Define issue templates here. When a new issue is created, the first matching template will be used.
Issue_Description_Templates=Issue Description Templates
IssueTemplateEditPanel.h6=Issue Description Template
IssueTemplate.issue.desc=Optionally specify issues applicable for this template. Leave empty for all
IssueTemplate.issueDesc.name=Issue Description
IssueTemplate.issueDesc.desc=Specify issue description template content
LinkSpecListPage.title=Links can be used to associate different issues. For instance, an issue can be linked to child issues or blocking issues
LinkSpecListPage.colName=Name
LinkSpecListPage.colOtherName=Name On the Other Side
LinkSpecListPage.deleteConfirm=Do you really want to delete this link?
LinkSpecEditPanel.title=Issue Link
LinkSpecEditPanel.saveFail.sameName=Name and name on the other side should be different
LinkSpecEditPanel.saveFail.nameAlreadyUsed=Name already used by another link
TimeTrackingSettingPage.saveSuccess=Time tracking settings have been saved
Time_Tracking_Settings=Time Tracking Settings
StateEditPanel.title=Issue State
StateEditPanel.saveFail=This name has already been used by another state
IssueStateListPage.title=Define all custom issue states here. The first state will be used as initial state of created issues
IssueStateListPage.colName=Name
IssueStateListPage.colColor=Color
IssueStateListPage.colDesc=Description
IssueStateListPage.deleteConfirm=Do you really want to delete this state?
IssueStateListPage.topbarTitle=Issue States
IssueStateListPage.noDesc=<i>No description</i>
IssueStateListPage.inital=<span class='badge badge-light-info badge-sm ml-2'>initial</span>
CheckIssueIntegrityPage.title=In rare cases, your issues might be out of sync with workflow settings (undefined state/field etc.). Run integrity check below to find problems and get them fixed.
CheckIssueIntegrityPage.run=Run Integrity Check
WorkflowReconcilePanel.fixing=Fixing...
WorkflowReconcilePanel.checking.state=Checking state...
WorkflowReconcilePanel.checking.fields=Checking fields...
WorkflowReconcilePanel.checking.value=Checking field values...
WorkflowReconcilePanel.checking.sf=Checking state and field ordinals...
WorkflowReconcilePanel.complete=Workflow reconciliation completed
StateSpec.color.desc=Specify color of the state for displaying purpose
DefaultBoardListPage.title=Define default issue boards for all projects here. A certain project can override this setting to define its own issue boards.
DefaultBoardListPage.deleteConfirm=Do you really want to delete this board?
DefaultBoardListPage.columns=Columns
DefaultBoardListPage.identifyField=Identify Field
DefaultBoardListPage.defaultBoards=Default Issue Boards
BoardEditPanel.title=Issue Board
BoardEditPanel.saveFail=This name has already been used by another board
BoardSpec.bq.name=Base Query
BoardSpec.bq.desc=Optionally specify a base query to filter/order issues of the board
BoardSpec.blbq.name=Backlog Base Query
BoardSpec.blbq.desc=Optionally specify a base query to filter/order issues in backlog. Backlog issues are those not associating with current iteration
BoardSpec.if.name=Identify Field
BoardSpec.if.placeholder=Please choose a field
BoardSpec.if.desc=Specify issue field to identify different columns of the board. Only state and single-valued enumeration field can be used here
BoardSpec.col.name=Board Columns
BoardSpec.col.placeholder=Please choose at least two columns
BoardSpec.col.desc=Specify columns of the board. Each column corresponds to a value of the issue field specified above
BoardSpec.ip.name=Iteration Prefix
BoardSpec.ip.desc=If specified, System will only display iterations with this prefix
BoardSpec.df.name=Display Fields
BoardSpec.df.placeholder=Not displaying any fields
BoardSpec.df.desc=Specify fields to display in board card
BoardSpec.dl.name=Display Links
BoardSpec.dl.placeholder=Not displaying any links
BoardSpec.dl.desc=Specify links to display in board card
CommitMessageFixPatternsPage.title=Commit message can be used to fix issues by prefixing and suffixing issue number with specified pattern. Each line of the commit message will be matched against each entry defined here to find issues to be fixed
CommitMessageFixPatternsPage.updateSuccess=Setting updated
CommitMessageFixPatternsPage.Entry.prefixPattern.name=Prefix Pattern
CommitMessageFixPatternsPage.Entry.prefixPattern.desc=Specify a <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>regular expression</a> before issue number
CommitMessageFixPatternsPage.Entry.suffixPattern.name=Suffix Pattern
CommitMessageFixPatternsPage.Entry.suffixPattern.desc=Specify a <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>regular expression</a> after issue number
CommitMessageFixPatternsPage.validateError=Malformed regular expression
LinkSpec.name.desc=Name of the link
LinkSpec.multi.name=Multiple
LinkSpec.multi.desc=Whether multiple issues can be linked
LinkSpec.li.name=Linkable Issues
LinkSpec.li.placeholder=All issues
LinkSpec.li.desc=Optionally specify criteria of issues which can be linked
LinkSpec.asy.name=Asymmetric
LinkSpec.asy.desc=Whether the link is asymmetric. A asymmetric link has different meaning from different side. For instance a 'parent-child' link is asymmetric, while a 'related to' link is symmetric
LinkSpec.sa.name=Show Always
LinkSpec.sa.desc=Whether to show this link always even if no issues are linked
LinkSpecOpposite.name.name=Name On the Other Side
LinkSpecOpposite.name.desc=Name of the link on the other side. For instance if name is <tt>child issues</tt>, name on the other side can be <tt>parent issue</tt>
LinkSpecOpposite.multi.name=Multiple On the Other Side
LinkSpecOpposite.multi.desc=Whether multiple issues can be linked on the other side. For instance child issues on the other side means parent issue, and multiple should be false on that side if only one parent is allowed
LinkSpecOpposite.li.name=Linkable Issues On the Other Side
LinkSpecOpposite.li.placeholder=All issues
LinkSpecOpposite.li.desc=Optionally specify criteria of issues which can be linked on the other side
StateTransitionListPage.title=Define how issue states should be transited from one to another, either manually or automatically when some events happen. And the rule can be configured to apply to certain projects and issues via the applicable issues setting
StateTransitionListPage.topbarTitle=Issue State Transitions
TransitionSpec.from.name=From States
AnyState=Any state
NoFieldsRemove=No fields to remove
TransitionSpec.rf.name=Remove Fields
TransitionSpec.rf.desc=Optionally select fields to remove when this transition happens
BranchUpdatedSpec.name=Code is committed
ApplicableBranches.name=Applicable Branches
ApplicableBranches.placeholder=Any branch
ApplicableBranches.desc=Optionally specify space-separated branches applicable for this transition. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all
ApplicableIssues.name=Applicable Issues
ApplicableJobs.name=Applicable Jobs
ApplicableIssues.desc=Optionally specify issues applicable for this transition. Leave empty for all issues
BranchUpdatedSpec.codeIsCommitedToBranch=code is committed to branches '%s'
BranchUpdatedSpec.codeIsCommitedToAnyBranch=code is committed to any branch
BuildSuccessfulSpec.name=Build is successful
BuildSuccessfulSpec.jn.desc=Optionally specify space-separated jobs applicable for this transition. Use '*' or '?' for wildcard match. Prefix with '-' to exclude. Leave empty to match all
BuildSuccessfulSpec.triggerDesc1=build is successful for jobs '%s' on branches '%s'
BuildSuccessfulSpec.triggerDesc2=build is successful for jobs '%s' on any branch
BuildSuccessfulSpec.triggerDesc3=build is successful for any job on branches '%s'
BuildSuccessfulSpec.triggerDesc4=build is successful for any job and branch
IssueStateTransitedSpec.name=State of other issue is transited to
IssueStateTransitedSpec.states.name=State of other issue is transited to
IssueStateTransitedSpec.states.desc=What state has the other issue transited to
IssueStateTransitedSpec.triggerDesc=state of other issue is transited to
ManualSpec.name=Transit manually
ToState.name=To State
AnyUser=Any user
ManualSpec.ar.name=Authorized Roles
ManualSpec.ar.desc=Optionally specify authorized roles to press this button. If not specified, all users are allowed
ManualSpec.pf.name=Prompt Fields
ManualSpec.pf.placeholder=No fields to prompt
ManualSpec.pf.desc=Optionally select fields to prompt when this button is pressed
ManualSpec.triggerDesc1=transit manually by any user
ManualSpec.triggerDesc2=transit manually by any user of roles %s
ManualSpec.roleName.issueSubmitter=<Issue Submitter>
ManualSpec.roleName.issueSubmitter.desc=user opening the issue
ManualSpec.userAssociated.desc=user associated with this field
ManualSpec.groupAssociated.desc=group associated with this field
NoActivitySpec.name=No activity for some time
NoActivitySpec.days.name=No Activity Days
NoActivitySpec.triggerDesc=no activity for %s days
PullRequestSpec.branch.name=Target Branches
PullRequestSpec.branch.placeholder=Any branch
PullRequestSpec.branch.desc=Optionally specify space-separated target branches of the pull requests to check. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all branches
PullRequestOpenedSpec.name=Pull request is opened
PullRequestOpenedSpec.triggerDesc1=pull request to branches '%s' is opened
PullRequestOpenedSpec.triggerDesc2=pull request to any branch is opened
PullRequestMergedSpec.name=Pull request is merged
PullRequestMergedSpec.triggerDesc1=pull request to branches '%s' is merged
PullRequestMergedSpec.triggerDesc2=pull request to any branch is merged
PullRequestDiscardedSpec.name=Pull request is discarded
PullRequestDiscardedSpec.triggerDesc1=pull request to branches '%s' is discarded
PullRequestDiscardedSpec.triggerDesc2=pull request to any branch is discarded
TransitionEditPanel.title=Issue State Transition
StateTransitionListPage.deleteConfirm=Do you really want to delete this transition?
StateTransitionListPage.anyState=[Any state]
StateTransitionListPage.when=When ${triggerDesc}
StateTransitionListPage.issueQuery=For issues matching: ${issueQuery}
StateTransitionListPage.allIssues=For all issues
IssueFieldListPage.desc=Define all custom issue fields here. Each project can decide to use all or a subset of these fields via its issue transition setting.<b class="text-warning">NOTE: </b> Newly defined fields by default only appear in new issues. Batch edit existing issues from issue list page if you want them to have these new fields.
IssueFieldListPage.topbarTitle=Custom Issue Fields
IssueFieldListPage.colType=Type
IssueFieldListPage.deleteConfirm=Do you really want to delete this field?
FieldEditPanel.title=Custom Issue Field
FieldEditPanel.validateError.1=This name has already been used by another field
FieldSpec.description.desc=Optionally describes the custom field. Html tags are accepted
FieldSpec.am.name=Allow Multiple
FieldSpec.am.desc=Whether multiple values can be specified for this field
FieldSpec.sc.name=Show Conditionally
FieldSpec.sc.placeholder=Always
FieldSpec.sc.desc=Enable if visibility of this field depends on other fields
FieldSpec.aev.name=Allow Empty Value
FieldSpec.aev.desc=Whether this field accepts empty value
FieldSpec.iwiio.name=Include When Issue is Opened
FieldSpec.iwiio.desc=Whether to include this field when issue is initially opened. If not, you may include this field later when issue is transited to other states via issue transition rule
FieldSpec.ApplicableProjects.desc=Specify applicable projects for above option. Multiple projects should be separated by space. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty for all projects
All_Projects=All projects
Applicable_Projects_Desc=Specify applicable projects. Multiple projects should be separated by space. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty for all projects
FieldSpec.nameEmpty.name=Name Of Empty Value
FieldSpec.nameEmpty.desc=Specify the name of empty value
ShowCondition.when.name=When
ShowCondition.values.placeholder=Choose values...
ValueIsEmpty.name=is empty
ValueIsOneOf.name=has any value of
ValueIsNotAnyOf.name=does not have any value of
ValueIsNotEmpty.name=is not empty
Text=Text
Date=Date
Date\ Time=Date Time
Secret=Secret
Checkbox=Checkbox
Integer=Integer
Float=Float
Working\ Period=Working Period
Enumeration=Enumeration
User=User
Group=Group
Issue=Issue
Iteration=Iteration
Build=Build
Pull\ Request=Pull Request
TextField.ml.name=Multiple Lines
TextField.pattern.name=Pattern
TextField.pattern.desc=Optionally specify a <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>regular expression pattern</a> for valid values of the text input
SpecifiedDefaultValue.name=Use specified default value
SpecifiedDefaultValue.desc=For a particular project, the first matching entry will be used
SpecifiedDefaultValue.value.name=Specified default value
ScriptingDefaultValue.name=Evaluate script to get default value
ScriptingDefaultValue.script.name=Choose script name
ScriptingDefaultValue.script.desc=Groovy script to be evaluated. It should return a <i>String</i> value. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingDefaultMultiValue.script.desc=Groovy script to be evaluated. It should return <i>String</i> or <i>List<String></i>. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
UserChoiceField.ac.name=Available Choices
DefaulValue.value.name=Literal value
Field.dv.name=Default Value
Field.dv.placeholder=No default value
SpecifiedChoices.name=Use specified choices
SpecifiedChoices.choices.name=Specified choices
ScriptingChoices.name=Evaluate script to get choices
ScriptingChoices.script.desc=Groovy script to be evaluated. The return value should be a value to color map, for instance:<br> <code>return [\"Successful\":\"#00ff00\", \"Failed\":\"#ff0000\"]</code>, Use <tt>null</tt> if the value does not have a color. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingUserChoices.script.desc=Groovy script to be evaluated. The return value should be a list of user login names to be used as choices. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
AllUsers.name=All users
AllGroups.name=All groups
GroupChoiceField.ac.name=Available Choices
GroupChoiceField.editEtime.name=Can Edit Estimated Time
GroupChoiceField.editEtime.desc=If ticked, group indicated by this field will be able to edit estimated time of corresponding issues if time tracking is enabled
ScriptingGroupDefaultValue.script.desc=Groovy script to be evaluated. It should return name of a group. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingGroupChoice.script.name=Groovy script to be evaluated. The return value should be a list of group facade object to be used as choices. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingBooleanDefaultValue.script.desc=Groovy script to be evaluated. It should return a <i>boolean</i> value. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
Boolean.false=false
Boolean.true=true
IntegerField.min.name=Min Value
IntegerField.min.desc=Optionally specify the minimum value allowed.
IntegerField.max.name=Max Value
IntegerField.max.desc=Optionally specify the maximum value allowed.
ScriptingIntegerDefaultValue.script.desc=Groovy script to be evaluated. It should return a <i>Integer</i> value. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingFloatDefaultValue.script.desc=Groovy script to be evaluated. It should return a <i>Float</i> value. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
ScriptingDateDefaultValue.script.desc=Groovy script to be evaluated. It should return a <i>Date</i> value. Check <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>scripting help</a> for details
TimeTrackingSetting.hpd.name=Hours Per Day
TimeTrackingSetting.hpd.desc=Specify working hours per day. This will affect parsing and displaying of working periods. For instance <tt>1d</tt> is the same as <tt>8h</tt> if this property is set to <tt>8</tt>
TimeTrackingSetting.dpw.name=Days Per Week
TimeTrackingSetting.dpw.desc=Specify working days per week. This will affect parsing and displaying of working periods. For instance <tt>1w</tt> is the same as <tt>5d</tt> if this property is set to <tt>5</tt>
TimeTrackingSetting.al.name=Aggregation Link
TimeTrackingSetting.al.placeholder=No Aggregation
TimeTrackingSetting.al.desc=If specified, total estimated/spent time of an issue will also include linked issues of this type
TimeTrackingSetting.getWorkingPeriodHelp=Expects one or more <tt>&lt;number&gt;(w|d|h|m)</tt>. For instance <tt>1w 1d 1h 1m</tt> represents 1 week (%d days), 1 day (%d hours), 1 hour, and 1 minute
TimeTrackingSetting.workingPeriodHelpOnlyUseHours=Expects one or more <tt>&lt;number&gt;(h|m)</tt>. For instance <tt>1h 1m</tt> represents 1 hour and 1 minute
TimeTrackingSetting.useHoursAndMinutesOnly.name=Use Hours And Minutes Only
TimeTrackingSetting.useHoursAndMinutesOnly.desc=Whether to input and display estimated/spent time in hours/minutes only
TimeTrackingSetting.deleteLinkUsaege=time aggregation link
TimeTrackingSetting.parse.invalid=Invalid working period
WorkflowChangeAlertPanel.alert1=Issue workflow changed, 
WorkflowChangeAlertPanel.alert2=reconciliation
WorkflowChangeAlertPanel.alert3= needs to be performed to make data consistent. You may do this after making all necessary changes
WorkflowChangeAlertPanel.reconcile.name=reconciliation (need administrator permission)
WorkflowReconcilePanel.fus=Fix Undefined States
WorkflowReconcilePanel.fuf=Fix Undefined Fields
WorkflowReconcilePanel.fufv=Fix Undefined Field Values
WorkflowReconcilePanel.alert=Please resolve undefined states below. Note that if you select to delete an undefined state, all issues with that state will be deleted 
WorkflowReconcilePanel.alertFields=Please resolve undefined fields below
WorkflowReconcilePanel.alertValues=Please resolve undefined field values below
WorkflowReconcilePanel.undefined=Undefined
WorkflowReconcilePanel.changeTo=Change To
WorkflowReconcilePanel.fix=Fix
WorkflowReconcilePanel.fn=Field Name
WorkflowReconcilePanel.fv=Field Value
WorkflowReconcilePanel.res=Resolution
FieldInstanceIgnoreValue.displayName=Ignore this field
FieldInstanceSpecifiedValue.displayName=Use specified value
FieldInstanceSpecifiedValue.secretDisplayName=Use specified job secret
FieldInstanceScriptingValue.displayName=Evaluate script to get value
FieldInstanceScriptingValue.secretDisplayName=Evaluate script to get secret
ProjectBlobPage.btnClone.title=Code clone or download
ProjectBlobPage.btnAdd.title=Add files to current directory
ProjectBlobPage.btnSearch.title=Search files, symbols and texts
ProjectBlobPage.btnHistroy.title=Commit history of current path
ProjectBlobPage.projectTitle=Files
ProjectBlobPage.revisionIndexing=Revision indexing in progress... (search in this revision will be accurate after indexed)
ProjectBlobPage.enableBuildDesc=Enable build support by 
ProjectBlobPage.adding=adding ${blobPath}
ProjectBlobPage.storedLFS=Stored with Git LFS
ProjectBlobPage.missing=This is a Git LFS object, but the storage file is missing
ProjectBlobPage.bf=Binary file
ProjectBlobPage.tooLarge=File is too large
ProjectBlobPage.addOnBranch=Add on branch 
ProjectBlobPage.deleteFromBranch=Delete from branch 
ProjectBlobPage.reviewNeed=Review required for this deletion. Please submit pull request instead
ProjectBlobPage.buildNeed=Build required for this deletion. Please submit pull request instead
ProjectBlobPage.loginFirst=Please login to perform this operation
ProjectBlobPage.noPerm=Code write permission is required for this operation
ProjectBlobPage.disallow=This operation is disallowed by branch protection rule
ProjectBlobPage.getPermalink=Press 'y' to get permalink
QuickSearchPanel.title=File and Symbol Search 
QuickSearchPanel.title2=in current branch
QuickSearchPanel.showMore=Show more
QuickSearchPanel.noMatch=No matches
AdvancedSearchPanel.title=Advanced Search 
AdvancedSearchPanel.tree=Search inside current tree
SearchInput.validate.1=Search is too general
SearchInput.validate.2=Invalid PCRE syntax
SearchInput.validate.3=This field is required
TextQueryOptionEditor.fileName=File name
TextQueryOptionEditor.sName=Symbol name
SearchResultPanel.result=Search Result
SearchResultPanel.prev=Show previous match
SearchResultPanel.next=Show next match
SearchResultPanel.exp=Expand all
SearchResultPanel.coll=Collapse all
SearchResultPanel.nothing=Nothing matching your query
SearchResultPanel.tooManyResults=(too many matches, displaying ${num} of them)
GetCodePanel.download1=Download zip
GetCodePanel.download2=Download tar.gz
GetCodePanel.copy=Copy clone url
GetCodePanel.clone1=Clone in VSCode
GetCodePanel.clone2=Clone in IntelliJ
BlobUploadPanel.title=Upload Files
BlobUploadPanel.dir=Optionally specify relative directory to put uploaded files
BlobUploadPanel.cm=Commit Message
BlobUploadPanel.addViaUpload=Add files via upload
BlobUploadPanel.upload=Upload
BlobUploadPanel.dirTitle=Directory
BlobUploadPanel.file=File
BlobNavigator.typeName=Name your file
BlobNavigator.root=ROOT
NoNameFormComponent.title=Please specify file name above before editing content
BlobEditPanel.editSource=Edit Source
BlobEditPanel.toUtf8=Will be transcoded to UTF-8
BlobEditPanel.commitToUtf8=<svg class='icon icon-sm mr-1'><use xlink:href='%s'/></svg> %s will be transcoded to UTF-8 upon commit
CommitOptionPanel.title=Commit Your Change
CommitOptionPanel.commitMsgDelete=Delete ${oldName}
CommitOptionPanel.commitMsgAdd1=Add ${newName}
CommitOptionPanel.commitMsgAdd2=Add new file
CommitOptionPanel.commitMsgEdit=Edit ${oldName}
CommitOptionPanel.commitMsgRename=Rename ${oldName}
CommitOptionPanel.error.filenameNeed=Please specify file name
CommitOptionPanel.error.reviewNeed=Review required for this change. Please submit pull request instead
CommitOptionPanel.error.buildNeed=Build required for this change. Please submit pull request instead
CommitOptionPanel.error.signNeed=Signature required for this change, but no signing key is specified, please generate system GPG signing key first
CommitOptionPanel.error.nameAlreadyUsed=A path with same name already exists.Please choose a different name and try again.
CommitOptionPanel.error.fileExists=A file exists where you’re trying to create a subdirectory.Choose a new path and try again...
CommitOptionPanel.warn.someoneChanged=Someone made below change since you started editing
FolderViewPanel.loadLastCommit=<span class='text-warning'>Loading last commit info...</span>
FolderViewPanel.binaryFile=This seems like a binary file!
BlobViewPanel.viewSource=View Source
BlobViewPanel.blame=Blame
BlobViewPanel.download=Download source file
BlobViewPanel.fileTooLarge=Displaying first ${lines} lines as file is too large
BlobViewPanel.editFileTooLarge=File is too large to edit here
BlobViewPanel.editOnBranch=Edit on branch 
BlobViewPanel.deleteFromBranch=Delete from branch 
SourceFormComponent.title=Below content is restored from an unsaved change. Clear to discard
SourceViewPanel.codecomment=Code Comment
SourceViewPanel.hidecomment=Hide comment
SourceViewPanel.sccs=Show commented code snippet
SourceViewPanel.outline=Outline
SourceViewPanel.lineTooLong=Unable to comment as line is too long
SourceViewPanel.tooLong=Comment too long
SymbolLinkPanel.title=Symbol link: 
RevisionSelector.title.focb=Find or create branch
RevisionSelector.title.fb=Find branch
RevisionSelector.title.foct=Find or create tag
RevisionSelector.title.ft=Find tag
RevisionSelector.colBranches=branches
RevisionSelector.colTags=tags
RevisionSelector.createBranchesLabel=Create branch <b>${ref}</b> from ${revision}
RevisionSelector.createTagsLabel=Create tag <b>${ref}</b> from ${revision}
RevisionSelector.noBranch=No branches found
RevisionSelector.noTag=No tags found
RevisionSelector.canNotFindRevision=Can not find commit of revision ${revision}
RevisionSelector.inputPlaceholder=Input revision
RevisionPicker.choose=Choose Revision
RevisionPicker.projectNotSpecify=Project not specified yet
RevisionPicker.selectProject=<i>Select project first</i>
GitLinkPanel.subMoudle=Sub module: 
ProjectBranchesPage.base=Base
ProjectBranchesPage.fb=Filter branches...
ProjectBranchesPage.default=default
ProjectBranchesPage.setDefault=Set default
ProjectBranchesPage.create=Create Branch
ProjectBranchesPage.pleaseConfirm=Please confirm
ProjectBranchesPage.openPr1=There are 
ProjectBranchesPage.openPr2=open pull requests
ProjectBranchesPage.openPr3= against branch 
ProjectBranchesPage.openPr4=,These pull requests will be discarded if the branch is deleted
ProjectBranchesPage.createError.nameAlreadyExists=Branch '${branchName}' already exists, please choose a different name
ProjectBranchesPage.createError.unable=Unable to create protected branch
ProjectBranchesPage.createError.needSign=Valid signature required for head commit of this branch per branch protection rule
ProjectBranchesPage.createSuccess=Branch '${branchName}' created
ProjectBranchesPage.prOpen=Open
ProjectBranchesPage.prOpenTitle=A pull request is open for this change
ProjectBranchesPage.prMerged=Merged
ProjectBranchesPage.prMergedTitle=This change is squashed/rebased onto base branch via a pull request
ProjectBranchesPage.deleteTitle1=Can not delete default branch
ProjectBranchesPage.deleteTitle2=Deletion not allowed due to branch protection rule
ProjectBranchesPage.deleteTitle3=You selected to delete branch ${branch}
ProjectBranchesPage.deleteSuccess=Branch '${branch}' deleted
ProjectBranchesPage.ahead=ahead
ProjectBranchesPage.ahead.title=${count} commits ahead of base branch
ProjectBranchesPage.behind=behind
ProjectBranchesPage.behind.title=${count} commits behind of base branch
BranchBean.branchName.name=Branch Name
BranchBean.branchName.invalid=Invalid branch name
CommitDetailPage.browseCode=Browse code
CommitDetailPage.moreActions=More actions
CommitDetailPage.moreActions.createBranch=Create Branch
CommitDetailPage.moreActions.createTag=Create Tag
CommitDetailPage.moreActions.cherryPick=Cherry-Pick
CommitDetailPage.moreActions.revert=Revert
CommitDetailPage.cherryPick.error=No branch to cherry-pick to
CommitDetailPage.cherryPick.selectBranch=Select Branch to Cherry Pick to
CommitDetailPage.revert.error=No branch to revert on
CommitDetailPage.revert.selectBranch=Select Branch to Revert on
CommitDetailPage.specifyCommitMessage=Specify Commit Message
CommitDetailPage.cherryPick.error2=Error cherry-picking to ${branch}: ${errMsg}
CommitDetailPage.revert.error2=Error reverting on ${branch}: ${errMsg}
CommitDetailPage.cherryPick.error3=Error cherry-picking to ${branch}: Merge conflicts detected
CommitDetailPage.revert.error3=Error reverting on ${branch}: Merge conflicts detected
CommitDetailPage.cherryPick.success=Cherry-picked successfully
CommitDetailPage.revert.success=Reverted successfully
CommitDetailPage.revert.msg=Revert "${commitMsg}" \\n\\nThis reverts commit ${commitName}
CommitDetailPage.parent.commit=Show commit of this parent
CommitDetailPage.parent.compare=Compare with this parent
CommitDetailPage.1parent=1 parent
CommitDetailPage.multiParent=${parentCount} parent
CommitListPanel.compare=Compare with base revision
CommitListPanel.commitsError=Error calculating commits: check log for details
CommitMessagePanel.toggleDetail=Toggle detail message
ProjectTagsPage.ft=Filter tags..
ProjectTagsPage.toggleMessage=Toggle message
ProjectTagsPage.createdBy=Created by
ProjectTagsPage.createTag=Create Tag
ProjectTagsPage.createError.nameAlreadyExists=Tag '${tagName}' already exists, please choose a different name
ProjectTagsPage.createError.unable=Unable to create protected tag
ProjectTagsPage.createSuccess=Tag '${tagName}' created
ProjectTagsPage.deleteConfirm=Do you really want to delete tag '${tagName}' ?
ProjectTagsPage.deleteTitle=Deletion not allowed due to tag protection rule
ProjectTagsPage.deleteSuccess=Tag '${tagName}' deleted
TagBean.tagName.name=Tag Name
TagBean.tagMessage.name=Tag Message
TagBean.tagName.invalid=Invalid tag name
CommitStatusLink.title1=Some builds are ${status}, click for details
CommitStatusLink.title2=Builds are ${status}, click for details
CommitStatusLink.title3=No builds
RevisionDiffPanel.diffOps=Diff options
RevisionDiffPanel.bs=Batched suggestions
RevisionDiffPanel.toggleNav=Toggle navigation
RevisionDiffPanel.downloadPatch=Download patch
RevisionDiffPanel.noDiff=No diffs
RevisionDiffPanel.error.prUpdated=Pull request was updated by some others just now, please try again
RevisionDiffPanel.error.sugOnMark=Please remove outdated suggestion on: ${mark}
RevisionDiffPanel.discardConfirm=Do you really want to discard batched suggestions?
RevisionDiffPanel.pathHint=Path containing spaces or starting with dash needs to be quoted
RevisionDiffPanel.reviewedLabel=${progress} reviewed
RevisionDiffPanel.tooManyFiles=The diff you're trying to view is too large. We only load the first ${size} changed files.
RevisionDiffPanel.totalLabel=${totalFileChangesCount} files changed (${displayFileChangesCount} shown) with ${additionsCount} insertions(+) and ${deletionsCount} deletions(-)
RevisionDiffPanel.noDiffToNavigate=<i>No diffs to navigate</i>
RevisionDiffPanel.malformedPathFilter=Malformed path filter
DiffStatBar.tooltipTitle=${additions} additions & ${deletions} deletions
CommentPanel.saveWarn=Someone changed the content you are editing. Reload the page and try again.
CommentPanel.deleteConfirm=Do you really want to delete this comment?
BlobDiffPanel.blobMessage.missing=Storage file missing
BlobDiffPanel.blobMessage.fileTooLarge=Unable to diff as the file is too large.
BlobDiffPanel.blobMessage.someLineTooLarge=Unable to diff as some line is too large.
BlobDiffPanel.blobMessage.diffTooLarge=Large diffs are not rendered by default.
BlobDiffPanel.blobMessage.fileWasDeleted=This file was deleted.
BlobDiffPanel.blobMessage.lockFile=Lock files or dependency manifests are not rendered by default.
BlobDiffPanel.blobMessage.testFile=Test files are not rendered by default.
BlobDiffPanel.blobMessage.generatedFile=Generate files are not rendered by default.
BlobDiffPanel.blobMessage.totalDiffsExceed=Total diffs exceeding the maximum limit are not rendered by default.
BlobDiffPanel.blobMessage.unknownDiff=unknown diffs are not rendered.
BlobDiffPanel.blobMessage.emptyAdded=Empty file added.
BlobDiffPanel.blobMessage.emptyRemoved=Empty file removed.
BlobDiffPanel.blobMessage.bf=Binary file.
BlobDiffPanel.blobMessage.cii=Content is identical
BlobDiffPanel.setUnreviewed=Set unreviewed
BlobDiffPanel.setReviewed=Set reviewed
BlobDiffPanel.editOnSourceBranch=Edit on source branch
BlobDiffPanel.editOnBranch=Edit on branch ${revision}
BlobDiffPanel.loadDiff=Load diff
BlobDiffPanel.viewSource=View source
BlobImageDiffPanel.before=Before modification
BlobImageDiffPanel.after=After modification
InvalidCodeCommentPage.missingCommits=Some related commits of the code comment is missing
InvalidCodeCommentPage.missingCommitsDesc=This might happen when project points to a wrong git repository, or the commit is garbage collected.
InvalidCodeCommentPage.missingCommitsLabel=Missing Commits
InvalidCodeCommentPage.commentFileLabel=Comment on File
InvalidCodeCommentPage.codeComments=Code Comments
InvalidCodeCommentPage.commentContentLabel=Comment Content
InvalidCodeCommentPage.deleteButton=Delete Comment
InvalidCodeCommentPage.confirmDeleteMessage=Really want to delete this code comment?
InvalidCodeCommentPage.deleteSuccessMessage=Code comment ${codeCommentId} deleted
CodeCommentPanel.action.replied=replied
CodeCommentPanel.action.commented=commented
CodeCommentPanel.showCommentContext=Current context is different from the context when this comment is added, click to show the comment context
CodeCommentPanel.showReplyContext=Current context is different from the context when this reply is added, click to show the reply context
CodeCommentPanel.deleteConfirm=Do you really want to delete this comment?
CodeCommentPanel.deleteReplyConfirm=Do you really want to delete this reply?
CodeCommentPanel.deleteRepliesConfirm=Deleting this comment will also delete all replies, do you really want to continue?
CodeCommentPanel.leaveANote.placeholder=Leave a note
CodeCommentPanel.contentLabel=Content
CodeCommentPanel.resolved=resolved
CodeCommentPanel.unresolved=unresolved
CodeCommentPanel.permanentLink=Permanent link
StatusChangeOptionBean.name=Confirm your action
CodeCommentListPanel.search.placeholder=Query/order comments
CodeCommentListPanel.selectToResolved=Please select comments to set resolved
CodeCommentListPanel.noComentsToResolved=No comments to set resolved
CodeCommentListPanel.noComentsToUnresolved=No comments to set unresolved
CodeCommentListPanel.noComentsToDelete=No comments to delete
CodeCommentListPanel.noComentsToRead=No comments to set as read
CodeCommentListPanel.selectToUnresolved=Please select comments to set unresolved
CodeCommentListPanel.deleteSelectedConfirm=Type <code>yes</code> below to delete selected issues
CodeCommentListPanel.deleteAllQueriedConfirm=Type <code>yes</code> below to delete all queried comments
CodeCommentListPanel.deleteSelectedTitle=Please select issues to delete
CodeCommentListPanel.foundCommentsCount=found ${count} comments
CodeCommentListPanel.onFile=on file ${path}
RevisionComparePage.commonAncestor=common ancestor
RevisionComparePage.swap=Swap
RevisionComparePage.compare=Compare
RevisionComparePage.base=Base
RevisionComparePage.createPr=Create Pull Request for This Change
RevisionComparePage.selectToCompare=Please select revisions to compare
RevisionComparePage.historyOfComparing=History of comparing revisions is unrelated
RevisionComparePage.tooltip.onlyCompreWithCommonAncestor=Can only compare with common ancestor when different projects are involved
RevisionComparePage.tooltip.checkToCompareRightSideWithCommonAncestor=Check this to compare 'right side' with common ancestor of left and right
RevisionComparePage.effectiveRequest.labelPrOpen=This change is already opened for merge by 
RevisionComparePage.effectiveRequest.label=This change is squashed/rebased onto base branch via 
RevisionComparePage.prLink.label=pull request #${number}
RevisionComparePage.fileChanges=File Changes
InvalidPullRequestPage.missingCommits=Some related commits of the pull request are missing
InvalidPullRequestPage.description=This might happen when project points to a wrong git repository, or these commits are garbage collected.
InvalidPullRequestPage.missingCommitsTitle=Missing Commits
InvalidPullRequestPage.branches=Pull Request Branches
InvalidPullRequestPage.title=Pull Request Title
InvalidPullRequestPage.descriptionTitle=Pull Request Description
InvalidPullRequestPage.delete=Delete Pull Request
InvalidPullRequestPage.deleteSuccess=Pull request #${number} deleted
InvalidPullRequestPage.deleteConfirm=Do you really want to delete pull request #${number}?
PullRequestListPanel.queryHint=Query/order pull requests
PullRequestListPanel.openNew=Open new pull request
PullRequestListPanel.comments=Comments
PullRequestListPanel.watchSuccess=Watch status changed
PullRequestListPanel.noPrWatchUnWatch=No pull requests to watch/unwatch
PullRequestListPanel.noPrDiscard=No pull requests to discard
PullRequestListPanel.noPrDelete=No pull requests to delete
PullRequestListPanel.noPrRead=No pull requests to set as read
PullRequestListPanel.selectToWatchUnWatch=Please select pull requests to watch/unwatch
PullRequestListPanel.selectToDiscard=Please select pull requests to discard
PullRequestListPanel.discardConfirm=Type <code>yes</code> below to discard selected pull requests
PullRequestListPanel.error.alreadyClosed=Pull request #${number} already closed
PullRequestListPanel.deleteSelectedConfirm=Type <code>yes</code> below to delete selected pull requests
PullRequestListPanel.deleteSelectedTitle=Please select pull requests to delete
PullRequestListPanel.discardAllQueriedConfirm=Type <code>yes</code> below to discard all queried pull requests
PullRequestListPanel.deleteAllQueriedConfirm=Type <code>yes</code> below to delete all queried pull requests
PullRequestListPanel.foundCount=found ${count} pull requests
ReviewStatusIcon.title.pending=Pending review
ReviewStatusIcon.title.approved=Approved
ReviewStatusIcon.title.requestedForChanges=Requested for changes
ReviewListPanel.requestView.title=Request review
ReviewListPanel.removeReviewer.title=Remove this reviewer
ReviewListPanel.reviewAnotherConfirm=Do you really want to request another review from '${reviewer}'?
ReviewListPanel.resetMyReview=Reset my review
ReviewListPanel.deleteReviewer=Do you really want to remove reviewer '${reviewer}'?
ReviewListPanel.canNotDeleteReviewer=Reviewer '${reviewer}' is required and can not be removed ?
ReviewerChoice.addReviewer.placeholder=Add reviewer...
PullRequestSingleChoice.choose.placeholder=Choose pull request...
AssigneeChoice.addAssignee=Add assignee...
AssignmentListPanel.removeAssigneeTitle=Remove this assignee
AssignmentListPanel.deleteAssigneeConfirm=Do you really want to remove assignee '${assignee}'?
NewPullRequestPage.target=Target
NewPullRequestPage.source=Source
NewPullRequestPage.selectAnother=Please select different branches
NewPullRequestPage.mergeDesc.1=Branch 
NewPullRequestPage.mergeDesc.2=is up to date with all commits from 
NewPullRequestPage.mergeDesc.3=.
NewPullRequestPage.mergeDesc.4=Try 
NewPullRequestPage.mergeDesc.5=swap source and target 
NewPullRequestPage.mergeDesc.6=for the comparison.
NewPullRequestPage.unrelatedMsg=History of target branch and source branch is unrelated
NewPullRequestPage.needSelectBranch=Please select branches to create pull request
NewPullRequestPage.inputTitle.placeholder=Input title here
NewPullRequestPage.inputTitle.label=Title
NewPullRequestPage.mergeStrategy=Merge Strategy
NewPullRequestPage.reviewers=Reviewers
NewPullRequestPage.assignees=Assignees
NewPullRequestPage.assignToMe=assign to me
NewPullRequestPage.mergedBy=Pull request can only be merged after getting approvals from all reviewers
NewPullRequestPage.assigneePermission=Assignees have code write permission and will be responsible for merging the pull request
NewPullRequestPage.sendPullRequest=Send Pull Request
NewPullRequestPage.sendWarn=Either target branch or source branch has new commits just now, please re-check.
NewPullRequestPage.title=Title
NewPullRequestPage.description.error=Description too long
NewPullRequestPage.prMergePreview=Pull request merge preview
NewPullRequestPage.mergeWithoutConflicts=Able to merge without conflicts
NewPullRequestPage.mergeWithConflicts=There are merge conflicts. You can still create the pull request though
NewPullRequestPage.mergeCaculating=Calculating merge preview...
NewPullRequestPage.createPrWithHtml=<span class='text-nowrap'>Create Pull Request</span>
Create\ Merge\ Commit=Create Merge Commit
Create\ Merge\ Commit\ If\ Necessary=Create Merge Commit If Necessary
Squash\ Source\ Branch\ Commits=Squash Source Branch Commits
Rebase\ Source\ Branch\ Commits=Rebase Source Branch Commits
MergeStrategy.createMergeCommitDesc=Add all commits from source branch to target branch with a merge commit.
MergeStrategy.createMergeCommitIfNecessaryDesc=Only create merge commit if target branch can not be fast-forwarded to source branch
MergeStrategy.squashDesc=Squash all commits from source branch into a single commit in target branch
MergeStrategy.rebaseDesc=Rebase all commits from source branch onto target branch
UnreviewedChangesPanel.label1=There are 
UnreviewedChangesPanel.label2=new changes 
UnreviewedChangesPanel.label3=since your review
ConflictResolveInstructionPanel.label1=From working directory of project 
ConflictResolveInstructionPanel.label2=check out source branch and merge with target branch
ConflictResolveInstructionPanel.getFetch=git fetch origin
ConflictResolveInstructionPanel.getMerge=git merge 
ConflictResolveInstructionPanel.origin=origin/
ConflictResolveInstructionPanel.gitPull=git pull 
ConflictResolveInstructionPanel.gitPullRebase=git pull --rebase 
ConflictResolveInstructionPanel.gitRebase=git rebase origin/
ConflictResolveInstructionPanel.gitCheckout=git checkout 
ConflictResolveInstructionPanel.gitPushOrigin=git push origin 
ConflictResolveInstructionPanel.gitPushOriginF=git push -f origin 
ConflictResolveInstructionPanel.resolveMergeConflicts=Resolve merge conflicts, test changes and push result commit to DevGrip
ConflictResolveInstructionPanel.resolveRebaseConflicts=Resolve rebase conflicts, test changes and force push result commit to DevGrip
ConflictResolveInstructionPanel.fromWorking=From working directory of project 
ConflictResolveInstructionPanel.chekcout=check out source branch and rebase to destination branch
PullRequestDetailPage.moreInfo=More info
PullRequestDetailPage.checkout=Check out to local workspace
PullRequestDetailPage.discarded=This pull request has been discarded
PullRequestDetailPage.fastForward=Target branch was fast-forwarded to source branch
PullRequestDetailPage.merged=Commits were merged into target branch
PullRequestDetailPage.merged2=Commits were merged into target branch outside of this pull request
PullRequestDetailPage.squashed=Commits were squashed into a single commit on target branch
PullRequestDetailPage.rebased=Commits were rebased onto target branch
PullRequestDetailPage.sourceBranchIsOutdated=Source branch is outdated
PullRequestDetailPage.sourceBranchIsOutdated.showChanges=Show changes
PullRequestDetailPage.conflicts=There are merge conflicts.
PullRequestDetailPage.reviewRequiredForThisChange=Review required for this change. Submit pull request instead
PullRequestDetailPage.needToBeVerifiedBySomeJobs=This change needs to be verified by some jobs. Submit pull request instead
PullRequestDetailPage.noValidSignature=No valid signature for head commit of target branch
PullRequestDetailPage.noGpgSigningKeySpecified=Commit signature required but no GPG signing key specified
PullRequestDetailPage.errorValidatingCommitMessageOf=Error validating commit message of '${errorName}': ${errorMsg}
PullRequestDetailPage.mergeBranchInto=Merge branch '${target}' into branch '${source}'
PullRequestDetailPage.mergeBranchIntoWithProject=Merge branch '${target}' of project '${projectPath}' into branch '${source}'
PullRequestDetailPage.pleaseFollow=Please follow 
PullRequestDetailPage.thisInstruction=this instruction
PullRequestDetailPage.toResolve=to resolve the conflicts
PullRequestDetailPage.prCanNotBeMerged=Pull request can not be merged now as valid signature is required for head commit
PullRequestDetailPage.prCanNotBeMerged2=Pull request can not be merged now as it was
PullRequestDetailPage.prCanNotBeMerged3=Pull request can not be merged now as it is
PullRequestDetailPage.prCanNotBeMerged4=Pull request can not be merged now as
PullRequestDetailPage.someRequiredBuilds=some required builds
PullRequestDetailPage.notSuccess=are not successful
PullRequestDetailPage.notFinished=are not finished yet
PullRequestDetailPage.runThisJob=Run this job
PullRequestDetailPage.prCanOnlymergedBy=Pull request can only be merged by users with code write permission
PullRequestDetailPage.pendingReview=pending review
PullRequestDetailPage.actions.merge=Merge
PullRequestDetailPage.actions.discard=Discard
PullRequestDetailPage.actions.approve=Approve
PullRequestDetailPage.actions.rc=Request For Changes
PullRequestDetailPage.actions.reopen=Reopen
PullRequestDetailPage.actions.updateSourceBranch=Update Source Branch 
PullRequestDetailPage.actions.delete=Delete Source Branch
PullRequestDetailPage.actions.restore=Restored Source Branch
PullRequestDetailPage.submitter=Submitter
PullRequestDetailPage.change=change
PullRequestDetailPage.hiddenJobs=Some jobs are hidden due to permission policy
PullRequestDetailPage.jobsMarkedPrefix=Jobs marked with 
PullRequestDetailPage.jobsMarkedSuffix=are required to be successful
PullRequestDetailPage.assigneesToExpected=Assignees are expected to merge the pull request
PullRequestDetailPage.mergeStrategy=Merge Strategy
PullRequestDetailPage.sync=Synchronize
PullRequestDetailPage.syncDesc=In case the pull request status is out of sync with underlying repository, you may synchronize them manually here
PullRequestDetailPage.checkoutPrHead=Checkout Pull Request Head
PullRequestDetailPage.gitFetchOrigin=git fetch origin 
PullRequestDetailPage.gitCheckout=git checkout FETCH_HEAD
PullRequestDetailPage.prMergePreview=Checkout Pull Request Merge Preview
PullRequestDetailPage.error.commitMessagePrefix=Error validating commit message of
PullRequestDetailPage.error.autoMergeCommitMessagePrefix=Error validating auto merge commit message
PullRequestDetailPage.buildRequired.succOnMerge=Jobs required to be successful on merge commit: 
PullRequestDetailPage.buildRequired.succ=Jobs required to be successful: 
PullRequestDetailPage.tabs.activities=Activities
PullRequestDetailPage.changeTargetBranchConfirm=Do you really want to change target branch to ${branch}?
PullRequestDetailPage.unknown=unknown
PullRequestDetailPage.prSyncSubmitted=Pull request synchronization submitted
PullRequestDetailPage.prDeleteSuccess=Pull request #${number} deleted
PullRequestDetailPage.prDeleteConfirm=Do you really want to delete this pull request?
PullRequestDetailPage.ops.merge=Merge
PullRequestDetailPage.ops.mergeModalTitle=Merge Target Branch into Source Branch
PullRequestDetailPage.ops.updateSuccess=Source branch updated successfully
PullRequestDetailPage.ops.updateFail=Target or source branch is updated. Please try again
PullRequestDetailPage.ops.rebase=Rebase
PullRequestDetailPage.ops.rebaseConfirm=You are rebasing source branch on top of target branch
PullRequestDetailPage.ops.approved=Approved
PullRequestDetailPage.ops.requestedForChanges=Requested For changes
PullRequestDetailPage.ops.deletedSourceBranch=Deleted source branch
PullRequestDetailPage.ops.restoredSourceBranch=Resotred source branch
PullRequestDetailPage.canNotPerform=Can not perform this operation now
PullRequestDetailPage.autoMergeLabel=Auto Merge
PullRequestDetailPage.autoMergeEnableDesc1=Pull request will be merged automatically with a preset 
PullRequestDetailPage.autoMergeEnableDesc2=commit message 
PullRequestDetailPage.autoMergeEnableDesc3=when ready. This option will be disabled upon adding new commits, changing merge strategy, or switching target branch.
PullRequestDetailPage.autoMergeEnableDescRebase=Pull request will be merged automatically when ready. This option will be disabled upon adding new commits, changing merge strategy, or switching target branch.
PullRequestDetailPage.presetCommitMessage=Preset Commit Message
PullRequestDetailPage.presetCommitMessageUpdated=Preset commit message updated
PullRequestDetailPage.autoMergeOn=ON
PullRequestDetailPage.autoMergeOff=OFF
PullRequestDetailPage.autoMergeDisableDesc=Enable this option to merge the pull request automatically when ready (all reviewers approved, all required jobs passed etc.)
MergeConfirmPanel.commitMessage.mergesPullRequestPrefix=Merges (pull request #%s)
MergeConfirmPanel.confirmDescription.rebase=Source branch commits will be rebased onto target branch
MergeConfirmPanel.confirmDescription.fastForward=Source branch commits will be fast-forwarded to target branch
CommitMessageBean.commitMessage.name=Commit Message
Reference.type.pr=pull request
Reference.type.build=build
Reference.type.issue=issue
EntityReferencePanel.title=Reference
EntityReferencePanel.help=Reference this ${type} in markdown or commit message via below string. 
EntityReferencePanel.helpProjectPath=Project path can be omitted if reference from current project
EntityWatchesPanel.andMore=and more
EntityWatchesPanel.watchersLabel=Watchers (${count})
OperationConfirmPanel.newCommitsWarn=There are <a href='${url}'>new commits</a> in this pull request
EntityNavPanel.entityName.pullRequest=pull request
EntityNavPanel.entityCountLabel=${entityName} ${offset} of ${total}
EntityNavPanel.prevTitle=Previous ${entityName}
EntityNavPanel.nextTitle=Next ${entityName}
EntityNavPanel.noMore=No more ${entityName}s
PullRequestChangePanel.changesSince=Changes since this action
PullRequestUpdatedPanel.tooManyCommits=Too many commits, displaying recent ${count}
PullRequestUpdatedPanel.thisCommitIsRebased=This commit is rebased
PullRequestActivitiesPage.changesSinceThisComment=Changes since last visit
PullRequestActivitiesPage.toggleComments=Toggle Comments
PullRequestActivitiesPage.toggleCommits=Toggle Commits
PullRequestActivitiesPage.toggleChangeHistory=Toggle change history
BlobTextDiffPanel.showMoreLines=Show more lines
BlobTextDiffPanel.skippedLines=${linesNum} lines folded
BlobTextDiffPanel.spoofingRisk=Unicode spoofing risk in this line
ProjectIssueListPage.copyTitleAndNumber=Copy issue number and title
ProjectIssueListPage.unPin=Unpin this issue
EstimatedTimeEditBean.name=Edit Estimated Time
EstimatedTimeEditBean.estimatedTime.desc=Specify estimated time <b class='text-warning'>only for this issue</b>, not counting '%s'
EstimatedTimeEditBean.estimatedTime.placeholder=Estimated Time
IssueProgressPanel.estimatedTitle=Estimated/Spent time. Click for details
IssueProgressPanel.stopWork=Stop work
QueriedIssuesProgressPanel.estimatedTime=Overall Estimated Time: 
QueriedIssuesProgressPanel.spentTime=Overall Spent Time: 
QueriedIssuesProgressPanel.noValidQuery=No valid query to show progress
TimingDetailPanel.h6=Estimated Time
TimingDetailPanel.total=Total: 
TimingDetailPanel.own=Own: 
TimingDetailPanel.aggregated=Aggregated from 
TimingDetailPanel.spentTime=Spent Time
TimingDetailPanel.logWork=Log work
TimingDetailPanel.startWork=Start work
TimingDetailPanel.estimatedTimeNoAggr=Estimated Time:
TimingDetailPanel.spentTimeNoAggr=Spent Time:
IssueListPanel.queryHint=Query/order issues
IssueListPanel.createNewIssue=Create new issue
IssueListPanel.createIssueSuccess=Issue created
IssueListPanel.fieldAndLinks=Fields & Links
IssueListPanel.timing=Timing
IssueListPanel.tips=By default, issues of parent and child projects will also be listed. Use query <code>&quot;Project&quot; is current</code> to show issues only belong to this project
IssueListPanel.pin=pin this issue
IssueListPanel.votes=Votes
IssueListPanel.expandIssueDetail=expand issue detail
CreateEstimatedTimeEditBean.estimatedTime.name=Estimated Time
CreateEstimatedTimeEditBean.estimatedTime.desc=Optionally specify estimated time.
IssueListPanel.selectIssueToSync=Please select issues to sync estimated/spent time
IssueListPanel.requestedSuccess=Requested to sync estimated/spent time
IssueListPanel.selectIssueToEdit=Please select issues to edit
IssueListPanel.selectIssueToMove=Please select issues to move
IssueListPanel.selectIssueToCopy=Please select issues to copy
IssueListPanel.selectIssueToDelete=Please select issues to delete
IssueListPanel.noIssuesToSync=No issues to sync estimated/spent time
IssueListPanel.noIssuesToEdit=No issues to edit
IssueListPanel.noIssuesToMove=No issues to move
IssueListPanel.noIssuesToCopy=No issues to copy
IssueListPanel.noIssuesToDelete=No issues to delete
IssueListPanel.noIssuesToWatchUnwatch=No issues to watch/unwatch
IssueListPanel.noIssuesToRead=No issues to set as read
IssueListPanel.watchStatusChanged=Watch status changed
IssueListPanel.issuesMoved=Issues moved
IssueListPanel.issuesMoveConfirm=Type <code>yes</code> below to move selected issues to project '${targetProject}'
IssueListPanel.allQueriedIssuesMoveConfirm=Type <code>yes</code> below to move all queried issues to project '${targetProject}'
IssueListPanel.issuesCopied=Issues copied
IssueListPanel.issuesCopyConfirm=Type <code>yes</code> below to copy selected issues to project '${targetProject}'
IssueListPanel.allQueriedIssuesCopyConfirm=Type <code>yes</code> below to copy all queried issues to project '${targetProject}'
IssueListPanel.issuesDeleteConfirm=Type <code>yes</code> below to delete selected issues
IssueListPanel.allQueriedIssuesDeleteConfirm=Type <code>yes</code> below to delete all queried issues
IssueListPanel.foundIssues=found ${count} issues
IssueListPanel.exportAllQueriedTo=Export All Queried Issues
IssueListPanel.noIssuesToExport=No issues to export
IssueListPanel.header.number=Number
IssueListPanel.header.title=Title
IssueListPanel.header.iteration=Iteration
IssueListPanel.header.state=State
IssueListPanel.header.estimatedTime=Estimated Time
IssueListPanel.header.spentTime=Spent Time
FieldsAndLinksBean.fields.name=Display Fields
FieldsAndLinksBean.fields.desc=Specify fields to be displayed in the issue list
FieldsAndLinksBean.fields.placeholder=Not displaying any fields
FieldsAndLinksBean.links.name=Display Links
FieldsAndLinksBean.links.desc=Specify links to be displayed in the issue list
FieldsAndLinksBean.links.placeholder=Not displaying any links
BatchEditPanel.desc=<b>NOTE:</b> Batch editing issues will not cause state transitions of other issues even if transition rule matches
BatchEditPanel.filesToChange=Fields to Change
BatchEditPanel.state=State
BatchEditPanel.confidential=Confidential
BatchEditPanel.iteration=Iteration
BatchEditPanel.sendNotifications=Send Notifications
BatchEditPanel.sendToWatchers=Whether to send notifications to issue watchers for this change
BatchEditPanel.batchEditingLabel=Batch Editing ${issuesCount} Issues
BuiltInFieldsBean.state=State
BuiltInFieldsBean.confidential=Confidential
BuiltInFieldsBean.iterations=Iterations
PullRequestChangesPage.prevCommitTitle=Previous commit
PullRequestChangesPage.nextCommitTitle=Next commit
PullRequestChangesPage.youAreReviewASubset=You are viewing a subset of all changes. 
PullRequestChangesPage.showAllChanges=show all changes
PullRequestChangesPage.allChanges=All changes
PullRequestChangesPage.changesSinceLastReview=Changes since last review
PullRequestChangesPage.clickToSelectACommit=Click to select a commit, or shift-click to select multiple commit
PullRequestChangesPage.base=base
PullRequestChangesPage.head=head
PullRequestActivitiesPage.loginToComment=Login to comment
IssueActivitiesPanel.toggleWorkLog=Toggle work log
TransitionMenuLink.couldNotTransitLabel=<div class='px-3 py-2'><i>No applicable transitions or no permission to transit</i></div>
IssueSidePanel.removeIssueFromIteration=Remove issue from this iteration
IssueSidePanel.issueVotes=Issue Votes
IssueSidePanel.externalParticipants=External Participants
IssueSidePanel.removeExternalParticipantsTitle=Remove this external participant from issue
IssueSidePanel.externalParticipantsDesc=External participants do not have accounts and involve in the issue via email
IssuePrimaryPanel.unlink=Unlink this issue
IssuePrimaryPanel.selectExisting=Select Existing
IssuePrimaryPanel.createNew=Create New
IssuePrimaryPanel.addLinkTitle=Add ${linkName}
IssuePrimaryPanel.linkError.canNotLinktoSelf=Can not link to self
IssuePrimaryPanel.linkError.alreadyLinked=An issue already linked for ${specName}. Unlink it first
IssuePrimaryPanel.linkError.alreadyLinked2=Issue already linked
IssuePrimaryPanel.removeLinkConfirm=Do you really want to remove this link?
IssuePrimaryPanel.addIssueLink=Link issue
IssueSidePanel.removeIssueFromIterationConfirm=Do you really want to remove the issue from iteration '${iteration}'?
IssueSidePanel.addToIterationPlaceholder=Add to iteration...
IssueSidePanel.loginToVote=Login to vote
IssueSidePanel.unvote=Unvote
IssueSidePanel.vote=Vote
IssueSidePanel.notAbleToparticipate=${name} will not be able to participate in this issue. Do you want to continue?
IterationCrumbPanel.noPermission=<div class='px-3 py-2'><i>No permission to schedule issues</i></div>
IterationCrumbPanel.goToIterationDetail=go to iteration detail
FieldValuesPanel.uneditReason.noPermission=No permission to edit field
FieldValuesPanel.uneditReason.specNotFound=Field spec not found
FieldValuesPanel.uneditReason.needsReconciled=Issue needs to be reconciled
FieldValuesPanel.dependentFieldsTitle=Dependent Fields
FieldValuesPanel.notFoundWithI=<i>Not Found</i>
IssueEditableTitlePanel.editTitle=Edit issue title
IssueEditableTitlePanel.title=Title
IssueEditableTitlePanel.similarIssues=Similar Issues
CreateIssuePanel.title=New Issue
CreateIssuePanel.continueToAdd=Continue to create other issue
IssueStateBadge.stateTitle=State
NewIssueEditor.switchdescriptionTemplate=A different description template is available. Do you want to discard current description and switch?
IssueChoice.chooseIssue.placeholder=Choose issue...
IssueAddChoice.addIssue.placeholder=Add Issue...
IssueAuthorizationsPanel.desc=Specify additional users able to access this confidential issue besides those granted via role. Users mentioned in the issue will be authorized automatically 
IssueAuthorizationsPanel.unauthToThisUser=Unauthorize this user
IssueAuthorizationsPanel.authUser.placeholder=Authorize user...
IssueAuthorizationsPanel.authorizeSuccess=User authorized
IssueAuthorizationsPanel.authorizeUserSuccess=User '${name}' unauthorized
IssueAuthorizationsPanel.authorizeUserConfirm=Do you really want to unauthorize user '${name}'?
IssueWorkPanel.logedWork=logged work
IssueChangeData.activities.removedComment=removed comment
IssueChangeData.activities.changedConfidential=changed confidential
IssueChangeData.activities.changedDescription=changed description
IssueChangeData.activities.changedFields=changed fields
IssueChangeData.activities.addedToIteration=added to iteration '${iteration}'
IssueChangeData.activities.changedIterations=changed iterations
IssueChangeData.activities.removedFromIteration=removed from iteration '${iteration}'
IssueChangeData.activities.addedToLink=added '${linkName}' '${linkedIssueNumber}'
IssueChangeData.activities.removeedFromLink=removed '${linkName}' '${linkedIssueNumber}'
IssueChangeData.activities.changeOwnEstimatedTime=changed own estimated time
IssueChangeData.activities.changeOwnSpentTime=changed own spent time
IssueChangeData.activities.moved=moved
IssueChangeData.activities.referenceFromCodeComment=referenced from code comment
IssueChangeData.activities.referenceFromOtherIssue=referenced from other issues
IssueChangeData.activities.referenceFromPullRequest=referenced from pull request
IssueChangeData.activities.changedStateTo=changed state to '${state}'
IssueChangeData.activities.changedTitle=changed title
IssueChangeData.activities.changedTotalEstimatedTime=changed total estimated time
IssueChangeData.activities.changeTotalSpentTime=changed total spent time
IssueChangeData.activities.batchUpdated=batch edited
PullRequestChangeData.activities.approved=approved
PullRequestChangeData.activities.enabledAutoMerge=enabled auto merge
PullRequestChangeData.activities.disabledAutoMerge=disabled auto merge
PullRequestChangeData.activities.removedComment=removed comment
PullRequestChangeData.activities.changedDescription=changed description
PullRequestChangeData.activities.discarded=discarded
PullRequestChangeData.activities.merged=merged
PullRequestChangeData.activities.reopened=reopened
PullRequestChangeData.activities.requestedForChanges=requested for changes
PullRequestChangeData.activities.changedMergeStrategy=changed merge strategy
PullRequestChangeData.activities.referencedFromIssue=referenced from issue
PullRequestChangeData.activities.referencedFromOtherPullRequest=referenced from other pull request
PullRequestChangeData.activities.deleteSourceBranch=deleted source branch
PullRequestChangeData.activities.restoredSourceBranch=restored source branch
PullRequestChangeData.activities.changedTargetBranch=changed target branch
PullRequestChangeData.activities.changedTitle=changed title
CodeCommentEvent.codeCommentCreated=added
CodeCommentEvent.codeCommentDeleted=code comment deleted
CodeCommentEvent.codeCommentEdited=edited
CodeCommentEvent.codeCommentReplyCreated=replied
CodeCommentEvent.codeCommentReplyDeleted=reply deleted
CodeCommentEvent.codeCommentReplyEdited=reply edited
CodeCommentEvent.codeCommentsDeleted=code comments deleted
CodeCommentEvent.codeCommentStatusResolved=resolved
CodeCommentEvent.codeCommentStatusUnresolved=unresolved
CodeCommentEvent.touched=touched
IssueEvent.issueCommentCreated=commented
IssueEvent.issueCommentEdited=comment edited
IssueEvent.issueCommitAttached=commits attached
IssueEvent.issueDeleted=issue deleted
IssueEvent.issuesDeleted=deleted
IssueEvent.issueOpened=opened
IssueEvent.issueCopied=copied
IssueEvent.issueImported=imported
IssueEvent.issueMoved=moved
IssueEvent.issueTouched=touched
PackEvent.packPublished=published
PullRequestEvent.buildCommitUpdted=Build commit updated
PullRequestEvent.checkFailed=Check failed
PullRequestEvent.createCodeComment=created code comment
PullRequestEvent.repliedCodeComment=replied code comment
PullRequestEvent.resolvedCodeComment=resolved code comment
PullRequestEvent.unresolvedCodeComment=unresolved code comment
PullRequestEvent.createdComment=commented
PullRequestEvent.editedComment=comment edited
PullRequestEvent.pullRequestDeleted=pull request deleted
PullRequestEvent.pullRequestsDeleted=pull requests deleted
PullRequestEvent.mergePrivewUpdated=Merge preview updated
PullRequestEvent.pullRequestOpened=opened
PullRequestEvent.pullRequestUpdated=added commits
PullRequestEvent.assigned=assigned
PullRequestEvent.touched=touched
PullRequestEvent.unassigned=unassigned
PullRequestEvent.removedReviewer=removed reviewer
PullRequestEvent.requestedReview=requested review
PullRequestBuildEvent.activity.withVersion=build #%s (%s) is %s
PullRequestBuildEvent.activity.withoutVersion=build #%s is %s
ActivityDetail.prevValue=Previous Value
ActivityDetail.currentValue=Current Value
ActivityDetail.empty=empty
NewIssueEditor.chooseIterations.pleaseChoose=Choose iterations...
BasePage.performNotAllow=You are not allowed to perform this operation
IssueDetailPage.activities=Activities
IssueDetailPage.fixingCommits=Fixing Commits
IssueDetailPage.fixingBuilds=Fixing Builds
IssueDetailPage.authorizations=Authorizations
IssueDetailPage.deleteSuccess=Issue #${number} deleted
IssueDetailPage.deleteConfirm=Do you really want to delete this issue?
EntityNavPanel.entityName.issue=issue
IssueWorkPanel.deleteWorkConfirm=Do you really want to delete this work?
IssuePrimaryPanel.onbehalfOf= on behalf of <b>${behalf}</b>
IssuePrimaryPanel.linkIssuesButton=Link others
StateStatsBar.tipsTitle=${count} ${state} issues
StateStatsBar.noIssueInIteration=No issues in iteration
TransitionOptionPanel.stateTransitionTitle=Issue State Transition (${from} -> ${to})
IncompatibilitiesPage.warning=There are incompatibilities since your upgraded version
IncompatibilitiesPage.warningTips=you may show this page later via incompatibilities link in help menu
IncompatibilitiesPage.goback=Go Back
ResourceListPage.title=All RESTful Resources
ResourceListPage.notice=In case anonymous access is disabled or anonymous user does not have enough permission for a resource operation, you will need to authenticate by providing user name and password (or access token) via http basic auth header
ResourceListPage.resouceLabel=Resources
ResourceListPage.endpointLabel=Endpoint
ResourceDetailPage.operationLabel=Operation
ResourceDetailPage.httpMethodLabel=Http Method
MethodDetailPage.placeholderLabel=Placeholder
MethodDetailPage.exampleLabel=Example
MethodDetailPage.queryParams=Query Parameters
MethodDetailPage.param=Parameter
MethodDetailPage.required=Required
MethodDetailPage.response=Response
MethodDetailPage.opsSuccess=Operation Successful
MethodDetailPage.opsFailed=Operation Failed
MethodDetailPage.statusCode=Status Code
MethodDetailPage.responseBody=Response Body
MethodDetailPage.responseErrorDesc=Status code other than 200 indicating the error type
MethodDetailPage.errorDetailConetentTypeDesc=Error detail of content type &quot;text/plain&quot;
MethodDetailPage.curlExample=cURL Example 
ProjectImportPage.dryRun=Dry Run
UrlProjectImporter.title=Specify URL to import from
ImportServer.url.name=URL
ImportServer.url.desc=Specify URL of remote git repository. Only http/https protocol is supported
ImportServer.ra.name=Require Autentication
ImportServer.project.name=Project
ImportServer.project.desc=Specify project to import into at DevGrip side
ImportServer.import.starting=Importing from '%s' to '%s'...
ImportServer.import.cloneCode=Cloneing code...
ImportServer.import.success=project imported successfully
ImportServer.import.projectAlreadyHasCode=Skipping code clone as the project already has code
ImportServer.importError.targetAlreadyExists=Import target already exists. You need to have project management privilege over it
ImportServer.importError.onlyHttpSupported=Only http(s) protocol is supported
ProjectImportPage.topTitle=Importing projects from 
GitHubProjectImporter.title.step1=Authenticate to GitHub
GitHubProjectImporter.title.step2=Specify repositories
GitHubProjectImporter.title.step3=Specify import option
GitHubImportServer.apiurl.name=GitHub API URL
GitHubImportServer.apiurl.desc=Specify GitHub API url, for instance <tt>https://api.github.com</tt>
GitHubImportServer.gpat.name=GitHub Personal Access Token
GitHubImportServer.gpat.desc=GitHub personal access token should be generated with scope <b>repo</b> and <b>read:org</b>
GitHubImportServer.error.listingOrg=Error listing organizations
GitHubImportServer.error.dupIssueFieldMapping=Duplicate issue field mapping (issue: %s, field: %s)
GitHubImportServer.issueImportedCount=Imported %s issues
GitHubImportServer.error.rateLimit=Rate limit exceeded, wait until reset...
GitHubImportServer.error.httpFailedWithMsg=Http request failed (url: %s, status code: %d, error message: %s)
GitHubImportServer.error.notAGithubUrl=This does not seem like a GitHub api url
GitHubImportServer.error.authFailed=Authentication failed
GitHubImportServer.error.connectFailed=Error connecting api service
GitHubImportServer.starting=Importing from '%s' to '%s'...
GitHubImportServer.error.httpFailed=Http request failed (status: %s)
GitHubImportServer.import.projectAlreadyHasCode=Skipping code clone as the project already has code
GitHubImportServer.import.milestones=Importing milestones...
GitHubImportServer.import.issues=Importing issues...
GitHubImportServer.import.success=Repositories imported successfully
GitLabProjectImporter.title.step1=Authenticate to GitLab
GitLabProjectImporter.title.step2=Specify repositories
GitLabProjectImporter.title.step3=Specify import option
GitLabImportServer.apiurl.name=GitLab API URL
GitLabImportServer.apiurl.desc=Specify GitLab API url, for instance <tt>https://gitlab.example.com/api/v4</tt>
GitLabImportServer.gpat.name=GitLab Personal Access Token
GitLabImportServer.gpat.desc=GitLab personal access token should be generated with scope <b>read_api</b>, <b>read_user</b> and <b>read_repository</b>. Note that only groups/projects owned by user of specified access token will be listed
GitLabImportServer.error.notAGitLabUrl=This does not seem like a GitLab api url
GitLabImportServer.starting=Importing from '%s' to '%s'...
GitLabImportServer.import.projectAlreadyHasCode=Skipping code clone as the project already has code
GitLabImportServer.import.milestones=Importing milestones...
GitLabImportServer.import.issues=Importing issues...
GitLabImportServer.import.success=Repositories imported successfully
JiracloudProjectImporter.title.step1=Authenticate to JIRA cloud
JiracloudImportServer.apiurl.name=API url of your JIRA cloud instance
JiracloudImportServer.apiurl.desc=API url of your JIRA cloud instance, for instance, <tt>https://your-domain.atlassian.net/rest/api/3</tt>
JiracloudImportServer.accountEmail.name=Account Email
JiracloudImportServer.apiToken.name=Api Token
IssueImporter.import.success=Issues imported successfully
JiracloudImportServer.error.notAJiraCloudUrl=This does not seem like a Jira cloud api url
JiracloudImportServer.error.download=Error downloading attachment (url: %s, error message: %s)
EmptyWithI=<i>Empty</i>
ImportOrganization.org.desc=Select organization to import from. Leave empty to import from repositories under current account
ImportOrganization.includeForks.name=Include Forks
ImportOrganization.includeForks.desc=Whether to include forked repositories
ImportRepositories.parentDevgripProject.name=Parent DevGrip Project
ImportRepositories.importAll.name=Import All Repositories
ImportRepositories.includeForks.name=Include Forks
ImportRepositories.includeForks.desc=Whether to import forked GitHub repositories
ImportRepositories.parentDevgripProject.desc=Optionally specify a DevGrip project to be used as parent of imported repositories. Leave empty to import as root projects
ImportRepositories.repoToImport.name=GitHub Repositories to Import
ImportRepository.repo.name=GitHub Repository
ImportRepository.repo.desc=Select repository to import from
IssueImportOption.closedIssueState.name=Closed Issue State
IssueImportOption.closedIssueState.desc=Specify which issue state to use for closed GitHub issues.<br><b>NOTE: </b> You may customize DevGrip issue states in case there is no appropriate option here
IssueImportOption.assigneesIssueField.name=Assignees Field
IssueImportOption.assigneesIssueField.desc=Specify a multi-value user field to hold assignees information.<b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
IssueImportOption.issueLabelMappings.name=Issue Label Mappings
IssueImportOption.issueLabelMappings.desc=Specify how to map GitHub issue labels to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
IssueLabelMapping.githubIssueLabel.name=GitHub Issue Label
IssueLabelMapping.devgripIssueField.name=DevGrip Issue Field
IssueLabelMapping.devgripIssueField.desc=Specify a custom field of Enum type
ProjectImportOption.publicRole.name=Public Roles
ProjectImportOption.publicRole.desc=If specified, all public repositories imported from GitHub will use this as default roles. Private repositories are not affected
ProjectImportOption.importIssues.name=Import Issues
IssueImporter.title.step2=Choose repository
IssueImporter.title.chooseProject=Choose project
IssueImporter.title.step3=Specify import option
IssueImporter.import.starting=Importing issues from repository %s ...
ImportResult.andMore=and more
ImportResult.note=<br><br><b>NOTE:</b><ul>
ImportResult.nonExistentIterations=Non existent iterations
ImportResult.notMaps=%s issue labels not mapped to DevGrip custom field
ImportResult.accountCanNotBeMaps=%s logins without public email or public email can not be mapped to DevGrip account
ImportResult.attachmentsAreNotImported=<li> Attachments in issue description and comments are not imported due to GitHub limitation
ImportGroup.groupId.name=GitLab Group
ImportGroup.groupId.desc=Specify group to import from. Leave empty to import from projects under current account
ImportProject.project.name=GitLab Project
ImportProject.project.desc=Select project to import from
ImportProjects.parentProject.name=Parent DevGrip Project
ImportProjects.parentProject.desc=Optionally specify a DevGrip project to be used as parent of imported projects. Leave empty to import as root projects
ImportProjects.all.name=Import All Projects
ImportProjects.includeForks.name=Include forks
ImportProjects.includeForks.desc=Whether to import forked GitLab projects
ImportProjects.gitlabProjects.name=GitLab Projects to Import
ImportResult.tooLargeAttachments=Too large attachments
ImportResult.downloadFailed=Failed to download attachments
GitLabIssueImportOption.closedState.desc=Specify which issue state to use for closed GitLab issues.<br><b>NOTE: </b> You may customize DevGrip issue states in case there is no appropriate option here
GitLabIssueImportOption.assigneesIssueField.desc=Specify a multi-value user field to hold assignees information.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GitLabIssueImportOption.dueDateIssueField.name=Due Date Field
GitLabIssueImportOption.dueDateIssueField.desc=Optionally specify a date field to hold due date information.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GitLabIssueImportOption.estimatedTimeField.name=Estimated Time Field
GitLabIssueImportOption.estimatedTimeField.desc=Optionally specify a working period field to hold estimated time infomration.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GitLabIssueImportOption.spentTimeField.name=Spent Time Field
GitLabIssueImportOption.spentTimeField.desc=Optionally specify a working period field to hold spent time infomration.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GitLabIssueImportOption.issueLabelMappings.desc=Specify how to map GitLab issue labels to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GitLabIssueLabelMapping.issueLabel.name=GitLab Issue Label
GitLabIssueLabelMapping.devgripIssueField.name=DevGrip Issue Field
GitLabIssueLabelMapping.devgripIssueField.desc=Specify a custom field of Enum type
GitLabProjectImportOption.publicRole.desc=If specified, all public and internal projects imported from GitLab will use this as default role. Private projects are not affected
JiraCloudImportOption.assigneeIssueField.name=Assignees Field
JiraCloudImportOption.assigneeIssueField.desc=Specify a multi-value user field to hold assignees information.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportOption.dueDateField.name=Due Date Field
JiraCloudImportOption.dueDateField.desc=Optionally specify a date field to hold due date information.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportOption.timeSpentField.name=Time Spent Field
JiraCloudImportOption.timeSpentField.desc=Optionally specify a working period field to hold time spent infomration.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportOption.estimatedField.name=Time estimated field
JiraCloudImportOption.estimatedField.desc=Optionally specify a working period field to hold time estimate infomration.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportOption.issueStatusMapping.name=Issue Status Mappings
JiraCloudImportOption.issueStatusMapping.desc=Specify how to map JIRA issue statuses to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue states in case there is no appropriate option here
JiraCloudImportOption.issueTypeMapping.name=Issue Type Mappings
JiraCloudImportOption.issueTypeMapping.desc=Specify how to map JIRA issue types to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportOption.issuePriorityMapping.name=Issue Type Mappings
JiraCloudImportOption.issuePriorityMapping.desc=Specify how to map JIRA issue priorities to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
JiraCloudImportProject.project.name=JIRA Project
JiraCloudImportProject.project.desc=Choose JIRA project to import issues from
JiraCloudImportProjects.parentProject.name=Parent DevGrip Project
JiraCloudImportProjects.parentProject.desc=Optionally specify a DevGrip project to be used as parent of imported projects. Leave empty to import as root projects
JiraCloudImportProjects.projectToImport.name=JIRA Projects to Import
JiraCloudImportProjects.error.validate=No permission to import as root projects, please specify parent project
JiraCloudImportResult.error.customFieldsNotImported=<li> JIRA issue custom fields are not imported
JiraCloudImportResult.error.statusNotMapped=JIRA issue statuses not mapped to DevGrip custom field
JiraCloudImportResult.error.typesNotMapped=JIRA issue types not mapped to DevGrip custom field
JiraCloudImportResult.error.prioritiesNotMapped=JIRA issue priorities not mapped to DevGrip custom field
JiraCloudImportResult.error.accountsNotAvailable=JIRA accounts not available in DevGrip (matching by full name)
IssuePriorityMapping.issuePriority.name=JIRA Issue Priority
IssuePriorityMapping.devgripIssueField.name=DevGrip Issue Field
IssuePriorityMapping.devgripIssueField.desc=Specify a custom field of Enum type
IssueStatusMapping.issuePriority.name=JIRA Issue Status
IssueStatusMapping.issueState.name=DevGrip Issue State
IssueTypeMapping.issueType.name=JIRA Issue Type
IssueTypeMapping.issueField.name=DevGrip Issue Field
IssueTypeMapping.issueField.desc=Specify a custom field of Enum type
GiteaIssueImporter.stepAuth.title=Authenticate to Gitea
GiteaIssueImporter.stepChooseRepo.title=Choose repository
GiteaIssueImporter.stepSpecifyImportOption.title=Specify import option
GiteaIssueImporter.log.importing=Importing issues from repository %s ...
GiteaImportServer.apiUrl.name=Gitea API URL
GiteaImportServer.apiUrl.desc=Specify Gitea API url, for instance <tt>https://gitea.example.com/api/v1</tt>
GiteaImportServer.gpat.name=Gitea Personal Access Token
GiteaImportServer.error.issueFieldDup=Duplicate issue field mapping (issue: %s, field: %s)
GiteaImportServer.error.fieldNotFound=No field spec found: %s
GiteaImportServer.logInfo.importedIssues=Imported %d issues
GiteaImportServer.logInfo.importingFrom=Importing from '%s' to '%s'...
GiteaImportServer.error.targetAlreadyExists=Import target already exists. You need to have project management privilege over it
GiteaImportServer.logInfo.cloneCode=Cloning code...
GiteaImportServer.logWarn.skipCloneCode=Skipping code clone as the project already has code
GiteaImportServer.logInfo.importingMilestones=Importing milestones...
GiteaImportServer.logInfo.importingIssues=Importing issues...
GiteaImportServer.importedSuccess=Repositories imported successfully
GiteaImportServer.error.authFailed=Authentication failed
GiteaImportServer.error.connectingFailed=Error connecting api service
GiteaProjectImporter.stepAuth.title=Authenticate to Gitea
GiteaProjectImporter.stepSpecifyRepo.title=Specify repositories
GiteaProjectImporter.stepSpecifyImportOption.title=Specify import option
GiteaImportOrganization.org.name=Gitea Organization
GiteaImportOrganization.org.desc=Select organization to import from. Leave empty to import from repositories under current account
GiteaImportOrganization.includeForks.name=Include Forks
GiteaImportOrganization.includeForks.desc=Whether to include forked repositories
GiteaImportRepositories.parent.name=Parent DevGrip Project
GiteaImportRepositories.parent.desc=Optionally specify a DevGrip project to be used as parent of imported repositories. Leave empty to import as root projects
GiteaImportRepositories.importAll.name=Import All Repositories
GiteaImportRepositories.includeForks.name=Include Forks
GiteaImportRepositories.includeForks.desc=Whether to import forked Gitea repositories
GiteaImportRepositories.reposToImport.name=Gitea Repositories to Import
GiteaImportRepository.repo.name=Gitea Repository
GiteaImportRepository.repo.desc=Select repository to import from
GiteaImportResult.andMore=and more
GiteaImportResult.notice=<br><br><b>NOTE:</b><ul>
GiteaImportResult.noExistsIterations=Non existent iterations
GiteaImportResult.fieldNotMapped=Gitea issue labels not mapped to DevGrip custom field
GiteaImportResult.accountNotMapped=Gitea logins without email or email can not be mapped to DevGrip account
GiteaImportResult.attachmentsAndCommentsAreNotImported=<li> Attachments in issue description and comments are not imported as Gitea does not provide attachment api currently
GiteaImportResult.issueDependenciesAreNotImported=<li> Issue dependencies are not imported as Gitea does not provide public api to access this information
GiteaIssueImportOption.closedIssueState.name=Closed Issue State
GiteaIssueImportOption.closedIssueState.desc=Specify which issue state to use for closed Gitea issues.<br><b>NOTE: </b> You may customize DevGrip issue states in case there is no appropriate option here
GiteaIssueImportOption.assigneesIssueField.name=Assignees Issue Field
GiteaIssueImportOption.assigneesIssueField.desc=Specify a multi-value user field to hold assignees information.<b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GiteaIssueImportOption.dueDateIssueField.name=Due Date Issue Field
GiteaIssueImportOption.dueDateIssueField.desc=Optionally specify a date field to hold due date information.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GiteaIssueImportOption.issueLabelMappings.name=Due Date Issue Field
GiteaIssueImportOption.issueLabelMappings.desc=Specify how to map Gitea issue labels to DevGrip custom fields.<br><b>NOTE: </b> You may customize DevGrip issue fields in case there is no appropriate option here
GiteaIssueLabelMapping.giteaIssueLabel.name=Gitea Issue Label
GiteaIssueLabelMapping.myIssueField.name=DevGrip Issue Field
GiteaIssueLabelMapping.myIssueField.desc=Specify a custom field of Enum type
GiteaProjectImportOption.publicRole.name=Public Roles
GiteaProjectImportOption.publicRole.desc=If specified, all public repositories imported from Gitea will use this as default roles. Private repositories are not affected
GiteaProjectImportOption.importIssues.name=Import Issues
NewIssuePage.topTile=<span class='text-nowrap'>Create Issue</span>
IssueBoardsPage.board=Board
IssueBoardsPage.backlogTitle=Show issues not scheduled into current iteration
IssueBoardsPage.iteration=Iteration
IssueBoardsPage.backlog=Backlog
IssueBoardsPage.noIssueBoard=No issue boards defined
AddNew=Add New
IssueBoardsPage.firstBoardTips=The first board will be the default board
IssueBoardsPage.defaultBadge=default
IssueBoardsPage.addNewBoard=Add New Board
IssueBoardsPage.useDefaultBoard=Use Default Boards
IssueBoardsPage.burndownChart=Burndown chart
IssueBoardsPage.inherited=Inherited
IssueBoardsPage.showClosed=Show Closed
IssueBoardsPage.hideClosed=Hide Closed
IssueBoardsPage.unscheduledIssues=Unscheduled Issues
IssueBoardsPage.allIssues=All Issues
IssueBoardsPage.createIteration=Create Iteration
IssueBoardsPage.close=Close
IssueBoardsPage.reopen=Reopen
IssueBoardsPage.useDefaultConfirm=This will discard all project specific boards, do you want to continue?
IssueBoardsPage.deleteBoardConfirm=Do you really want to delete this board?
IssueBoardsPage.unscheduled=<i>Unscheduled</i>
IssueBoardsPage.iterationDue=Iteration is due
IssueBoardsPage.iterationCloseSuccess=Iteration '${name}' is closed
IssueBoardsPage.iterationReopenSuccess=Iteration '${name}' is reopened
IssueBoardsPage.iterationDeleteSuccess=Iteration '${name}' is deleted
IssueBoardsPage.editIteration=Edit Iteration
IssueBoardsPage.deleteIterationConfirm=Do you really want to delete this iteration?
IssueBoardsPage.createIterationModalTitle=Create Iteration
IssueBoardsPage.backlogFilter.placeholder=Filter backlog issues
IssueBoardsPage.filter.placeholder=Filter issues
IssueBoardsPage.pageTitle=Issue Boards
IssueBoardsPage.errorParseQuery=Error parsing %squery: 
IssueBoardsPage.malformedQuery=Malformed %squery
IssueBoardsPage.errorParseBaseQuery=Error parsing %sbase query: 
IssueBoardsPage.malformedBaseQuery=Malformed %sbase query
IssueBoardsPage.queryMessageWithBacklog=backlog 
NewBoardPanel.newBoard=New Board
NewBoardPanel.error.nameAlreadyUsed=This name has already been used by another issue board in the project
NewBoardPanel.createSuccess=New issue board created
CardDetailPanel.recentCommitsFixingTheIssue=This page lists recent commits fixing the issue
BoardColumnPanel.addNewBoardTitle=Add new card to this column
BoardColumnPanel.addAllCards=Add all cards to specified iteration
BoardColumnPanel.showTotalSpentTime=Show total estimated/spent time
BoardColumnPanel.showIssues=Show issues in list
BoardColumnPanel.noValueWithI=<i>No value</i>
BoardCardPanel.showIssueDetails=Click to show issue details
AddToIterationBean.name=Add Issues to Iteration
AddToIterationBean.iteration.desc=Select iteration to schedule issues into
AddToIterationBean.iteration.name=Iteration
AddToIterationBean.sendNotification.desc=Whether to send notifications to issue watchers for this change
AddToIterationBean.sendNotification.name=Send Notifications
AddToIterationBean.removeFromCurrentIteration.name=Remove From Current Iteration
IterationEditPage.saveSuccess=Iteration saved
IterationDetailPage.iteration=Iteration 
IterationDetailPage.burndown=Burndown
NewIterationPage.createSuccess=New iteration created
IterationBurndownPage.showStatesBy=Show States By
IterationListPanel.open=Open
IterationListPanel.close=Closed
IterationListPanel.sort=Sort 
IterationListPanel.dueDate=Due Date
IterationListPanel.issueStats=Issue Stats
IterationDateLabel.noStartOrNoDue=<i> No start/due date</i>
IterationDateLabel.startDateSpan=<span title='Start date'>
IterationDateLabel.dueDateSpan=<span title='Due date'>
IterationBurndownPanel.error.daystooLong=Iteration spans too long to show burndown chart
IterationBurndownPanel.error.startDateShouldBeforeDueDate=Iteration start date should be before due date
IterationBurndownPanel.error.startOrDueNotSpicified=Iteration start and due date should be specified to show burndown chart
IterationBurndownPanel.error.avoidDup=To avoid duplication, estimated/remaining time showing here does not include those aggregated from '${name}'
IterationActinosPanel.reopenTitle=Reopen this iteration
IterationActinosPanel.closeTitle=Close this iteration
IterationActinosPanel.editTitle=Edit this iteration
IterationActinosPanel.deleteTitle=Delete this iteration
IterationActinosPanel.reopenSuccess=Iteratioin '${name}' reopened
IterationActinosPanel.closeSuccess=Iteratioin '${name}' closed
IterationActinosPanel.deleteSuccess=Iteratioin '${name}' deleted
IterationActinosPanel.deleteConfirm=Do you really want to delete iteration '${name}'?
IterationSort.closestDueDate=Closest due date
IterationSort.furthestDueDate=Furthest due date
IterationSort.name=Name
IterationSort.nameReversely=Name reversely
IterationEditBean.startDate.name=Start Date
IterationEditBean.dueDate.name=Due Date
IterationEditBean.error.namePrefixMustWith=Name must prefix with: 
IterationEditBean.error.nameAlreadyUsed=Name has already been used by another iteration in the project hierarchy
BurndownIndicators.issueCount=Issue Count
BurndownIndicators.remainTime=Remaining Time
BurndownIndicators.estimatedTime=Estimated Time
IterationStatusName.open=Open
IterationStatusName.closed=Closed
IterationBurndownPanel.guideLine=Guide Line
AvatarEditPage.title=Current avatar
AvatarEditPage.toptitle=Edit Avatar
UserAuthorizationsPage.tips=When authorize a user, the user will also be authorized with assigned roles for all child projects
GroupAuthorizationsPage.tips=When authorize a group, the group will also be authorized with assigned roles for all child projects
UserAuthorizationBean.username.name=User
UserAuthorizationsPage.submitError.unableToApplyChanges=Unable to apply change as otherwise you will not be able to manage this project
UserAuthorizationsPage.submitError.dupAuth=Duplicate authorizations found: 
UserAuthorizationsPage.submitSuccess=User authorizations updated
UserAuthorizationsPage.topTitle=User Authorizations
GroupAuthorizationBean.groupname.name=Group
GroupAuthorizationsPage.submitSuccess=Group authorizations updated
BranchProtectionsPage.tips=Define branch protection rules. Rules defined in parent project are considered to be defined after rules defined here. For a given branch and user, the first matching rule will take effect
BranchProtectionsPage.editRule=Edit Rule
BranchProtectionsPage.addRule=Add Rule
GroupAuthorizationsPage.topTitle=Group Authorizations
BranchProtectionPanel.deleteConfirm=Do you really want to delete this protection?
BranchProtectionPanel.editThisRuleTitle=Edit this rule
BranchProtectionPanel.deleteThisRuleTitle=Delete this rule
BranchProtection.branches.name=Branches
BranchProtection.branches.desc=Specify space-separated branches to be protected. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
BranchProtection.users.name=Applicable Users
BranchProtection.users.desc=Rule will apply only if the user changing the branch matches criteria specified here
BranchProtection.preventForcePush.name=Prevent Forced Push
BranchProtection.preventForcePush.desc=Check this to prevent forced push
BranchProtection.preventBranchDeletion.name=Prevent Deletion
BranchProtection.preventBranchDeletion.desc=Check this to prevent branch deletion
BranchProtection.preventBranchCreation.name=Prevent Creation
BranchProtection.preventBranchCreation.desc=Check this to prevent branch creation
BranchProtection.requiredValidSign.name=Commit Signature Required
BranchProtection.requiredValidSign.desc=Check this to require valid signature of head commit
BranchProtection.enforceConventionalCommits.name=Enforce Conventional Commits
BranchProtection.enforceConventionalCommits.desc=Check this to require <a href='https://www.conventionalcommits.org' target='_blank'>conventional commits</a>. Note this is applicable for non-merge commits
BranchProtection.commitTypes.name=Commit Types
BranchProtection.commitTypes.placeholder=Arbitrary type
BranchProtection.commitTypes.desc=Optionally specify valid types of conventional commits (hit ENTER to add value). Leave empty to allow arbitrary type
BranchProtection.commitScopes.name=Commit Scopes
BranchProtection.commitScopes.placeholder=Arbitrary Scope
BranchProtection.commitScopes.desc=Optionally specify valid scopes of conventional commits (hit ENTER to add value). Leave empty to allow arbitrary scope
BranchProtection.checkCommitFooter.name=Check Commit Message Footer
BranchProtection.commitFooterPattern.name=Commit Message Footer Pattern
BranchProtection.commitFooterPattern.desc=A <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>Java regular expression</a> to validate commit message footer
BranchProtection.commitTypesForFooterCheck.name=Commit Types For Footer Check
BranchProtection.commitTypesForFooterCheck.desc=Optionally specify applicable commit types for commit message footer check (hit ENTER to add value). Leave empty to all types
BranchProtection.maxLength.name=Max Commit Message Line Length
BranchProtection.maxLength.placeholder=No limit
BranchProtection.requiredReviewers.name=Required Reviewers
BranchProtection.requiredReviewers.placeholder=No one
BranchProtection.requiredReviewers.desc=Optionally specify required reviewers for changes of specified branch
BranchProtection.requiredBuilds.name=Required Builds
BranchProtection.requiredBuilds.placeholder=No any
BranchProtection.requiredBuilds.desc=Optionally choose required builds
BranchProtection.fileProtections.name=File Protections
BranchProtection.fileProtections.desc=Optionally specify path protection rules
BranchProtection.requireStrictPrBuilds.name=Require Strict Pull Request Builds
BranchProtection.requireStrictPrBuilds.desc=When target branch of a pull request " has new commits, merge commit of the pull request will be recalculated, and this option tells whether to accept pull request builds ran on previous merged commit. If enabled, you will need to re-run required builds on the new merge commit. This setting takes effect only when required builds are specified
FileProtection.paths.name=Paths
FileProtection.paths.desc=Specify space-separated paths to be protected. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
FileProtection.reviewers.name=Reviewers
FileProtection.reviewers.desc=Specify required reviewers if specified path is changed. Note that the user submitting the change is considered to review the change automatically
FileProtection.validateError=Either reviewer or required builds should be specified
TagProtectionsPage.tips=Define tag protection rules. Rules defined in parent project are considered to be defined after rules defined here. For a given tag and user, the first matching rule will take effect
BooleanEditSupport.value.true=Yes
BooleanEditSupport.value.false=No
UserMatchEditSupport.value.anyone=anyone
TagProtection.tags.name=Tags
TagProtection.tags.desc=Specify space-separated tags to be protected. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
TagProtection.users.desc=Rule will apply if user operating the tag matches criteria specified here
TagProtection.preventUpdate.name=Prevent Update
TagProtection.preventUpdate.desc=Check this to prevent tag update
TagProtection.preventBranchDeletion.desc=Check this to prevent tag deletion
TagProtection.preventBranchCreation.desc=Check this to prevent tag creation
GitPackConfigPage.tips=For very large git repository, you may need to tune options here to reduce memory usage
GitPackConfig.windowMem.name=Window Memory
GitPackConfig.windowMem.desc=Optionally specify value of git config <code>pack.windowMemory</code> for the repository
GitPackConfig.packSizeLimit.name=Pack Size Limit
GitPackConfig.packSizeLimit.desc=Optionally specify value of git config <code>pack.packSizeLimit</code> for the repository
GitPackConfig.threads.name=Threads
GitPackConfig.threads.desc=Optionally specify value of git config <code>pack.threads</code> for the repository
GitPackConfig.window.name=Window
GitPackConfig.window.desc=Optionally specify value of git config <code>pack.window</code> for the repository
GitPackConfigPage.updateSuccess=Git pack config updated
PullRequestSettingPage.updateSuccess=Pull request settings updated
ProjectPullRequestSetting.defaultMergeStrategy.name=Default Merge Strategy
ProjectPullRequestSetting.defaultMergeStrategy.rootPlaceholder=Create merge commit
ProjectPullRequestSetting.defaultMergeStrategy.desc=Specify default merge strategy of pull requests submitted to this project
ProjectPullRequestSetting.defaultAssignee.name=Default Assignees
ProjectPullRequestSetting.defaultAssignee.rootPlaceholder=Not assigned
ProjectPullRequestSetting.defaultAssignee.desc=Specify default assignees of pull requests submitted to this project. Only users with the write code permission to the project can be selected
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.name=Delete Source Branch After Merge
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.rootPlaceholder=No
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.desc=If enabled, source branch will be deleted automatically after merge the pull request if user has permission to do that
ContributedProjectSettingPage.saveSuccess=Setting has been saved
CodeAnalysisSettingPage.submitSuccess=Code analysis settings updated
CodeAnalysisSetting.files.name=Files to Be Analyzed
InheritFromParent=Inherit from parent
CodeAnalysisSetting.files.rootPlaceholder=All files
CodeAnalysisSetting.files.desc=DevGrip analyzes repository files for code search, line statistics, and code contribution statistics. This setting tells which files should be analyzed, and expects space-separated <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path patterns</a>. A pattern can be excluded by prefixing with '-', for instance <code>-**/docs/**</code> will exclude all files with docs in path. <b>NOTE: </b> Changing this setting only affects new commits. To apply the change to history commits, please stop the server and delete folder <code>index</code> and <code>info/commit</code> under <a href='https://docs.devgrip.net/concepts#project-storage' target='_blank'>project's storage directory</a>. The repository will be re-analyzed when server is started
ServiceDeskSettingsPage.submitError=This address has already been used by another project
ServiceDeskSettingsPage.submitSuccess=Service desk settings updated
JobSecretsPage.tips=<svg class='icon mr-2'><use xlink:href='%s'/></svg> Define job secrets to be used in build spec. Secrets with <b>same name</b> can be defined. For a particular name, the first authorized secret with that name will be used (search in current project first, then search in parent projects). Note that secret value containing line breaks or less than <b>%d</b> characters will not be masked in build log
HideArchived=Hide Archived
ShowArchived=Show Archived
JobSecretsPage.deleteConfirm=Do you really want to delete secret '${name}'?
JobSecretsPage.deleteSuccess=Secret '${name}' deleted?
JobSecretsPage.archived=archived
JobSecret.authorization.name=Authorization
JobSecret.authorization.placeholder=Any job
JobSecret.authorization.desc=Optionally specify branches/users/groups allowed to access this secret. If left empty, any job can access this secret, including those triggered via external pull requests
JobSecret.archived.name=Archived
JobSecret.archived.desc=Mark a secret archived if it is no longer used by current build spec, but still need to exist to reproduce old builds. Archived secrets will not be shown by default
JobSecretEditPanel.editJobSecret=Edit Job Secret
SecretPropertyEditor.show=Show
SecretPropertyEditor.hide=Hide
JobPropertiesPage.tips=Define properties to be used in build spec. Properties will be inherited by child projects, and can be overridden by child properties with same name.
JobPropertiesPage.saveSuccess=Job properties saved
JobProperty.archived.desc=Mark a property archived if it is no longer used by current build spec, but still need to exist to reproduce old builds. Archived properties will not be shown by default
BuildPreservationPage.tips=Define rules to preserve builds. A build will be preserved as long as one rule defined here or in parent projects preserves it. All builds will be preserved if no rules are defined here and in parent projects
BuildPreservationPage.submitSuccess=Build preserve rules saved
BuildPreservation.condition.name=Condition
BuildPreservation.condition.desc=Specify the condition preserved builds must match
BuildPreservation.count.desc=Number of builds to preserve
BuildPreservation.count.name=Count
BuildPreservation.count.placeholder=Unlimited
DefaultFixedIssueFiltersPage.tips=For each build, System calculates a list of fixed issues since previous build automatically. This setting provides a default query to further filter/order this list. For a given job, the first matching entry will be used.
DefaultFixedIssueFiltersPage.submitSuccess=Default fixed issue filters saved
DefaultFixedIssueFilter.jobNames.name=Job Names
DefaultFixedIssueFilter.jobNames.desc=Specify space-separated jobs. Use '*' or '?' for wildcard match. Prefix with '-' to exclude
DefaultFixedIssueFilter.issueQuery.name=Issue Query
DefaultFixedIssueFilter.issueQuery.desc=Specify a default query to filter/order fixed issues of specified jobs
CacheManagementPage.preserveDays=Preserve Days
CacheManagementPage.uploadCaches=Uploaded Caches
CacheManagementPage.columnKey=Key
CacheManagementPage.columnSize=Size
CacheManagementPage.columnLastAccessed=Last Accessed
CacheManagementPage.deleteSuccess=Cache '${key}' deleted
CacheManagementPage.fileMissing=<i class='text-danger'>File missing or obsolete</i>
CacheManagementPage.topTitle=Job Cache Management
CacheSettingBean.preserveDays.desc=Cache will be deleted to save space if not accessed for this number of days
WebHooksPage.tips=For web hooks defined here and in parent projects, DevGrip will post event data in JSON format to specified URLs when subscribed events happen
WebHooksPage.submitSuccess=Web hooks saved
WebHook.postUrl.name=Post Url
WebHook.postUrl.desc=The URL of the server endpoint that will receive the webhook POST requests
WebHook.eventTypes.name=Event Types
WebHook.secret.name=Secret
WebHook.secret.desc=The secret which allows you to ensure that POST requests sent to the payload URL are from DevGrip. When you set a secret you'll receive the X-DevGrip-Signature header in the webhook POST request
BuildListPanel.queryOrOrder.placeholder=Query/order builds
BuildListPanel.runJobTitle=Run job
BuildListPanel.error.buildAlreadyFinished=Build #${number} already finished
BuildListPanel.error.buildNotFinishedYet=Build #${number} not finished yet
BuildListPanel.cancelSuccess=Cancel request submitted
BuildListPanel.cancelConfirm=Type <code>yes</code> below to cancel selected builds
BuildListPanel.cancelAllQueriedConfirm=Type <code>yes</code> below to cancel all queried builds
BuildListPanel.selectBuildsCancelTitle=Please select builds to cancel
BuildListPanel.allQueriedBuildsCancelTitle=No builds to cancel
BuildListPanel.reRunSuccess=Re-run request submitted
BuildListPanel.resubmitManually=Resubmitted manually
BuildListPanel.reRunConfirm=Type <code>yes</code> below to re-run selected builds
BuildListPanel.allQueriedReRunConfirm=Type <code>yes</code> below to re-run all queried builds
BuildListPanel.selectBuildsReRunTitle=Please select builds to re-run
BuildListPanel.allQueriedBuildsReRunTitle=No builds to re-run
BuildListPanel.deleteConfirm=Type <code>yes</code> below to delete selected builds
BuildListPanel.allQueriedDeleteConfirm=Type <code>yes</code> below to delete all queried builds
BuildListPanel.selectBuildsDeleteTitle=Please select builds to delete
BuildListPanel.allQueriedBuildsDeleteTitle=No builds to delete
BuildListPanel.countBuilds=Found ${count} builds
BuildListPanel.columnBuild=Build
BuildListPanel.columnDuration=Duration
BuildListPanel.columnOnBehalfOf=On Behalf Of
BuildListPanel.columnLastUpdate=Last Update
BuildListPanel.noraWithI=<i>n/a</i>
BuildListPanel.labelBranch=branch ${branch}
BuildListPanel.labelTag=tag ${tag}
BuildListPanel.labelPr=pull request #${pr}
BuildListPanel.labelTod=command line tool
BuildLogPanel.buildLog.tooManyLogs=Too many logs, showing only the latest ${num}
BuildLogPanel.buildLog.noLogs=No logs
BuildLogPanel.buildLog.total=${total} logs found
BuildLogPanel.buildLog.loadedCount=${total} logs loaded
BuildLogPanel.buildLog.loadingLogs=Loading logs...
BuildLogPanel.buildLog.allLogsloaded=All ${total} logs have been loaded
BuildLogPanel.buildLog.loadMore=Load more
BuildLogPanel.buildLog.executionPaused=Script execution paused
BuildLogPanel.buildLog.resume=Resume
MiniBuildListPanel.noBuilds=No builds
BuildMultiChoice.chooseBuilds=Choose builds...
BuildMultiChoice.chooseBuild=Choose build...
BuildMultiChoice.chooseBranches=Choose branches...
BuildMultiChoice.chooseBranch=Choose branch...
BranchPicker.chooseWithI=<i>choose</i>
InvalidBuildPage.h2=Commit of the build is missing
InvalidBuildPage.h2Tips=This might happen when project points to a wrong git repository, or the commit is garbage collected. 
InvalidBuildPage.missingCommit=Missing Commit
InvalidBuildPage.jobName=Job Name
InvalidBuildPage.deleteBuild=Delete Build
InvalidBuildPage.builds=Builds
InvalidBuildPage.deleteSuccess=Build #${build} deleted
InvalidBuildPage.entityNotFound=Unable to find build #%s in project %s
InvalidBuildPage.deleteConfirm=Do you really want to delete this build?
DescriptionBean.name=Build Description
BuildDetailPage.reRunTitle=Re-run this build
BuildDetailPage.cancelTitle=Cancel this build
BuildDetailPage.editTitle=Edit build description
BuildDetailPage.openTerminalTitle=Open terminal of current running step
BuildDetailPage.promotionsTitle=Promotions
BuildDetailPage.buildSpecNotFound=Build spec not found in commit of this build
BuildDetailPage.job=Job 
BuildDetailPage.notFound=\ associated with the build not found.
BuildDetailPage.importErrors=Most probably there are import errors in the 
BuildDetailPage.buildSpec=build spec
BuildDetailPage.rebuildConfirm=Do you really want to rebuild?
BuildDetailPage.cancelConfirm=Do you really want to cancel this build?
BuildDetailPage.interactiveWebShell=Interactive web shell access to running jobs is an enterprise feature. <a href='https://devgrip.net/pricing' target='_blank'>Try free</a> for 30 days
BuildDetailPage.columnLog=Log
BuildDetailPage.columnPipeline=Pipeline
BuildDetailPage.columnArtifacts=Artifacts
BuildDetailPage.columnFixedIssues=Fixed Issues
BuildDetailPage.columnChanges=Changes
BuildDetailPage.pageTitle.build=Build
BuildLogPage.downloadFullLog=Download full log
ArtifactUploadPanel.relativeDir=Optionally specify relative directory to put uploaded files
ArtifactUploadPanel.error.dirNotAllowed='..' is not allowed in the directory
BuildArtifactsPage.noArtifacts=No artifacts published
BuildArtifactsPage.columnlastModified=Last Modified
BuildArtifactsPage.deleteFileConfirm=Do you really want to delete this file?
BuildArtifactsPage.deleteDirConfirm=Do you really want to delete this directory?
FixedIssuesPage.tips=No previous successful build on same build stream to calculate fixed issues since
BuildReportTabOptionPanel.viewStatTitle=View statistics
JobInfoButton.runJobTitle=Run this job
RunJobLink.submitManually=Submitted manually
RunJobLink.rebuildManually=Rebuild manually
RunJobLink.noRefsBuildOnBehalf=No refs to build on behalf of
JobMultiChoice.chooseJobs=Choose jobs...
JobSingleChoice.chooseJob=Choose job...
BuildOptionContentPanel.h5=Specify Build Options
BuildOptionContentPanel.buildOnBehalf=Build On Behalf Of
BuildOptionContentPanel.tips=For each selected branch/tag, a separate build will be generated with branch/tag set to corresponding value
BuildOptionContentPanel.errorValid=At least one branch or tag should be selected
JobRunSelector.searchJob=Search job
JobRunSelector.selectJob=Select Job
JobRunSelector.noJobsFound=No jobs found
EntityNavPanel.entityName.build=build
###在英文语境下，要用个空格当分隔符###
HumanDuration.delimiter=\ 
HumanDuration.na=n/a
HumanDuration.noTime=No time.
HumanDuration.year=%d years
HumanDuration.month=%d months
HumanDuration.week=%d weeks
HumanDuration.day=%d days
HumanDuration.hour=%d hours
HumanDuration.minute=%d minutes
HumanDuration.second=%d seconds
GpgSignatureVerifier.error.notVerified=Not a verified email of signing GPG key
GpgSignatureVerifier.error.invalid=Invalid GPG signature
GpgSignatureVerifier.error.unknownKey=Signed with an unknown GPG key (key ID: %s)
GpgSignatureVerifier.error.withoutNecessaryData=Looks like a GPG signature but without necessary data
GpgSignatureVerifier.error.couldNotVerifying=Error verifying GPG signature
RunTaskBehavior.inProgress=in progress...
DefaultJobManager.joblog.autoDiscovering=No job executor defined, auto-discovering...
DefaultJobManager.joblog.discoveredExecutor=Discovered job executor type: %s
DefaultJobManager.joblog.finished=Job finished
DefaultJobManager.joblog.timeout=Job execution timed out
DefaultJobManager.joblog.error=Error executing job
DefaultJobManager.joblog.lockingGroup=Locking sequential group...
DefaultJobManager.joblog.retryAfter=Job will be retried after a while...
DefaultJobManager.joblog.dependenciesRequireSuccessful=Some dependencies are required to be successful but failed
BuildSpecBlobViewPanel.jobs=Jobs
BuildSpecBlobViewPanel.services=Services
BuildSpecBlobViewPanel.stepTemplates=Step Templates
BuildSpecBlobViewPanel.properties=Properties
BuildSpecBlobViewPanel.imports=Imports
BuildSpecBlobViewPanel.errorParseBuildSpec=Error parsing build spec
BuildSpecBlobViewPanel.this=This
BuildSpecBlobViewPanel.importFrom=is imported from
BuildSpecBlobViewPanel.propertyImportFrom=This property is imported from ${path}
BuildSpecBlobViewPanel.noProperties=No properties defined
BuildSpecBlobViewPanel.noJobs=No jobs defined
BuildSpecBlobViewPanel.noServices=No services defined
BuildSpecBlobViewPanel.noStepTemplates=No step templates defined
BuildSpecBlobViewPanel.noImports=No imports defined
BuildSpecBlobViewPanel.noBuildSpec=Build spec not defined
BuildSpecBlobViewPanel.errorBuildSpecWithProperty=Error validating build spec (location: %s, error message: %s)
BuildSpecBlobViewPanel.errorBuildSpec=Error validating build spec: %s
BuildSpecEditPanel.suggestions=Quick Generate
BuildSpecEditPanel.noNameWithI=<i>No Name</i>
BuildSpec.malformed=Malformed build spec
Job.name.desc=Specify name of the job
Job.jobExecutor.name=Job Executor
Job.jobExecutor.desc=Optionally specify executor for this job. Leave empty to use first matching executor (or use auto-discovered executor if no executors are defined)
Job.autoDiscoveredExecutor=Auto-discovered executor
Job.firstApplicableExecutor=First applicable executor
Job.steps.name=Steps
Job.steps.desc=Steps will be executed serially on same node, sharing the same <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>
Job.paramSpec.name=Parameter Specs
Job.paramSpec.group=Params & Triggers
Job.paramSpec.desc=Optionally define parameter specifications of the job
Job.triggers.name=Triggers
Job.triggers.desc=Use triggers to run the job automatically under certain conditions
Job.jobDependence.name=Job Dependencies
Job.jobDependence.group=Dependencies & Services
Job.jobDependence.desc=Job dependencies determines the order and concurrency when run different jobs. You may also specify artifacts to retrieve from upstream jobs
Job.projectDependence.name=Project Dependencies
Job.projectDependence.desc=Use project dependency to retrieve artifacts from other projects
Job.requiredService.name=Required Services
Job.requiredService.placeholder=No required services
Job.requiredService.desc=Optionally specify services required by this job. <b class='text-warning'>NOTE:</b> Services are only supported by docker aware executors (server docker executor, remote docker executor, or kubernetes executor)
Job.sequentialGroup.name=Sequential Group
Job.sequentialGroup.group=More Settings
Job.sequentialGroup.desc=Jobs with same sequential group and executor will be executed sequentially. For instance, you may specify this property as <tt>@project_path@:prod</tt> for jobs executing by same executor and deploying to prod environment of current project to avoid conflicting deployments
Job.retryCondition.name=Retry Condition
Job.retryCondition.desc=Specify condition to retry build upon failure
Job.maxRetries.name=Max Retries
Job.maxRetries.desc=Maximum of retries before giving up
Job.retryDelay.name=Retry Delay
Job.retryDelay.desc=Delay for the first retry in seconds. Delay of subsequent retries will be calculated using an exponential back-off based on this value
Job.timeout.name=Timeout
Job.timeout.desc=Specify timeout in seconds. It counts from the time when job is submitted
Job.postBuildActions.name=Post Build Actions
PostBuildAction.name=Post Build Action
PostBuildAction.condition.name=Condition
PostBuildAction.condition.desc=Specify the condition current build must satisfy to execute this action
RunJobAction.name=Run job
SendNotificationAction.name=Send notification
SendNotificationAction.receivers.name=Receivers
CreateIssueAction.name=Create issue
CreateIssueAction.project.placeholder=Current project
CreateIssueAction.project.desc=Optionally Specify project to create issue in. Leave empty to create in current project
CreateIssueAction.accessTokenSecret.desc=Specify a job secret to be used as access token to create issue in above project if it is not publicly accessible
CreateIssueAction.accessTokenSecret.name=Access Token Secret
CreateIssueAction.issueTitle.name=Title
CreateIssueAction.issueTitle.group=Issue Details
CreateIssueAction.issueTitle.desc=Specify title of the issue
CreateIssueAction.issueDescription.desc=Optionally specify description of the issue
CreateIssueAction.confidential.name=Confidential
CreateIssueAction.confidential.desc=Whether the issue should be confidential
PostBuildActionListEditPanel.addNewTitle=Add new post-build action
PostBuildActionListViewPanel.actionTypeTitle=Post Build Action (type: ${actionType})
NotFoundWithI=<i>Not Found</i>
ImportListEditPanel.tips=Import build spec elements (jobs, services, step templates and properties) from other projects. Imported elements are treated as if they are defined locally. Locally defined elements will override imported elements with same name
ImportListEditPanel.addNewImport=Add new import
JobDependencyEditPanel.error.alreadyDefined=Dependency to this job is already defined
JobDependencyEditPanel.jobDependency=Job Dependency
JobDependencyListEditPanel.columnRequireSucc=Require Successful
JobDependencyListEditPanel.columnParamsCount=Params Count
JobDependencyListEditPanel.addNew=Add new job dependency
ProjectDependency.projectDependency=Project Dependency
ProjectDependencyListEditPanel.addNew=Add new project dependency
ProjectDependencyListEditPanel.columnBuild=Build
JobTriggerEditPanel.jobTrigger=Job Trigger
JobTriggerListEditPanel.addNew=Add new trigger
JobTriggerListViewPanel.triggerType=Trigger (type: ${triggerType})
ParamSpecEditPanel.error.nameAlreadyUsed=This name has already been used by another parameter
ParamSpecEditPanel.parameterDefinition=Parameter Definition
ParamSpecListEditPanel.addNew=Add new param
ParamSpecListViewPanel.paramType=Parameter Spec (type: ${paramType})
StepListEditPanel.addNew=Add new step
StepListEditPanel.moreOps=More operations
VariableInterpolator.helpText=<b>Tips: </b> Type <tt>@</tt> to <a href='https://docs.devgrip.net/appendix/job-variables' target='_blank' tabindex='-1'>insert variable</a>. Use <tt>@@</tt> for literal <tt>@</tt>
VariableInterpolator.helpTextDot=. 
BuildSidePanel.countBuilds=${count} build(s)
HttpCredential.accessTokenSecret.name=Access Token Secret
HttpCredential.accessTokenSecret.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as access token
HttpCredential.error.notFound=Secret not found (%s)
SshCredential.keySecret.name=Key Secret
SshCredential.keySecret.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as SSH private key
DefaultCredential.name=Default
CreateIssueAction.description.value=Create issue
CreateIssueAction.error.validating=Error validating issue fields: 
RunJobAction.description.value=Run job '%s'
RunJobAction.submitReason=Post build action of job '%s'
RunJobAction.error.validating=Error validating job parameters (job: %s, error message: %s)
RunJobAction.error.jobNotFound=Job not found (%s)
SendNotificationAction.description.value=Send notification to %s
LastFinishedBuild.name=Last Finished of Specified Project
LastFinishedBuild.jobname.placeholder=please choose job name...
LastFinishedBuild.refName.name=Ref Name
LastFinishedBuild.refName.placeholder=Any ref
LastFinishedBuild.refName.desc=Optionally specify ref of above job, for instance <i>refs/heads/main</i>. Use * for wildcard match
LastFinishedBuild.descriptionWithRef=Last finished of job '%s' on ref '%s'
LastFinishedBuild.description=Last finished of job '%s'
ProjectDependency.projectPath.desc=Specify project to retrieve artifacts from
ProjectDependency.artifacts.name=Artifacts to Retrieve
ProjectDependency.artifacts.desc=Specify artifacts to retrieve into <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. Only published artifacts (via artifact publish step) can be retrieved.
ProjectDependency.destProject.name=Destination Path
ProjectDependency.destProject.placeholder=Job workspace
ProjectDependency.destProject.desc=Optionally specify a path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to put retrieved artifacts. Leave empty to use job workspace itself
ProjectDependency.accessTokenSecret.name=Access Token Secret
ProjectDependency.accessTokenSecret.placeholder=Access Anonymously
ProjectDependency.accessTokenSecret.desc=Specify a secret to be used as access token to retrieve artifacts from above project. If not specified, project artifacts will be accessed anonymously
SpecifiedBuild.name=Specify by Build Number
SpecifiedBuild.buildNumber.placeholder=Build Number
BlobViewPanel.linesLabel=${count} lines
JobDependency.requireSuccessful.name=Require Successful
JobDependency.requireSuccessful.desc=Whether to require this dependency to be successful
JobDependency.artifacts.desc=Optionally specify artifacts to retrieve from the dependency into <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. Only published artifacts (via artifact publish step) can be retrieved. Leave empty to not retrieve any artifacts
JobDependency.artifacts.name=Artifacts to Retrieve
JobDependency.artifacts.placeholder=Do not retrieve
JobDependency.destPath.desc=Optionally specify a path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to put retrieved artifacts. Leave empty to use job workspace itself
JobDependency.destPath.name=Destination Path
JobDependency.destPath.placeholder=Job workspace
Job.error.dupDp=Duplicate dependency (%s)
Job.error.dupParam=Duplicate parameter spec (%s)
Job.error.malformedRc=Malformed retry condition
Job.error.validating=Error validating job parameters (item: #%s, error message: %s)
JobTrigger.project.desc=Optionally specify space-separated projects applicable for this trigger. This is useful for instance when you want to prevent the job from being triggered in forked projects. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all projects
ExcludeParamCombos=Exclude Param Combos
BranchUpdateTrigger.bu.name=Branch update
BranchUpdateTrigger.bu.desc=Job will run when code is committed. <b class='text-info'>NOTE:</b> This trigger will ignore commits with message containing <code>[skip ci]</code>, <code>[ci skip]</code>, <code>[no ci]</code>, <code>[skip job]</code>, <code>[job skip]</code>, or <code>[no job]</code>
BranchUpdateTrigger.branches.desc=Optionally specify space-separated branches to check. Use '**' or '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all branches
BranchUpdateTrigger.paths.name=Touched Files
BranchUpdateTrigger.paths.placeholder=Any file
BranchUpdateTrigger.paths.desc=Optionally specify space-separated files to check. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all files
BranchUpdateTrigger.reason=Branch '%s' is updated
BranchUpdateTrigger.desc1=When update branches '%s' and touch files '%s'
BranchUpdateTrigger.desc2=When update branches '%s'
BranchUpdateTrigger.desc3=When touch files '%s'
BranchUpdateTrigger.desc4=When update branches
DependencyFinishedTrigger.name=Dependency job finished
DependencyFinishedTrigger.reason=Dependency job '%s' is finished
DependencyFinishedTrigger.desc=When dependency jobs finished
IssueInStateTrigger.name=Issue in state
IssueInStateTrigger.desc=Job will run on head commit of default branch
IssueInStateTrigger.state.name=State
Any_Issue=Any issue
JobTrigger.descInProject=\ in projects '%s'
IssueInStateTrigger.reason=Issue state is '%s'
IssueInStateTrigger.desc1=When issue is in state '%s' and matches: %s
IssueInStateTrigger.desc2=When issue is in state '%s'
PullRequestDiscardTrigger.name=Pull request discard
PullRequestDiscardTrigger.desc=Job will run on head commit of target branch
PullRequestDiscardTrigger.reason=Pull request is discarded
PullRequestDiscardTrigger.action=discard
PullRequestMergeTrigger.name=Pull request merge
PullRequestMergeTrigger.desc=Job will run on merge commit of target branch and source branch
PullRequestMergeTrigger.reason=Pull request is merged
PullRequestMergeTrigger.action=merge
PullRequestUpdateTrigger.name=Pull request open or update
PullRequestUpdateTrigger.desc=Job will run on merge commit of target branch and source branch.<br><b class='text-info'>NOTE:</b> Unless required by branch protection rule, this trigger will ignore commits with message containing <code>[skip ci]</code>, <code>[ci skip]</code>, <code>[no ci]</code>, <code>[skip job]</code>, <code>[job skip]</code>, or <code>[no job]</code>
PullRequestUpdateTrigger.reason=Pull request is opened/updated
PullRequestUpdateTrigger.action=open/update
PullRequestTrigger.desc1=When %s pull requests targeting branches '%s' and touching files '%s'
PullRequestTrigger.desc2=When %s pull requests targeting branches '%s'
PullRequestTrigger.desc3=When %s pull requests touching files '%s'
PullRequestTrigger.desc4=When %s pull requests
ScheduleTrigger.name=Cron schedule
ScheduleTrigger.CronExp.name=Cron Expression
ScheduleTrigger.CronExp.desc=Specify a <a target='_blank' href='http://www.quartz-scheduler.org/documentation/quartz-2.3.0/tutorials/crontrigger.html#format'>cron schedule</a> to fire the job automatically. <b class='text-info'>Note:</b> To save resource, seconds in cron expression will be ignored, and the minimum schedule interval is one minute
ScheduleTrigger.branches.placeholder=Default branch
ScheduleTrigger.branches.desc=Optionally specify space-separated branches applicable for this trigger. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty for default branch
ScheduleTrigger.reason=Scheduled
ScheduleTrigger.desc1=Schedule at %s for branches '%s'
ScheduleTrigger.desc2=Schedule at %s for default branch
TagCreateTrigger.name=Tag creation
TagCreateTrigger.tags.placeholder=Any tag
TagCreateTrigger.tags.desc=Optionally specify space-separated tags to check. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all tags
TagCreateTrigger.branches.name=On Branches
TagCreateTrigger.branches.placeholder=Any branch
TagCreateTrigger.branches.desc=This trigger will only be applicable if tagged commit is reachable from branches specified here. Multiple branches should be separated with spaces. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to match all branches
TagCreateTrigger.reason=Tag '%s' is created
TagCreateTrigger.desc1=When create tags '%s' on branches '%s'
TagCreateTrigger.desc2=When create tags '%s'
TagCreateTrigger.desc3=When create tags on branches '%s'
TagCreateTrigger.desc4=When create tags
ParamIgnoreValue.name=Ignore this param
ParamSpecifiedValue.name=Use specified value or job secret
ParamSpecifiedValue.dn=Use specified value
ParamSpecifiedValue.sdn=Use specified job secret
ParamSpecifiedValues.name=Use specified values or job secrets
ParamSpecifiedValues.dn=Use specified values
ParamSpecifiedValues.sdn=Use specified job secrets
ParamScriptingValue.name=Evaluate script to get value or secret
ParamScriptingValue.dn=Evaluate script to get value
ParamScriptingValue.sdn=Evaluate script to get secret
ParamScriptingValues.name=Evaluate script to get values or job secrets
ParamScriptingValues.dn=Evaluate script to get values
ParamScriptingValues.sdn=Evaluate script to get job secrets
ParamPassthroughValue.name=Use value of specified parameter/secret
ParamPassthroughValue.dn=Use value of specified parameter
ParamUtils.validating.atLeastOneValueNeed=At least one value needs to be specified
ParamUtils.validating.DupValueNotAllow=Duplicate values not allowed
ParamUtils.validating.paramValuesError=Error validating param values (param: %s, error message: %s)
ParamUtils.validating.paramValueError=Error validating param value (param: %s, value: %s, error message: %s)
ParamUtils.validating.secretNotFound=Secret not found
ParamUtils.validating.missingJobParameter=Missing job parameter (%s)
ParamUtils.validating.unknownJobParameter=Unknown job parameter (%s)
ParamUtils.validating.dupParam=Duplicate param (%s)
ParamUtils.secret.note=<div style='margin-top: 12px;'><b>Note:</b> Secret less than %d characters will not be masked in build log</div>
ParamMatrixEditPanel.jobTips=Add additional values to run matrix job. The job will run for each possible combination of different values across all parameters
ParamMatrixEditPanel.stepTips=Add additional values to run matrix step. The step will run for each possible combination of different values across all parameters
ParamSpec.description.desc=Optionally describes the param. Html tags are accepted.
ParamSpec.allowMultiple.desc=Whether multiple values can be specified for this param
ParamSpec.allowMultiple.name=Allow Multiple
ParamSpec.sc.name=Show Conditionally
ParamSpec.sc.desc=Enable if visibility of this param depends on other params
ParamSpec.sc.placeholder=Always
ParamSpec.amv.name=Allow Empty Value
ParamSpec.amv.desc=Whether this param accepts empty value
ParamSpec.defaultProviderValue.name=Default Value
ParamSpec.defaultProviderValue.placeholder=No default value
ParamSpec.ChoiceProvider.name=Available Choices
IntegerParam.min.name=Min Value
IntegerParam.min.desc=Optionally specify the minimum value allowed.
IntegerParam.max.name=Max Value
IntegerParam.max.desc=Optionally specify the maximum value allowed.
TextParam.multiLine.name=Multiple Lines
TextParam.pattern.name=Pattern
TextParam.pattern.desc=Optionally specify a <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>regular expression pattern</a> for valid values of the text input
Project.serviceDeskEmailAddress.name=Service Desk Email Address
Project.serviceDeskEmailAddress.placeholder=Default
Project.serviceDeskEmailAddress.desc=Specify an email address that shares the same inbox as the system email address in mail setting definition. Emails sent to this address will be created as issues in this project. The default value takes the form <tt>&lt;system email address name&gt;+&lt;project path&gt;@&lt;system email address domain&gt;</tt>
PassthroughValue.error=Param not found: %s
BuildSpec.validating.errorImported=Error validating imported %s (%s: %s, error message: %s)
BuildSpec.validating.errorImportedWithLocation=Error validating imported %s (%s: %s, location: %s, error message: %s)
BuildSpec.validating.errorDupJobName=Duplicate job name (%s)
BuildSpec.validating.errorDupServiceName=Duplicate service name (%s)
BuildSpec.validating.errorDupTemplateName=Duplicate step template name (%s)
BuildSpec.validating.errorDupPropertyName=Duplicate property name (%s)
BuildSpec.validating.errorDupImportProject=Duplicate import (project: %s, revision: %s)
BuildSpec.validating.errorImportedStepTemplate=Error validating imported step template (step template: %s, error message: %s)
BuildSpec.validating.errorImportedJob=Error validating imported job (job: %s, error message: %s)
BuildSpec.validating.errorImportedJobWithLocationAndStepTemplate=Error validating imported job (job: %s, location: steps[%d].templateName, error message: %s)
BuildSpec.validating.errorPostBuildAction=Error validating post build action (index: %d, error message: %s)
BuildSpec.validating.errorUndefinedService=Undefined service (%s)
BuildSpec.validating.errorCircularTemplateUsages=Circular template usages (%s)
BuildSpec.validating.errorStepTemplateParameters=Error validating step template parameters (%s)
BuildSpec.validating.errorStepTemplateNotFound=Step template not found (%s)
BuildSpec.validating.errorCircularDep=Circular dependencies (%s)
BuildSpec.validating.errorDepJobNotFound=Dependency job not found (%s)
BuildSpec.validating.errorDepJobParam=Error validating dependency job parameters (dependency job: %s, error message: %s)
BuildSpec.elementTypeName.job=job
BuildSpec.elementTypeName.service=service
BuildSpec.elementTypeName.stepTemplate=step template
BuildSpec.elementTypeName.property=property
Service.name.desc=Specify name of the service, which will be used as host name to access the service
Service.image.name=Image
Service.image.desc=Specify docker image of the service
Service.args.name=Arguments
Service.args.desc=Optionally specify arguments to run above image
Service.envs.name=Environment Variables
Service.envs.desc=Optionally specify environment variables of the service
Service.rcc.name=Readiness Check Command
Service.rcc.desc=Specify command to check readiness of the service. This command will be interpretated by cmd.exe on Windows images, and by shell on Linux images. It will be executed repeatedly until a zero code is returned to indicate service ready
Service.runAs.name=Run As
Service.runAs.group=More Settings
Service.runAs.placeholder=root
Service.runAs.desc=Optionally specify uid:gid to run container as. <b class='text-warning'>Note:</b> This setting should be left empty if container runtime is rootless or using user namespace remapping
Service.rl.desc=Optionally specify registry logins to override those defined in job executor. For built-in registry, use <code>@server_url@</code> for registry url, <code>@job_token@</code> for user name, and access token secret for password secret
StepRegistryLogin.un.desc=Specify user name of the registry
Import.project.desc=Specify project to import build spec from
Import.revision.desc=Specify branch, tag or commit in above project to import build spec from
Import.revision.name=Revision
Import.secret.name=Access Token Secret
Import.secret.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as access token to import build spec from above project if its code is not publicly accessible
Import.error.unableToImport=Unable to import build spec (import project: %s, import revision: %s): %s
Import.error.noCodeReadPermission=Code read permission is required to import build spec (import project: %s, import revision: %s)
Import.error.malformedBuildSpec=Malformed build spec (import project: %s, import revision: %s)
Import.error.buildSpecNotDefined=Build spec not defined (import project: %s, import revision: %s)
Import.error.circularBuildSpecImports=Circular build spec imports (%s)
Import.error.importedBuildSpec=Error validating imported build spec (import project: %s, import revision: %s, location: %s, message: %s)
Import.error.failedToValidate=Failed to validate build spec import. Check server log for details
Import.error.unableToFindProject=Unable to find project to import build spec: %s
Import.error.unableToFindCommit=Unable to find commit to import build spec (import project: %s, import revision: %s)
DefaultInterpreter.name=Default (Shell on Linux, Batch on Windows)
DefaultInterpreter.commands.name=Commands
DefaultInterpreter.commands.desc=Specify shell commands (on Linux/Unix) or batch commands (on Windows) to execute under the <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>
PowerShellInterpretor.commands.desc=Specify PowerShell commands to execute under the <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>.<br><b class='text-warning'>NOTE: </b> DevGrip checks exit code of the script to determine if step is successful. Since PowerShell always exit with 0 even if there are script errors, you should handle errors in the script and exit with non-zero code, or add line <code>$ErrorActionPreference = &quot;Stop&quot;</code> at start of your script<br>
PowerShellInterpretor.powershell.name=Executable
PowerShellInterpretor.powershell.desc=Specify powershell executable to be used
ShellInterpreter.name=Custom Linux Shell
ShellInterpreter.shell.desc=Specify shell to be used
ShellInterpreter.commands.desc=Specify shell commands to execute under the <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>
StepGroup.securityAndCompliance=Security & Compliance
StepGroup.publish=Publish
StepGroup.utilities=Utilities
StepGroup.repoSync=Repositry Sync
StepGroup.dependencyManagement=Dependency Management
StepGroup.dockerImage=Docker Image
BuildImageStep.name=Build Image
BuildImageStep.desc=Build docker image with docker buildx. This step can only be executed by server docker executor or remote docker executor, and it uses the buildx builder specified in these executors to do the job. To build image with Kubernetes executor, please use kaniko step instead
BuildImageStep.buildPath.name=Build Path
BuildImageStep.buildPath.desc=Optionally specify build path relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>. Leave empty to use job workspace itself
BuildImageStep.dockerfile.name=Dockerfile
BuildImageStep.dockerfile.desc=Optionally specify Dockerfile relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>. Leave empty to use file <tt>Dockerfile</tt> under build path specified above
BuildImageStep.output.name=Output
BuildImageStep.platforms.name=Platforms
BuildImageStep.platforms.placeholder=Current platform
BuildImageStep.platforms.desc=Optionally specify <span class='text-info'>comma separated</span> platforms to build, for instance <tt>linux/amd64,linux/arm64</tt>. Leave empty to build for platform of the node running the job
BuildImageStep.moreOptions.name=More Options
BuildImageStep.moreOptions.desc=Optionally specify additional options for buildx build command
RegistryOutput.name=Push to Container registry
RegistryOutput.tags.name=Tags
RegistryOutput.tags.desc=Specify image tags to push, for instance <tt>registry-server:5000/myorg/myrepo:latest</tt>. if you want to push to built-in registry, simply use the form <tt>@server@/&lt;project path&gt;/&lt;repo name&gt;:&lt;tag name&gt;</tt>. Multiple tags should be separated with space
OCIOutput.name=Export as OCI layout
OCIOutput.destPath.name=OCI Layout Directory
OCIOutput.destPath.desc=Specify relative path under <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a> to store OCI layout
Step.condition.name=Condition
Step.condition.desc=Under which condition this step should run. Successful means all required previous steps completed successfully
Step.optional.name=Optional
Step.optional.desc=If this step is optional, its failure won’t fail the build, and later steps will ignore its result when checking success.
BuildImageWithKanikoStep.name=Build Image (Kaniko)
BuildImageWithKanikoStep.desc=Build docker image with kaniko. This step needs to be executed by server docker executor, remote docker executor, or Kubernetes executor
BuildImageWithKanikoStep.buildContext.name=Build Context
BuildImageWithKanikoStep.buildContext.desc=Optionally specify build context path relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>. Leave empty to use job workspace itself. The file <code>Dockerfile</code> is expected to exist in build context directory, unless you specify a different location with option <code>--dockerfile</code>
CertificatesToTrust=Certificates to Trust
MoreSettings=More Settings
CertificatesToTrustPlaceholder=Base64 encoded PEM format, starting with -----BEGIN CERTIFICATE----- and ending with -----END CERTIFICATE-----
CertificatesToTrustDesc=Specify certificates to trust if you are using self-signed certificates for your docker registries
MoreOptionsName=More Options
MoreOptionsDesc=Optionally specify <a href='https://github.com/GoogleContainerTools/kaniko?tab=readme-ov-file#additional-flags' target='_blank'>additional options</a> of kaniko
RegistryOutput.destinations.name=Destinations
RegistryOutput.destinations.desc=Specify destinations, for instance <tt>your-registry-server:5000/your-org/repo:latest</tt>. if you want to push to built-in registry, simply use the form <tt>@server@/&lt;project path&gt;/&lt;repo name&gt;:&lt;tag name&gt;</tt>. Multiple destinations should be separated with space
CommandStep.name=Execute Commands
CommandStep.runInContainer.name=Run In Container
CommandStep.runInContainer.desc=Whether to run this step inside container
CommandStep.image.name=Image
CommandStep.image.desc=Specify container image to execute commands inside
CommandStep.interpreter.name=Interpreter
CommandStep.runAs.name=Run As
CommandStep.runAs.placeholder=root
CommandStep.runAs.desc=Optionally specify uid:gid to run container as. <b class='text-warning'>Note:</b> This setting should be left empty if container runtime is rootless or using user namespace remapping
CommandStep.rl.name=Registry Logins
CommandStep.rl.desc=Optionally specify registry logins to override those defined in job executor. For built-in registry, use <code>@server_url@</code> for registry url, <code>@job_token@</code> for user name, and access token secret for password secret
CommandStep.env.name=Environment Variables
CommandStep.env.desc=Optionally specify environment variables for this step
CommandStep.enableTty.name=Enable TTY Mode
CommandStep.enableTty.desc=Many commands print outputs with ANSI colors in TTY mode to help identifying problems easily. However, some commands running in this mode may wait for user input to cause build hanging. This can normally be fixed by adding extra options to the command
StepTemplate.steps.name=Steps
StepTemplate.steps.desc=Steps will be executed serially on same node, sharing the same <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>
StepTemplate.param.name=Parameter Specs
StepTemplate.param.desc=Optionally define parameter specifications of the step template
UseStepTemplate.name=Use Step Template
UseStepTemplate.desc=Run specified step template
UseStepTemplate.templateName.name=Template Name
UseStepTemplate.error=Step template not found: %s
SyncRepository.remoteurl.name=Remote URL
SyncRepository.remoteurl.desc=Specify URL of remote git repository. Only http/https protocol is supported
SyncRepository.username.name=Username
SyncRepository.username.desc=Optionally specify user name to access remote repository
SyncRepository.password.name=Password or Access Token for Remote Repository
SyncRepository.password.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as password or access token to access remote repository
SyncRepository.certificate.desc=Specify certificate to trust if you are using self-signed certificate for remote repository
SyncRepository.proxy.name=Proxy
SyncRepository.proxy.placeholder=No proxy
SyncRepository.proxy.desc=Optionally configure proxy to access remote repository. Proxy should be in the format of &lt;proxy host&gt;:&lt;proxy port&gt;
SyncRepository.force.name=Force
SyncRepository.force.desc=Whether you use force option to overwrite changes in case ref updating can not be fast-forwarded
PullRepository.name=Pull from Remote
PullRepository.desc=This step pulls specified refs from remote.
PullRepository.targetProject.name=Target Project
PullRepository.targetProject.placeholder=Current project
PullRepository.targetProject.desc=Select project to sync to. Leave empty to sync to current project
PullRepository.token.name=Access Token for Target Project
PullRepository.token.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> whose value is an access token with management permission for above project. Note that access token is not required if sync to current or child project and build commit is reachable from default branch
PullRepository.refs.name=Refs
PullRepository.refs.desc=Specify space separated refs to pull from remote. '*' can be used in ref name for wildcard match<br> <b class='text-danger'>NOTE:</b> branch/tag protection rule will be ignored when update branches/tags via this step
PullRepository.lfs.name=Transfer LFS Files
PullRepository.lfs.desc=If this option is enabled, git lfs command needs to be installed on DevGrip server (even this step runs on other node)
PullRepository.error.targetProjectNotFound=Target project not found: %s
PullRepository.error.notAuthorized=This build is not authorized to sync to project: %s
PushRepository.name=Push to Remote
PushRepository.desc=This step pushes current commit to same ref on remote
CheckoutStep.name=Checkout Code
CheckoutStep.cloneCredenital.desc=By default, code is cloned via an auto-generated credential, which only has read permission over current project. In case the job needs to <a href='https://docs.devgrip.net/tutorials/cicd/commit-and-push' target='_blank'>push code to server</a>, you should supply custom credential with appropriate permissions here
CheckoutStep.cloneCredenital.name=Clone Credential
CheckoutStep.lfs.name=Retrieve LFS Files
CheckoutStep.lfs.desc=Check this to retrieve Git LFS files
CheckoutStep.submodules.name=Retrieve Submodules
CheckoutStep.submodules.desc=Whether to retrieve submodules. Refer to <a href='https://docs.devgrip.net/tutorials/cicd/clone-submodules' target='_blank'>this tutorial</a> on how to set up clone credential above to retrieve submodules
CheckoutStep.depth.name=Clone Depth
CheckoutStep.depth.desc=Optionally specify depth for a shallow clone in order to speed up source retrieval
CheckoutStep.checkoutPath.name=Checkout Path
CheckoutStep.checkoutPath.placeholder=Job workspace
CheckoutStep.checkoutPath.desc=Optionally specify relative path under <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to clone code into. Leave empty to use job workspace itself
CloseIterationStep.name=Close Iteration
CloseIterationStep.iteration.name=Iteration Name
CloseIterationStep.iteration.desc=Specify name of the iteration
AccessTokenSecret=Access Token Secret
CloseIterationStep.ats.desc=For build commit not reachable from default branch, a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> should be specified as access token with manage issue permission
CloseIterationStep.error.noAuth=This build is not authorized to close iteration '%s'
CloseIterationStep.error.unableFind=Unable to find iteration '%s' to close. Ignored.
CreateBranchStep.name=Create Branch
CreateBranchStep.branch.name=Branch Name
CreateBranchStep.branch.desc=Specify name of the branch
CreateBranchStep.revision.name=Branch Revision
CreateBranchStep.revision.placeholder=Build Commit
CreateBranchStep.revision.desc=Optionally specify revision to create branch from. Leave empty to create from build commit
CreateBranchStep.ats.desc=For build commit not reachable from default branch, a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> should be specified as access token with create branch permission
CreateBranchStep.error.invalidName=Invalid branch name: %s
CreateBranchStep.error.nameAlreadyExists=Branch %s already exists
CreateBranchStep.error.noAuth=This build is not authorized to create branch '%s'
GenerateChecksumStep.name=Generate File Checksum
GenerateChecksumStep.desc=This step can only be executed by a docker aware executor
GenerateChecksumStep.files.name=Files
GenerateChecksumStep.files.desc=Specify files to create md5 checksum from. Multiple files should be separated by space. <a href='https://www.linuxjournal.com/content/globstar-new-bash-globbing-option' target='_blank'>Globstar</a> patterns accepted. Non-absolute file is considered to be relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>
GenerateChecksumStep.targetFile.name=Target Files
GenerateChecksumStep.targetFile.desc=Specify a file relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a> to write checksum into
CreateTagStep.name=Create Tag
CreateTagStep.tag.name=Tag Name
CreateTagStep.tag.desc=Specify name of the tag
CreateTagStep.msg.name=Tag Message
CreateTagStep.msg.desc=Optionally specify message of the tag
CreateTagStep.ats.desc=For build commit not reachable from default branch, a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> should be specified as access token with create tag permission
CreateTagStep.error.invalidName=Invalid branch name: %s
CreateTagStep.error.noAuth=This build is not authorized to create tag '%s'
PruneBuilderCacheStep.name=Prune Builder Cache
PruneBuilderCacheStep.desc=Prune image cache of docker buildx builder. This step calls docker builder prune command to remove cache of buildx builder specified in server docker executor or remote docker executor
PruneBuilderCacheStep.options.desc=Optionally specify options for docker builder prune command
PruneBuilderCacheStep.options.name=Options
PublishArtifactStep.name=Artifacts
PublishArtifactStep.fromDir.name=From Directory
PublishArtifactStep.fromDir.placeholder=Job workspace
PublishArtifactStep.fromDir.desc=Optionally specify path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to publish artifacts from. Leave empty to use job workspace itself
PublishArtifactStep.artifacts.name=Artifacts
PublishArtifactStep.artifacts.desc=Specify files under above directory to be published. Use * or ? for pattern match
PublishReportStep.report.name=Report Name
PublishReportStep.report.desc=Specify name of the report to be displayed in build detail page
PublishReportStep.filePatterns.name=File Patterns
PublishReportStep.filePatterns.desc=Specify files relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to be published. Use * or ? for pattern match
PublishSiteStep.name=Site
PublishSiteStep.desc=This step publishes specified files to be served as project web site. Project web site can be accessed publicly via <code>http://&lt;server base url&gt;/path/to/project/~site</code>
PublishSiteStep.project.placeholder=Current project
PublishSiteStep.project.desc=Optionally specify the project to publish site files to. Leave empty to publish to current project
PublishSiteStep.fromDir.name=From Directory
PublishSiteStep.fromDir.placeholder=Job workspace
PublishSiteStep.fromDir.desc=Optionally specify path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to publish artifacts from. Leave empty to use job workspace itself
PublishSiteStep.artifacts.name=Artifacts
PublishSiteStep.artifacts.desc=Specify files under above directory to be published. Use * or ? for pattern match. <b>NOTE:</b> <code>index.html</code> should be included in these files to be served as site start page
PublishSiteStep.error.unableFindProject=Unable to find project:  %s
PublishSiteStep.log.publishSuccess=Site published as %s/%s/~site
PublishSiteStep.error.publishProhibited=Site publish is prohibited by current job executor
PullImageStep.name=Pull Image
PullImageStep.desc=Pull docker image as OCI layout via crane. This step needs to be executed by server docker executor, remote docker executor, or Kubernetes executor
PullImageStep.srcImage.name=Source Docker Image
PullImageStep.srcImage.desc=Specify image tag to pull from, for instance <tt>your-registry-server:5000/your-org/repo:latest</tt>. if you want to pull from built-in registry, simply use the form <tt>@server@/&lt;project path&gt;/&lt;repo name&gt;:&lt;tag name&gt;</tt>
PullImageStep.platform.name=Platform
PullImageStep.platform.placeholder=All platforms in image
PullImageStep.platform.desc=Optionally specify platform to pull, for instance <tt>linux/amd64</tt>. Leave empty to pull all platforms in image
PullImageStep.moreOptions.desc=Optionally specify <a href='https://github.com/google/go-containerregistry/blob/main/cmd/crane/doc/crane_pull.md' target='_blank'>additional options</a> of crane
PullImageStep.moreOptions.name=More Options
PullImageStep.error.notAllowed=Loopback address not allowed for source docker image of push image step, please use ip address or host name instead
PushImageStep.name=Push Image
PushImageStep.desc=Push docker image from OCI layout via crane. This step needs to be executed by server docker executor, remote docker executor, or Kubernetes executor
PushImageStep.srcPath.desc=Specify OCI layout directory relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a> to push from
PushImageStep.destImage.name=Target Docker Image
PushImageStep.destImage.desc=Specify image tag to push to, for instance <tt>your-registry-server:5000/your-org/repo:latest</tt>. if you want to push to built-in registry, simply use the form <tt>@server@/&lt;project path&gt;/&lt;repo name&gt;:&lt;tag name&gt;</tt>
PushImageStep.moreOptions.name=More Options
PushImageStep.moreOptions.desc=Optionally specify <a href='https://github.com/google/go-containerregistry/blob/main/cmd/crane/doc/crane_push.md' target='_blank'>additional options</a> of crane
pushImageStep.error.notAllowed=Loopback address not allowed for target docker image of push image step, please use ip address or host name instead
RunContainerStep.name=Run Docker Container
RunContainerStep.desc=Run specified docker container. <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>Job workspace</a> is mounted into the container and its path is placed in environment variable <code>DEVGRIP_WORKSPACE</code>. <b class='text-warning'>Note: </b> this step can only be executed by server docker executor or remote docker executor
RunContainerStep.image.name=Image
RunContainerStep.image.desc=Specify container image to run
RunContainerStep.args.name=Arguments
RunContainerStep.args.desc=Optionally specify container arguments separated by space. Single argument containing space should be quoted. <b class='text-warning'>Note: </b> do not confuse this with container options which should be specified in executor setting
RunContainerStep.wd.name=Working Directory
RunContainerStep.wd.placeholder=Container default
RunContainerStep.wd.desc=Optionally specify working directory of the container. Leave empty to use default working directory of the container
RunContainerStep.env.desc=Optionally specify environment variables for the container
RunContainerStep.vm.name=Volume Mounts
RunContainerStep.vm.desc=Optionally mount directories or files under job workspace into container
RunImagetoolsStep.name=Run Buildx Image Tools
RunImagetoolsStep.desc=Run docker buildx imagetools command with specified arguments. This step can only be executed by server docker executor or remote docker executor
RunImagetoolsStep.args.name=Arguments
RunImagetoolsStep.args.desc=Specify arguments for imagetools. For instance <code>create -t myorg/myrepo:1.0.0 myorg/myrepo@&lt;arm64 manifest digest&gt; myorg/myrepo@&lt;amd64 manifest digest&gt;</code>
SCPCommandStep.name=Copy Files with SCP
SCPCommandStep.desc=This step can only be executed by a docker aware executor. It runs under <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>
SCPCommandStep.pks.name=Private Key Secret
SCPCommandStep.pks.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as private key for SSH authentication.<b class='text-info'>NOTE:</b> Private key with passphrase is not supported
SCPCommandStep.source.name=Source
SCPCommandStep.source.desc=Specify source param for SCP command, for instance <code>app.tar.gz</code>
SCPCommandStep.target.name=Target
SCPCommandStep.target.desc=Specify target param for SCP command, for instance <code>user@@host:/app</code>. <b class='text-info'>NOTE:</b> Make sure that scp command is installed on remote host
SCPCommandStep.options.name=Options
SCPCommandStep.options.desc=Optionally specify options for scp command. Multiple options need to be separated with space
SetBuildDescriptionStep.name=Set Build Description
SetBuildDescriptionStep.buildDesc.name=Build Description
SetBuildVersionStep.name=Set Build Version
SetBuildVersionStep.buildVersion.name=Build Version
SetupCacheStep.name=Set Up Cache
SetupCacheStep.desc=Set up job cache to speed up job execution. Check <a href='https://docs.devgrip.net/tutorials/cicd/job-cache' target='_blank'>this tutorial</a> on how to use job cache
SetupCacheStep.key.name=Cache Key
SetupCacheStep.key.desc=This key is used to determine if there is a cache hit in project hierarchy (search from current project to root project in order, same for load keys below). A cache is considered hit if its key is exactly the same as the key defined here
SetupCacheStep.loadKeys.name=Load Keys
SetupCacheStep.loadKeys.desc=In case cache is not hit via above key, DevGrip will loop through load keys defined here in order until a matching cache is found in project hierarchy. A cache is considered matching if its key is prefixed with the load key. If multiple caches matches, the most recent cache will be returned
SetupCacheStep.cachePaths.name=Cache Paths
SetupCacheStep.cachePaths.desc=For docker aware executors, this path is inside container, and accepts both absolute path and relative path (relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>). For shell related executors which runs on host machine directly, only relative path is accepted
SetupCacheStep.uploadStrategy.name=Upload Strategy
SetupCacheStep.uploadStrategy.desc=Specify cache upload strategy after build successful. <var>Upload If Not Hit</var> means to upload when cache is not found with cache key (not load keys), and <var>Upload If Changed</var> means to upload if some files in cache path are changed
SetupCacheStep.cde.name=Change Detection Excludes
SetupCacheStep.cde.desc=Optionally specify files relative to cache path to ignore when detect cache changes. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Multiple files should be separated by space, and single file containing space should be quoted
SetupCacheStep.uploadToProject.name=Upload to Project
SetupCacheStep.uploadToProject.placeholder=Current project
SetupCacheStep.uploadToProject.desc=In case cache needs to be uploaded, this property specifies target project for the upload. Leave empty for current project
SetupCacheStep.uploadAccessTokenSecret.name=Upload Access Token Secret
SetupCacheStep.uploadAccessTokenSecret.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> whose value is an access token with upload cache permission for above project. Note that this property is not required if upload cache to current or child project and build commit is reachable from default branch
SSHCommandStep.name=Execute Commands via SSH
SSHCommandStep.desc=This step can only be executed by a docker aware executor
SSHCommandStep.remoteMachine.name=Remote Machine
SSHCommandStep.remoteMachine.desc=Host name or ip address of remote machine to run commands via SSH
SSHCommandStep.username.name=User Name
SSHCommandStep.username.desc=Specify user name of above machine for SSH authentication
SSHCommandStep.pks.name=Private Key Secret
SSHCommandStep.pks.desc=Specify a <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as private key of above user for SSH authentication.<b class='text-info'>NOTE:</b> Private key with passphrase is not supported
SSHCommandStep.options.name=Options
SSHCommandStep.options.desc=Optionally specify options for ssh command. Multiple options need to be separated with space
SSHCommandStep.commands.name=Commands
SSHCommandStep.commands.desc=Specify commands to be executed on remote machine. <b class='text-warning'>Note:</b> user environments will not be picked up when execute these commands, set up them explicitly in commands if necessary
VolumeMount.sourcePath.name=Source Path
VolumeMount.sourcePath.placeholder=Job workspace
VolumeMount.sourcePath.desc=Specify a path relative to job workspace to be used as mount source. Leave empty to mount job workspace itself
VolumeMount.targetPath.name=Target Path
VolumeMount.targetPath.desc=Specify a path inside container to be used as mount target
PublishCheckstyleReportStep.name=Checkstyle Report
File_Patterns=File Patterns
PublishCheckstyleReportStep.filePatterns.desc=Specify checkstyle result xml file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/checkstyle-result.xml</tt>. Refer to <a href='https://checkstyle.org/'>checkstyle documentation</a> on how to generate the result xml file. Use * or ? for pattern match
PublishCheckstyleReportStep.tabWidth.name=Tab Width
PublishCheckstyleReportStep.tabWidth.desc=Specify tab width used to calculate column value of found problems in provided report
PublishESLintReportStep.name=ESLint Report
PublishESLintReportStep.filePatterns.desc=Specify ESLint report file in checkstyle format under <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with ESLint option <tt>'-f checkstyle'</tt> and <tt>'-o'</tt>. Use * or ? for pattern match
PublishClippyReportStep.name=Clippy Report
PublishClippyReportStep.filePatterns.desc=Specify <a href='https://github.com/rust-lang/rust-clippy'>rust clippy</a> json output file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with clippy json output option, for instance <code>cargo clippy --message-format json>check-result.json</code>. Use * or ? for pattern match
ProblemReportPage.filterTargets=Filter targets
ProblemReportPage.filterButton=Filter
ProblemReportPage.viewSource=View source
ProblemReportPage.notice=Report format changed. You may re-run this build to generate the report in new format
ProblemReportPage.hint1=Target containing spaces or starting with dash needs to be quoted
ProblemReportPage.hint2=Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
ProblemReportPage.tooManyProblems=Too many problems, displaying first ${max}
ProblemReportPage.line=Line: 
ProblemReportPage.filterMalformed=Malformed filter
ProblemStatsPage.title=Code Problem Statistics
BuildMetricStatsPage.filterButton=Filter
BuildMetricStatsPage.filter=Filter...
BuildMetricStatsPage.malformedQuery=Malformed query
CodeContribsPage.commits=Commits
CodeContribsPage.additions=Additions
CodeContribsPage.deletions=Deletions
CodeContribsPage.title=Code Contribution Statistics
CodeContribsPage.noteLabel=Contributions to ${branch} branch, excluding merge commits
GitRole.author=Author
GitRole.committer=Committer
PersonCardPanel.systemAccount=<i>System Account</i>
PersonCardPanel.noAccount=<i>No Account</i>
UserCardPanel.unknownAccount=<i>Unknown Account</>
CodeStatsPage.contributions=Contributions
CodeStatsPage.sourceLines=Source Lines
CodeStatsPage.title=Code Statistics
SourceLinesPage.title=Code Line Statistics
SourceLinesPage.slocTitle=SLOC on ${defaultBranch}
SourceLinesPage.noDefaultBranchTitle=No default branch
PublishProblemReportStep.ft.name=Fail Threshold
PublishProblemReportStep.ft.desc=Fail build if there are vulnerabilities with or severer than specified severity level. Note that this only takes effect if build is not failed by other steps
PublishProblemReportStep.error=%s: found problems with or severer than %s severity
PublishProblemReportStep.passed=Code passed all checks (report ignored).
Publish_Log_UnableFindBlob=Unable to find blob path for file: %s
PublishJestCoverageReportStep.name=Jest Coverage Report
PublishJestCoverageReportStep.filePatterns.desc=Specify Jest coverage report file in clover format relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance <tt>coverage/clover.xml</tt>. This file can be generated with Jest option <tt>'--coverage'</tt>. Use * or ? for pattern match
PublishCloverReportStep.name=Clover Coverage Report
PublishCloverReportStep.filePatterns.desc=Specify clover coverage xml report file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/site/clover/clover.xml</tt>. Refer to <a href='https://openclover.org/documentation'>OpenClover documentation</a> on how to generate clover xml file. Use * or ? for pattern match
Publish_Log_Processing=Processing %s report '%s'...
Publish_Log_Ignored=Ignored %s report '%s' as it is not a valid XML
PublishCoberturaReportStep.name=Cobertura Coverage Report
PublishCoberturaReportStep.filePatterns.desc=Specify cobertura coverage xml report file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/site/cobertura/coverage.xml</tt>. Use * or ? for pattern match
CoverageStatsPage.title=Coverage Statistics
CoverageStatsPage.hint1=Name containing spaces or starting with dash needs to be quoted
CoverageStatsPage.overall=Overall
CoverageStatsPage.files=Files
CoverageStatsPage.groups=Groups
CoverageStatsPage.filterFiles=Filter files...
CoverageStatsPage.filterGroups=Filter groups...
CoverageOrderBy.default=Default
CoverageOrderBy.leastBranchCoverage=Least branch coverage
CoverageOrderBy.mostBranchCoverage=Most branch coverage
CoverageOrderBy.leastLineCoverage=Least line coverage
CoverageOrderBy.mostLineCoverage=Most line coverage
CoveragePanel.lines=Lines 
CoveragePanel.branches=Branches 
PublishCPDReportStep.name=CPD Report
PublishCPDReportStep.filePatterns.desc=Specify CPD result xml file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/cpd.xml</tt>. Use * or ? for pattern match
PublishCPDReportStep.dupMsg=Duplicated with '%s' at <a href='%s'>line %s - %s</a>
PublishCppcheckReportStep.name=Cppcheck Report
PublishCppcheckReportStep.filePatterns.desc=Specify cppcheck xml result file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with cppcheck xml output option, for instance <code>cppcheck src --xml 2>check-result.xml</code>. Use * or ? for pattern match
PublishGTestReportStep.name=Google Test Report
PublishGTestReportStep.filePatterns.desc=Specify GoogleTest XML result file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This report can be generated with environment variable <tt>GTEST_OUTPUT</tt> when running tests, For instance, <code>export GTEST_OUTPUT=&quot;xml:gtest-result.xml&quot;</code>. Use * or ? for pattern match
PublishHtmlReportStep.name=Html Report
PublishHtmlReportStep.startPage.name=Start Page
PublishHtmlReportStep.startPage.desc=Specify start page of the report relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance: api/index.html
PublishHtmlReportStep.startPageNotFound=Html report start page not found: %s
PublishHtmlReportStep.noAuth=Html report publish is prohibited by current job executor
PublishJacocoReportStep.name=JaCoCo Coverage Report
PublishJacocoReportStep.filePatterns.desc=Specify JaCoCo coverage xml report file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/site/jacoco/jacoco.xml</tt>. Use * or ? for pattern match
PublishJestReportStep.name=Jest Test Report
PublishJestReportStep.filePatterns.desc=Specify Jest test result file in json format relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated via Jest option <tt>'--json'</tt> and <tt>'--outputFile'</tt>. Use * or ? for pattern match
PublishJUnitReportStep.name=JUnit Report
PublishJUnitReportStep.filePatterns.desc=Specify JUnit test result file in XML format relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance <tt>target/surefire-reports/TEST-*.xml</tt>. Use * or ? for pattern match
PublishMarkdownReportStep.name=Markdown Report
PublishMarkdownReportStep.startPage.name=Start Page
PublishMarkdownReportStep.startPage.desc=Specify start page of the report relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance: <tt>manual/index.md</tt>
PublishMarkdownReportStep.pageNotFound=Markdown report start page not found: %s
PublishPullRequestMarkdownReportStep.name=Pull Request Markdown Report
PublishPullRequestMarkdownReportStep.desc=This report will be displayed in pull request overview page if build is triggered by pull request
PublishPullRequestMarkdownReportStep.filePatterns.desc=Specify markdown file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to be published
PublishMypyReportStep.name=Mypy Report
PublishMypyReportStep.filePatterns.desc=Specify mypy output file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated by redirecting mypy output <b>without option '--pretty'</b>, for instance <code>mypy --exclude=.git --exclude=.venv . > mypy-output</code>. Use * or ? for pattern match
PublishPMDReportStep.name=PMD Report
PublishPMDReportStep.filePatterns.desc=Specify PMD result xml file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/pmd.xml</tt>. Use * or ? for pattern match
PublishPylintReportStep.name=Pylint Report
PublishPylintReportStep.filePatterns.desc=Specify pylint json result file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with pylint json output format option, for instance <code>--exit-zero --output-format=json:pylint-result.json</code>. Note that we do not fail pylint command upon violations, as this step will fail build based on configured threshold. Use * or ? for pattern match
PublishRoslynatorReportStep.name=Roslynator Report
PublishRoslynatorReportStep.filePatterns.desc=Specify Roslynator diagnostics output file in XML format relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with <i>-o</i> option. Use * or ? for pattern match
PublishRuffReportStep.name=Ruff Report
PublishRuffReportStep.filePatterns.desc=Specify ruff json result file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>. This file can be generated with ruff json output format option, for instance <code>--exit-zero --output-format json --output-file ruff-result.json</code>. Note that we do not fail ruff command upon violations, as this step will fail build based on configured threshold. Use * or ? for pattern match
PublishSpotBugsReportStep.name=SpotBugs Report
PublishSpotBugsReportStep.filePatterns.desc=Specify SpotBugs result xml file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance, <tt>target/spotbugsXml.xml</tt>. Use * or ? for pattern match
PublishTRXReportStep.name=TRX Report (.net unit test)
PublishTRXReportStep.filePatterns.desc=Specify .net TRX test result file relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a>, for instance <tt>TestResults/*.trx</tt>. Use * or ? for pattern match
UnitTestCasesPage.filterByTestSuite=Filter by test suite
UnitTestCasesPage.longestDurationFirst=Longest Duration First
UnitTestCasesPage.malformedTestSuiteFilter=Malformed test suite filter
UnitTestCasesPage.malformedNameFilter=Malformed name filter
UnitTestCasesPage.hint1=Path containing spaces or starting with dash needs to be quoted
UnitTestCasesPage.hint2=Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
UnitTestReportPage.testCases=Test Cases
UnitTestReportPage.testSuites=Test Suites
UnitTestStatsPage.title=Unit Test Statistics
UnitTestSuitesPage.showTestCasesTitle=Show test cases of this test suite
CallbackUrlPanel.callbackTips=This callback url will be required when registering DevGrip to OpenID provider
PackSidePanel.publishBy=Published By
PackSidePanel.publishAt=Published At
PackSidePanel.totalSize=Total Size
PackSidePanel.fixedIssueSince=Fixed issues since...
PackSidePanel.changeSince=Changes since...
PackListPanel.queryPlaceholder=Query/order packages
PackListPanel.howToPub=How to Publish
PackListPanel.followBelowToPublish=Follow below instructions to publish packages into this project
PackListPanel.deleteSelectedConfirm=Type <code>yes</code> below to delete selected packages
PackListPanel.deleteSelectedTitle=Please select packages to delete
PackListPanel.deleteAllQueriedConfirm=Type <code>yes</code> below to delete all queried packages
PackListPanel.deleteAllQueriedTitle=No packages to delete
PackListPanel.countPackages=found ${count} package
PackListPanel.colPackage=Package
PackListPanel.colType=Type
PackListPanel.colLastPublished=Last Published
PackListPanel.colTotalSize=Total Size
PackListPanel.pleaseSwitchToPackakgesPage=Please switch to packages page of a particular project for the instructions
OrderEditPanel.removeTitle=Remove
OrderEditPanel.descending=descending
MavenHelpPanel.pomHelp=1. Use below repositories in project pom.xml
MavenHelpPanel.settingsHelp=2. Add below to <code>$HOME/.m2/settings.xml</code> if you want to deploy from command line
MavenHelpPanel.commandHelp=3. For CI/CD job, it is more convenient to use a custom settings.xml, for instance via below code in a command step:
MavenPackPanel.pomTips=1. To use this package, add below to project pom.xml
MavenPackPanel.settingsTips=2. Also add below to $HOME/.m2/settings.xml if you want to compile project from command line
MavenPackPanel.commandTips=3. For CI/CD job, it is more convenient to use a custom settings.xml, for instance via below code in a command step:
MavenPackPanel.metadataNotFound=Plugin metadata not found
MavenPackPanel.publishedFile=Published File
MavenPackPanel.size=Size
ContainerHelpPanel.login=Login to DevGrip docker registry
ContainerHelpPanel.loginPermission=Login user needs to have package write permission over the project below
ContainerHelpPanel.pushImage=Then push image to desired repository under specified project
ContainerHelpPanel.pushImageViaCicd=You can also achieve this by adding a build docker image step to your CI/CD job and configuring the built-inregistry login with an access token secret that has package write permissions
ContainerPackPanel.digest=Digest
ContainerPackPanel.pullCommand=Pull Command
ContainerPackPanel.osArch=OS/ARCH
ContainerPackPanel.archPullCommand=Arch Pull Command
ContainerPackPanel.imageSize=Image SizeImage Size
ContainerPackPanel.imageManifest=Image Manifest
ContainerPackPanel.imageLabels=Image Labels
ContainerPackPanel.layerCache=This is a layer cache. To use the cache, add below option to your docker buildx command
ContainerPackPanel.manifest=Manifest
InsecureRegistryNotePanel.warning=WARNING: 
InsecureRegistryNotePanel.viaHttpProtocol=This server is currently accessed via http protocol, please configure your docker daemon or buildx builder to <a href="https://docs.devgrip.net/tutorials/cicd/insecure-docker-registry" target="_blank">work with insecure registry</a>
GemHelpPanel.addASource=Edit <code>$HOME/.gem/credentials</code> to add a source
GemHelpPanel.runCommand=Make sure the access token has package write permission over the project. Also make sure to run command <code>chmod 0600 $HOME/.gem/credentials</code> after creating the file
GemHelpPanel.pushGem=Then push gem to the source
GemHelpPanel.pushGemForCicd=For CI/CD job, run below to add source via command step
GemPackPanel.runBelow=Run below commands to install this gem
GemPackPanel.permission=Make sure the account has package read permission over the project
GemPackPanel.pushGemForCicd=For CI/CD job, add this gem to Gemfile like below
GemPackPanel.resolveDependency=Then resolve dependency via command step
GemPackPanel.gemInfo=Gem Info
NpmHelpPanel.configureScope=Configure your scope to use below registry
NpmHelpPanel.configureAuthToken=And configure auth token of the registry
NpmHelpPanel.permission=Make sure the access token has package write permission over the project
NpmHelpPanel.publish=Then publish package from project directory like below
NpmHelpPanel.publishViaCommand=For CI/CD job, run below to publish package via command step
NpmPackPanel.runBelow=Run below commands to install this package
NpmPackPanel.permission=Make sure the access token has package read permission over the project
NpmPackPanel.runBelowForCicd=For CI/CD job, run below via a command step
NpmPackPanel.readme=README
NugetHelpPanel.addSource=Add a package source like below
NugetHelpPanel.permission=Make sure the user has package write permission over the project
NugetHelpPanel.push=Then push package to the source
NugetHelpPanel.cicd=For CI/CD job, run below to add package source via command step
NugetPackPanel.runBelow=Run below commands to use this package
NugetPackPanel.permission=Make sure the account has package read permission over the project
NugetPackPanel.cicd=For CI/CD job, run below to add package source via command step
NugetPackPanel.nuSpec=NuSpec
PypiHelpPanel.editToAdd=Edit <code>$HOME/.pypirc</code> to add a package repository like below
PypiHelpPanel.permission=Make sure the user has package write permission over the project
PypiHelpPanel.thenUpload=Then upload package to the repository with twine
PypiHelpPanel.cicd=For CI/CD job, run below to add package repository via command step
PypiPackPanel.runBelow=Run below commands to install this package
PypiPackPanel.permission=Make sure the account has package read permission over the project
PypiPackPanel.cicd=For CI/CD job, add this package to requirements.txt and run below to install the package via command step
PypiPackPanel.metaInfo=Meta Info
HelmHelpPanel.pushToRepo=Push chart to the repository
HelmHelpPanel.writePermission=Make sure the account has package write permission over the project
HelmHelpPanel.cicdToPush=For CI/CD job, run below to push chart to the repository via command step
HelmPackPanel.runBelow=Run below commands to use this chart
HelmPackPanel.readPermission=Make sure the account has package read permission over the project
HelmPackPanel.cicdToInstall=For CI/CD job, run below to install chart via command step
HelmPackPanel.metadata=Chart Metadata
ColorPicker.btnSave=Save
ColorPicker.btnClear=Clear
BlobTextDiffPanel.invalidSelection=Invalid selection, click for details
BlobTextDiffPanel.unableToCommentHere=Unable to comment here
BlobTextDiffPanel.permanentLink=Permanent link of this selection
BlobTextDiffPanel.copySelectedText=Copy selected text to clipboard
BlobTextDiffPanel.addCommentOnThisSelection=Add comment on this selection
BlobTextDiffPanel.loginToComment=Login to comment on selection
BlobTextDiffPanel.showCommentOfMarkedTextTitle=Click to show comment of marked text
BlobTextDiffPanel.coveredByTestsTitle=Covered by tests
BlobTextDiffPanel.notCoveredByAnyTestTitle=Not covered by any test
BlobTextDiffPanel.partiallyCoveredBySomeTestsTitle=Partially covered by some tests
BlobTextDiffPanel.loading=Loading...
BlobTextDiffPanel.thereAreUnsavedChanges=There are unsaved changes, discard and continue?
MarkdownEditor.commandsHint=Press 'cmd-/' or 'ctrl-/' for commands
MarkdownEditor.outdated=Commented code is outdated
MarkdownEditor.suggestOnMac=Suggest changes (cmd-g)
MarkdownEditor.suggestOnWin=Suggest changes (ctrl-g)
MarkdownEditor.suggestionIsOutdated=Suggestion is outdated either due to code change or pull request close
MarkdownEditor.removeFromBatch=Remove from batch
MarkdownEditor.commitSuggestion=Commit suggestion
MarkdownEditor.addToBatchToCommit=Add to batch to commit with other suggestions later
MarkdownEditor.loading=loading...
MarkdownEditor.issueTooltip.noIssueFound=Issue not exist or access denied
MarkdownEditor.prTooltip.noPrFound=Pull request not exist or access denied
MarkdownEditor.buildTooltip.noBuildFound=Build not exist or access denied
MarkdownEditor.commitTooltip.commitBuildFound=Commit not exist or access denied
CodeProcessor.suggestedCodeDiv=<div class='pb-2 mb-2 head font-size-xs mx-n2 px-2'>Suggested change</div>
CodeProcessor.suggestedCode=<p><i>Suggested change</i></p>
SuggestionBatchApplyBean.name=Commit Batched Suggestions
SuggestionBatchApplyBean.defaultCommitMessage=Apply suggested changes from code comments
SuggestionApplyBean.name=Commit Suggestion
SuggestionApplyBean.defaultCommitMessage=Apply suggested change from code comment
SuggestionApplyBean.branch.desc=Specify branch to commit suggested change
SuggestionApplyBean.branch.name=Branch
SuggestionApplyBean.commitMessage.name=Commit Message
SuggestionApplyModalPanel.codeCommentNote=Suggestion applied
SuggestionApplyModalPanel.branchWasUpdate=Branch was updated by some others just now, please try again
SuggestionApplyModalPanel.applyDisallowed=Suggestion apply disallowed by branch protection rule
NewRolePage.createSuccess=Role created
RoleProjectOwner=Project Owner
RoleCodeReader=Code Reader
RoleCodeWriter=Code Writer
RoleIssueReporter=Issue Reporter
RoleIssueManager=Issue Manager
RolePackageReader=Package Reader
RolePackageWriter=Package Writer
SuggestionUtils.filesWithExt=files with ext '%s'
SuggestionUtils.anExampleBranch=An example branch
SuggestionUtils.anExampleBuild=An example build
SuggestionUtils.anExampleBuildWithProjectKey=An example build
SuggestionUtils.anExamplePullRequest=An example pull request
SuggestionUtils.anExamplePullRequestWithProjectKey=An example pull request
SuggestionUtils.anExampleIssue=An example issue
SuggestionUtils.anExampleIssueWithProjectKey=An example issue
SuggestionUtils.anExampleRevision=An example revision
SuggestionUtils.anExampleTag=An example tag
SourceFormatPanel.indentType.spaces=Spaces
SourceFormatPanel.indentType.tabs=Tabs
SourceFormatPanel.lineWrapMode.soft=Soft wrap
SourceFormatPanel.lineWrapMode.no=No wrap
SourceFormatPanel.indentType.label=Indent type
SourceFormatPanel.tabSize.label=Tab size
SourceFormatPanel.lineWrapMode.label=Line wrap mode
BuildTerminalPage.pageTilte=Web Terminal
TimesheetsPage.noTimesheets=No timesheets defined
TimesheetsPage.addNewTimesheet=Add New Timesheet
TimesheetsPage.default=default
TimesheetsPage.inherited=inherited
TimesheetsPage.firstWillBeDefault=The first timesheet will be the default timesheet
TimesheetsPage.export=Export
TimesheetsPage.canNotBeSorted=Inherited timesheets can not be sorted
TimesheetsPage.deleteConfirm=Do you really want to delete timesheet?
TimesheetsPage.editTimesheet=Edit Timesheet
TimesheetsPage.addTimesheet=Add Timesheet
TimesheetPanel.totalTime=Total time
TimesheetPanel.user=User
TimesheetPanel.spent=Spent
TimesheetPanel.issue=Issue
TimesheetSetting.filterIssues.name=Filter Issues
TimesheetSetting.filterIssues.placeholder=All issues
TimesheetSetting.showWorksOf.name=Show Works Of
TimesheetSetting.timeRange.name=Time Range
TimesheetSetting.groupBy.name=Group By
TimesheetSetting.groupBy.placeholder=No group by
DateRangeNavigator.prev=Prev
DateRangeNavigator.next=Next
DateRangeNavigator.thisWeek=This Week
DateRangeNavigator.thisMonth=This Month
DateRangeNavigator.toAvoidDuplication=To avoid duplication, spent time showing here does not include those aggregated from '${aggregationLink}'
BuildDurationStatsPage.help=This shows average pending and running duration of builds over time
BuildDurationStatsPanel.runningLabel=Running
BuildDurationStatsPanel.pendingLabel=Pending
BuildFrequencyStatsPage.help=This shows number of finished builds over time
BuildFrequencyStatsPage.frequencyLabel=frequency
BuildListStatsPanel.stats=Stats
BuildListStatsPanel.statsDesc=Build statistics is an enterprise feature. <a href='https://devgrip.net/pricing' target='_blank'>Try free</a> for 30 days
ProjectDashboardPage.dashboard=Dashboard
BuildStatsPage.buildStatsTitle=Build Statistics
Stats.durationsLabel=Durations
Stats.frequenciesLabel=Frequencies
EntityStatsPage.filterTitle=Filter
EntityStatsPage.monthDisplay=${num} Month${s}
IssueStateTrendStatsPage.help=This page shows the number of issues in each status over time, helping the team quickly identify bottlenecks, optimize task allocation, and improve workflow.
IssueListStatsPanel.statsDesc=Issue statistics is an enterprise feature. <a href='https://devgrep.net/pricing' target='_blank'>Try free</a> for 30 days
IssueStatsPage.prStatsTitle=Issue Statistics
IssueStatsPage.stateTrend=Cumulative Flow
DisplayMonthsEditBean.name=Months to Display
OsvVulnerScannerStep.name=Osv Vulnerability Scanner
OsvVulnerScannerStep.desc=This step runs osv scanner to scan vulnerabilities in <a href='https://google.github.io/osv-scanner/supported-languages-and-lockfiles/' target='_blank'>various lock files</a>. It can only be executed by docker aware executor.
OsvVulnerScannerStep.configPath.name=Config File
OsvVulnerScannerStep.configPath.placeholder=No config file
OsvVulnerScannerStep.configPath.desc=Optionally specify osv scanner <a href='https://google.github.io/osv-scanner/configuration/' target='_blank'>config file</a> under <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>. You may ignore particular vulnerabilities via this file
OsvScanStep.scanPaths.name=Scan Paths
OsvScanStep.scanPaths.placeholder=Job workspace
OsvScanStep.scanPaths.desc=Optionally specify relative paths under <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to scan dependency vulnerabilities. Multiple paths can be specified and should be separated with space. Leave empty to use job workspace itself
OsvScanStep.recursive.name=Recursive
OsvScanStep.recursive.desc=Whether to scan recursively in above paths
OsvScanStep.ft.name=Fail Threshold
OsvScanStep.ft.desc=Fail build if there are vulnerabilities with or severer than specified severity level
OsvScanStep.reportName.name=Report Name
OsvScanStep.reportName.desc=Specify name of the report to be displayed in build detail page
NoSubscriptionLog=This step requires an enterprise subscription. Visit https://devgrip.net/pricing to get a 30-days trial license
OsvLicenseScannerStep.name=Osv License Scanner
OsvLicenseScannerStep.desc=This step runs osv scanner to scan violated licenses used by various <a href='https://deps.dev/' target='_blank'>dependencies</a>. It can only be executed by docker aware executor.
OsvLicenseScannerStep.al.name=Allowed Licenses
OsvLicenseScannerStep.al.desc=Specify allowed <a href='https://spdx.org/licenses/' target='_blank'>spdx license identifiers</a> <span class='text-warning'>separated by comma</span>
FSScannerStep.name=Trivy Filesystem Scanner
FSScannerStep.desc=This step runs trivy filesystem scanner to scan various <a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>lock files</a>. It can only be executed by a docker aware executor, and is recommended to run <span class='text-warning'>after dependencies are resolved</span> (npm install or alike). Compared to OSV scanner, its setup is a bit verbose, but can provide more accurate result
DescWithNoSubscription=<br><br><b class='text-danger'>NOTE: </b> This step requires an enterprise subscription. <a href='https://devgrip.net/pricing' target='_blank'>Try free</a> for 30 days
FSScannerStep.scanPath.name=Scan Path
FSScannerStep.scanPath.placeholder=Job workspace
FSScannerStep.scanPath.desc=Optionally specify relative path under <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to scan. Leave empty to use job workspace itself
ImageScannerStep.name=Trivy Container Image Scanner
ImageScannerStep.desc=This step runs trivy container image scanner to find issues in specified image. For vulnerabilities, it checks various <a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>distribution files</a>It can only be executed by docker aware executor.
ImageScannerStep.scanPath.name=OCI Layout Directory
ImageScannerStep.scanPath.desc=Specify OCI layout directory of the image to scan. This directory can be generated via build image step or pull image step. It should be relative to <a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>job workspace</a>
ImageScannerStep.platforms.name=Platforms
ImageScannerStep.platforms.placeholder=All platforms in OCI layout
ImageScannerStep.platforms.desc=Optionally specify <span class='text-info'>comma separated</span> platforms to scan, for instance <tt>linux/amd64,linux/arm64</tt>. Leave empty to scan all platforms in OCI layout
LicenseSetting.ignoreLic.name=Ignored Licenses
LicenseSetting.ignoreLic.placeholder=No ignored licenses
LicenseSetting.ignoreLic.desc=Optionally specify comma separated licenses to be ignored
RootFSScannerStep.name=Trivy Rootfs Scanner
RootFSScannerStep.desc=This step runs trivy rootfs scanner to scan various <a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>distribution files</a>. It can only be executed by a docker aware executor and is recommended to run against staging area of your project
RootFSScannerStep.scanPath.name=Scan Path
RootFSScannerStep.scanPath.placeholder=Job workspace
RootFSScannerStep.scanPath.desc=Optionally specify relative path under <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to scan. Leave empty to use job workspace itself
SecretSetting.configPath.name=Secret Config File
SecretSetting.configPath.placeholder=No secret config
SecretSetting.configPath.desc=Optionally specify path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to be used as trivy <a href='https://aquasecurity.github.io/trivy/v0.50/docs/scanner/secret/#configuration' target='_blank'>secret config</a>
TrivyCacheStep.name=Set Up Trivy Cache
TrivyCacheStep.desc=This step set up trivy db cache to speed up various trivy scanner steps
TrivyCacheStep.key.name=Cache Key
TrivyCacheStep.key.desc=This key is used to download and upload cache in project hierarchy (search from current project to root project in order)
TrivyCacheStep.uploadProjectPath.name=Upload Project Path
TrivyCacheStep.uploadProjectPath.placeholder=Current project
TrivyCacheStep.uploadProjectPath.desc=In case cache needs to be uploaded, this property specifies target project for the upload. Leave empty for current project
TrivyCacheStep.uploadAts.name=Upload Access Token Secret
TrivyCacheStep.uploadAts.desc=Specify a secret whose value is an access token with upload cache permission for above project. Note that this property is not required if upload cache to current or child project and build commit is reachable from default branch
TrivyScanStep.dv.name=Detect Vulnerabilities
TrivyScanStep.ds.name=Detect Secrets
TrivyScanStep.dl.name=Detect Licenses
TrivyScanStep.skipDirs.name=Directories to Skip
TrivyScanStep.skipDirs.placeholder=No directories to skip
TrivyScanStep.skipDirs.desc=Optionally specify directories or glob patterns inside scan path to skip. Multiple skips should be separated by space
TrivyScanStep.ignoreFile.name=Ignore File
TrivyScanStep.ignoreFile.placeholder=No ignore file
TrivyScanStep.ignoreFile.desc=Optionally specify path relative to <a href='https://docs.devgrip.net/concepts#job-workspace'>job workspace</a> to be used as trivy <a href='https://aquasecurity.github.io/trivy/v0.50/docs/configuration/filtering/#by-finding-ids' target='_blank'>ignore file</a>
TrivyScanStep.ft.name=Fail Threshold
TrivyScanStep.ft.desc=Fail build if there are vulnerabilities with or severer than specified severity level
TrivyScanStep.reportName.name=Report Name
TrivyScanStep.reportName.desc=Specify name of the report to be displayed in build detail page
RenovateStep.name=Update Dependencies via Renovate
RenovateStep.desc=Run <a href='https://github.com/renovatebot/renovate' target='_blank'>renovate cli</a> to update dependencies via pull requests
RenovateStep.projects.name=Projects
RenovateStep.projects.placeholder=Current project
RenovateStep.projects.desc=Specify projects to update dependencies. Leave empty for current project
RenovateStep.includeChildProjects.name=Include Child Projects
RenovateStep.includeChildProjects.desc=Whether to also include children of above projects
RenovateStep.ats.name=Access Token Secret
RenovateStep.ats.desc=Specify <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> whose value is access token with code write permission over above projects. Commits, issues, and pull requests will also be created under name of the access token owner
RenovateStep.ics.name=Issue Close States
RenovateStep.ics.desc=Specify which states are considered as closed for various issues created by Renovate to orchestrate dependency update. Additionally, when Renovate closes the issue, DevGrip will transit the issue to first state specified here
RenovateStep.issueFields.name=Issue Fields
RenovateStep.issueFields.desc=Specify fields of various issues created by Renovate to orchestrate the dependency update
RenovateStep.dms.name=Merge Strategy
RenovateStep.dms.placeholder=Default merge strategy
RenovateStep.dms.desc=Optionally specify merge strategy of created pull request. Leave empty to use default strategy of each project
RenovateStep.rjsc.name=Renovate JavaScript Config
RenovateStep.rjsc.desc=Optionally specify JavaScript config to be used by Renovate CLI
RenovateStep.ghats.name=GitHub Access Token Secret
RenovateStep.ghats.desc=Optionally specify <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>job secret</a> to be used as GitHub access token. This is used to retrieve release notes of dependencies hosted on GitHub, and the authenticated access will get a higher rate limit
RenovateStep.cliOpt.name=Renovate CLI Options
RenovateStep.cliOpt.desc=Optionally specify options passed to renovate cli. Multiple options should be separated by space, and single option containing spaces should be quoted
RenovateStep.error.unableFindField=unable to find issue field: %s
RenovateStep.error.unableFindIssueState=unable to find issue state: %s
RenovateStep.error.noVerifiedEmail=no verified email: %s
RenovateStep.error.noCloseStates=Please specify issue close states
RenovateStep.error.noIssueFields=Please specify issue fields
RenovateCacheStep.name=Set Up Renovate Cache
RenovateCacheStep.desc=This step sets up Renovate cache. Place it before Renovate step if you want to use it
EntityQuery.error.operator=Field '%s' is not applicable for operator '%s'
EntitytQuery.error.fieldNotFound=Field not found: %s
EntitytQuery.error.canNotOrderBy=Can not order by field: %s
EntitytQuery.error.unableFindBuild=Unable to find build: %s
EntitytQuery.error.invalidField=Invalid field: %s
EntitytQuery.error.projectQueryNotSupport=Project criteria is not supported here
EntitytQuery.error.jobQueryNotSupport=Job criteria is not supported here
EntitytQuery.error.unableFindGroup=Unable to find group: %s
EntitytQuery.error.unableFindUser=Unable to find user: %s
EntitytQuery.error.groupIsIncludeMultipeTimes=Group '%s' is included multiple times
EntitytQuery.error.userIsIncludeMultipeTimes=User '%s' is included multiple times
EntitytQuery.error.malformed=Malformed query
DingtalkNotificationSetting.name=Dingtalk Notifications Setting
DingtalkNotificationSetting.desc=Set up dingtalk notification settings. Settings will be inherited by child projects, and can be overridden by defining settings with same webhook url
DingtalkNotificationSetting.needSign.name=Signing
DingtalkNotificationSetting.needSign.desc=Whether to use the signing method, If you have configured the signing method on the DingTalk side, It should be enabled here.
DingtalkNotificationSetting.secret.name=The Signing Secret
DingtalkNotificationSetting.secret.desc=Specify the signing secret on the DingTalk side. The secret is the string starting with “SEC” displayed under the signing section on the bot’s security settings page.
DingtalkNotificationSetting.needKeyword.name=Custom Keyword
DingtalkNotificationSetting.needKeyword.desc=Whether to use the custom keyword method, If you have configured the costum method on the DingTalk side, It should be enabled here.
DingtalkNotificationSetting.keyword.name=The Custom Keyword
DingtalkNotificationSetting.keyword.desc=Specify the custom keyword on the DingTalk side. If there are multiple, you only need to select one of them.
ClickForMoreDetails=Click for more details
ChannelNotification.webhookUrl.name=Webhook Url
ChannelNotification.webhookUrl.desc=Specify webhook url to post events
ChannelNotification.notifyIssueEvents.name=Notify Issue Events
ChannelNotification.applicableIssues.name=Applicable Issues
ChannelNotification.notifyPrEvents.name=Notify Pull Request Events
ChannelNotification.applicablePr.name=Applicable Pull Requests
ChannelNotification.notifyBuildEvents.name=Notify Build Events
ChannelNotification.applicableBuilds.name=Applicable Builds
ChannelNotification.notifyCodePushEvents.name=Notify Code Push Events
ChannelNotification.applicableCommits.name=Applicable Commits
ChannelNotification.notifyCodeCommentEvents.name=Notify Code Comment Events
ChannelNotification.applicableCodeComments.name=Applicable Code Comments
DiscordNotificationSetting.name=Discord Notifications Setting
DiscordNotificationSetting.desc=Set up discord notification settings. Settings will be inherited by child projects, and can be overridden by defining settings with same webhook url
NtfyNotificationSetting.name=Ntfy.sh Notifications Setting
NtfyNotificationSetting.desc=Set up ntfy.sh notification settings. Settings will be inherited by child projects, and can be overridden by defining settings with same webhook url
SlackNotificationSetting.name=Slack Notifications Setting
SlackNotificationSetting.desc=Set up slack notification settings. Settings will be inherited by child projects, and can be overridden by defining settings with same webhook url
WecomNotificationSetting.name=Wecom Notifications Setting
WecomNotificationSetting.desc=Set up wecom notification settings. Settings will be inherited by child projects, and can be overridden by defining settings with same webhook url
WecomNotificationSetting.imageNotSupport=Does not support displaying images
ChannelNotificationManager.issueSummary=[Issue %s] (%s)
ChannelNotificationManager.prSummary=[Pull Request %s] (%s)
ChannelNotificationManager.buildTitleWithVersion=[Build %s] (%s: %s) %s
ChannelNotificationManager.buildTitleWithoutVersion=[Build %s] (%s) %s
ChannelNotificationManager.packTitle=[%s %s] Package published
ChannelNotificationManager.commitTitle=[Commit %s:%s] (%s) %s
ChannelNotificationManager.codeCommentTitle=[Code Comment %s:%s] %s %s
RefUpdated.activity.branch=branch '%s' updated
RefUpdated.activity.tag=tag '%s' created
RefUpdated.activity.ref=ref '%s' updated
IssueNotificationManager.issueYou=[Issue %s] (%s: You) %s
IssueNotificationManager.mentionYou=[Issue %s] (Mentioned You) %s
IssueNotificationManager.issueBcc=[Issue %s] (%s) %s
IssueNotificationManager.issueBccOpened=Opened
IssueNotificationManager.issueBccUpdated=Updated
IssueNotificationManager.issueBccMoved=Moved
IssueNotificationManager.prevProject=Previous project: %s
IssueNotificationManager.currentProject=Current project: %s
PullRequestNotificationManager.prBcc=[Pull Request %s] (%s) %s
PullRequestNotificationManager.prAssigned=[Pull Request %s] (Assigned) %s
PullRequestNotificationManager.assignedToYou1=assigned to you
PullRequestNotificationManager.assignedToYou2=Assigned to you
PullRequestNotificationManager.prReviewRequest=[Pull Request %s] (Review Request) %s
PullRequestNotificationManager.requestedReviewFromYou1=requested review from you
PullRequestNotificationManager.requestedReviewFromYou2=Requested review from you
PullRequestNotificationManager.prMentioned=[Pull Request %s] (Mentioned You) %s
PullRequestNotificationManager.bccOpened=Opened
PullRequestNotificationManager.bccUpdated=Updated
PackNotificationManager.published=[%s %s] Package published
PackNotificationManager.publishedByUser=Package published by user %s
PackNotificationManager.publishedViaBuild=Package published via build %s
CommitNotificationManager.summary=Commit authored by %s
CodeCommentNotificationManager.title=[Code Comment %s:%s] %s
BuildNotificationManager.subjectWithVersion=[Build %s] (%s: %s) %s
BuildNotificationManager.subjectWithoutVersion=[Build %s] (%s) %s
BuildNotificationManager.summaryBranch=%s on branch %s
BuildNotificationManager.summaryTag=%s on tag %s
BuildNotificationManager.summaryPr=%s on pull request %s
BuildNotificationManager.summaryRef=%s on ref %s
IssueWorkBean.name=Log Work
IssueWorkBean.addSpentTime.name=Add Spent Time
IssueWorkBean.addSpentTime.desc=Add spent time <b class='text-warning'>only for this issue</b>, not counting '%s'
IssueWorkBean.startAt.name=Start At
IssueWorkBean.startAt.desc=When this work starts
IssueWorkBean.note.name=Note
IssueWorkBean.note.desc=Optionally leave a note
PackDetailPage.entityName=package
PackDetailPage.deleteConfirm=Do you really want to delete this package?
PackDetailPage.deleteSuccess=Package ${name} deleted
Stats.countAndStatsLabel=${count} ${status}
PackStats.countAndTypeLabel=${count} ${type}(s)
ProjectSelector.defaultQuery=Default Query
VerticalBeanListPropertyEditor.mustNotBeNull=must not be null
DefaultIssueChangeManager.stateChangeAsIssueTransited=State changed as issue %s transited to '%s'
DefaultIssueChangeManager.stateChangeAsBuildIsSuccessful=State changed as build %s is successful
DefaultIssueChangeManager.stateChangeAsPullRequestIs=State changed as pull request %s is %s
DefaultIssueChangeManager.stateChangeAsCodeFixing=State changed as code fixing the issue is committed (%s)
DisplayIssueFields.state=State
DisplayIssueFields.iteration=Iteration
DisplayGroupBy.project=Project
ReactionListPanel.displayAndMore=\ and ${count} more
ReactionListPanel.addReactionTitle=Add reaction
ReactionListPanel.thumbsUp=Thumbs up
ReactionListPanel.thumbsDown=Thumbs down
ReactionListPanel.smile=Smile
ReactionListPanel.tada=Tada
ReactionListPanel.confused=Confused
ReactionListPanel.heart=Heart
ReactionListPanel.rocket=Rocket
ReactionListPanel.eyes=Eyes
BasePage.eeContent=ee
ModalPanel.closeConfirm=There are unsaved changes, do you want to close?
DefaultUserInvitationManager.subject=[Invitation] You are Invited to Use DevGrip
PagingNavigator.first=Go to first page
PagingNavigator.previous=Go to previous page
PagingNavigator.next=Go to next page
PagingNavigator.last=Go to last page
PagingNavigation.page=Go to page ${page}
IssueBatchUpdateData.state=State
IssueBatchUpdateData.confidential=Confidential
IssueBatchUpdateData.iterations=Iterations
LayoutPage.collapseOrExpand=Click to collapse or expand
Usage.inusagePrefix=%s is still being used in below places:
LinkSide.linkNotFound=Link spec not found: %s
Usage.applicableJob=applicable jobs
BitbucketCloud.BitbucketProjectImporter.step1.title=Authenticate to Bitbucket Cloud
BitbucketCloud.BitbucketProjectImporter.step2.title=Specify repositories
BitbucketCloud.BitbucketProjectImporter.step3.title=Specify import option
BitbucketCloud.ImportOption.publicRoleName.name=Public Roles
BitbucketCloud.ImportOption.publicRoleName.desc=If specified, all public repositories imported from Bitbucket will use this as default roles. Private repositories are not affected
BitbucketCloud.ImportRepositories.parent.name=Parent Project
BitbucketCloud.ImportRepositories.parent.desc=Optionally specify a DevGrip project to be used as parent of imported repositories. Leave empty to import as root projects
BitbucketCloud.ImportRepositories.all.name=Import All Repositories
BitbucketCloud.ImportRepositories.includeForks.name=Include Forks
BitbucketCloud.ImportRepositories.includeForks.desc=Whether to import forked Bitbucket repositories
BitbucketCloud.ImportRepositories.bitbucktRepoToImport.name=Bitbucket Repositories to Import
BitbucketCloud.ImportServer.userName.name=Bitbucket Login Name
BitbucketCloud.ImportServer.userName.desc=Your Bitbucket account username (not email)
BitbucketCloud.ImportServer.appPassword.name=Bitbucket App Password
BitbucketCloud.ImportServer.appPassword.desc=Bitbucket app password should be generated with permission <b>account/read</b>, <b>repositories/read</b> and <b>issues:read</b>
BitbucketCloud.importWorkspace.workspace.name=Bitbucket Workspace
BitbucketCloud.importWorkspace.workspace.desc=Select workspace to import from
BitbucketCloud.importWorkspace.includeForks.name=Include Forks
BitbucketCloud.importWorkspace.includeForks.desc=Whether to include forked repositories
UnitTestModule.title=Unit Test
CoverageModule.title=Coverage
UnitTestMetric.group.successRate=Success Rate
UnitTestMetric.group.totalNumber=Total Number
UnitTestMetric.group.totalTestDuration=Total Test Duration
UnitTestMetric.name.testCase=Test Case
UnitTestMetric.name.testSuite=Test Suite
UnitTestMetric.name.totalTestDuration=Total Test Duration
ProblemMetric.group.totalProbelms=Total Problems
ProblemMetric.name.criticalSeverity=Critical Severity
ProblemMetric.name.highSeverity=High Severity
ProblemMetric.name.mediumSeverity=Medium Severity
ProblemMetric.name.lowSeverity=Low Severity
CoverageMetric.group.codeCoverage=Code Coverage
CoverageMetric.name.branch=Branch
CoverageMetric.name.line=Line
Nodata.label=No Data
Commits.label=commits
GlobalWithI=<i>Global</i>
QueryWatchesPanel.deleteSelected=Delete Selected
QueryWatchesPanel.selectToDelete=Please select query watches to delete
QueryWatchesPanel.selectToDeleteConfirm=Do you really want to delete selected query watches?
QueryWatchesPanel.statusWatched=Watch
QueryWatchesPanel.statusIgnore=Ignore
QueryWatchesPanel.suffixCommon=\ (common)
QueryWatchesPanel.suffixPersonal=\ (personal)
IssueQueryWatchesPanel.desc=Issue query watch only affects new issues. To manage watch status of existing issues in batch, filter issues by watch status in issues page, and then take appropriate action
QueryWatchesPanel.watchStatus=Watch Status
PrQueryWatchesPanel.desc=Pull request query watch only affects new pull requests. To manage watch status of existing pull requests in batch, filter pull requests by watch status in pull requests page, and then take appropriate action
QueryWatchesPanel.issue=Issue
QueryWatchesPanel.build=Build
QueryWatchesPanel.pr=Pull Request
QueryWatchesPanel.pack=Pack
QueryWatchesPanel.commit=Commit
ProjectPage.noSavedQueries=No saved queries
ProjectPage.keepOpen=Click to keep saved queries open
ProjectPage.hideAuto=Click to hide save queries automatically
IssueImportPage.desc=Importing issues into currrent project. Please note that issue numbers will only be retained if the whole project fork graph does not have any issues to avoid duplicate issue numbers
IssueImportPage.importingIssuesFrom=Importing issues from ${name}
QueryFilter=Filter
QueryFilter.desc=These are just some common filtering criteria. For more filtering combinations, please try typing in the search box above.
ProjectFilterPanel.rootProjects=Root Projects
ProjectFilterPanel.leafProjects=Leaf Projects
ProjectFilterPanel.childProjectsOf=Child Projects Of
ProjectFilterPanel.forksOf=Forks Of
ProjectFilterPanel.owneredBy=Ownered By
QueryFilter.label=Label
QueryFilter.activeSince=Active Since
QueryFilter.activeUntil=Active Until
PullRequestFilterPanel.assignedTo=Assigned To
QueryFilter.submittedBy=Submitted By
QueryFilter.status=Status
BuildFilterPanel.job=Job
BuildFilterPanel.ranOnAgent=Ran On Agent
BuildFilterPanel.submittedAfter=Submitted After
BuildFilterPanel.submittedBefore=Submitted Before
IssueFilterPanel.state=State
IssueFilterPanel.iteration=Iteration
IssueFilterPanel.confidential=Confidential
PackFilterPanel.publishedAfter=Published After
PackFilterPanel.publishedBefore=Published Before
PackFilterPanel.publishedByProject=Published By Project
PackFilterPanel.publishedByUser=Published By User
AgentFilterPanel.os=OS
AgentFilterPanel.osArch=OS Arch
AgentFilterPanel.paused=Paused
AgentFilterPanel.hasRunningBuilds=Has Running Builds
AgentFilterPanel.everUsedSince=Used Since
AgentFilterPanel.notUsedSince=Not Used Since
CommitFilterPanel.branch=Branch
CommitFilterPanel.tag=Tag
CommitFilterPanel.build=Build
CommitFilterPanel.touchedFile=Touched File
CommitFilterPanel.authoredBy=Authored By
CommitFilterPanel.committedBy=Committed By
CommitFilterPanel.committedAfter=Committed After
CommitFilterPanel.committedBefore=Committed Before
CodeCommentFilterPanel.createdBy=Created By
Pack.containerImage.name=Container Image
StatsGroup.defaultWeekFormatterPattern=w%02d
InsertAdmonitionPanel.pageTitle=Insert Admonition
InsertAdmonitionPanel.admTitleLabel=Title
InsertAdmonitionPanel.admContentLabel=Content
InsertAdmonitionPanel.admTypeLabel=Type
InsertAdmonitionPanel.admContentCollapseLabel=Allow content collapsing
InsertAdmonitionPanel.summary=summary
InsertAdmonitionPanel.bug=bug
InsertAdmonitionPanel.caution=caution
InsertAdmonitionPanel.error=error
InsertAdmonitionPanel.example=example
InsertAdmonitionPanel.failure=failure
InsertAdmonitionPanel.question=question
InsertAdmonitionPanel.help=help
InsertAdmonitionPanel.info=info
InsertAdmonitionPanel.note=note
InsertAdmonitionPanel.warning=warning
InsertAdmonitionPanel.success=success
InsertAdmonitionPanel.done=done
InsertAdmonitionPanel.tip=tip
InsertAdmonitionPanel.important=important
InsertAdmonitionPanel.error.titleTooLong=Title exceeds maximum length of 100 characters
InsertAdmonitionPanel.error.contentTooLong=Content exceeds maximum length of 3000 characters
TopbarOnlyLayoutPage.switchTheme=Change color, feel better!
TopbarOnlyLayoutPage.caremelbrown=Caramel Brown
TopbarOnlyLayoutPage.teal=Teal(Default)
TopbarOnlyLayoutPage.violet=Violet
TopbarOnlyLayoutPage.indigo=Indigo
TopbarOnlyLayoutPage.crimson=Crimson
TopbarOnlyLayoutPage.pastelpink=Pastel Pink
TopbarOnlyLayoutPage.oliveGreen=Olive Green
TwoFactorAuthenticationStatusPanel.alert.alreadySetup=Two-factor authentication already set up. 
TwoFactorAuthenticationStatusPanel.alert.clickHere=Click here 
TwoFactorAuthenticationStatusPanel.alert.reset=to reset if you've lost both your TOTP authenticator and access codes
TwoFactorAuthenticationStatusPanel.notConfigureAlert=Will be prompted to set up two-factor authentication upon next login
TwoFactorAuthenticationSetupPanel.twoFactorIsEnforced=Two-factor authentication is enforced for your account to enhance security. Please follow below procedure to set it up
TwoFactorAuthenticationSetupPanel.scan=Scan below QR code with your TOTP authenticators
TwoFactorAuthenticationSetupPanel.mobileApps=TOTP-compatible mobile apps (including Google Authenticator, Microsoft Authenticator, Authy, 1Password etc.)
TwoFactorAuthenticationSetupPanel.enterPasscode=Then enter the passcode shown in the TOTP authenticator to verify
TwoFactorAuthenticationSetupPanel.enterSecretKey=Alternatively, manually enter the secret key in your authenticator app
TwoFactorAuthenticationSetupPanel.configureSuccess=Two-factor authentication is now configured
TwoFactorAuthenticationSetupPanel.important=IMPORTANT:
TwoFactorAuthenticationSetupPanel.please=Please
TwoFactorAuthenticationSetupPanel.download=download
TwoFactorAuthenticationSetupPanel.recovery=recovery codes below and keep them secret. These codes can be used to provide one-time access to your account in case you can not access the authentication application. They will <b>NOT</b> be displayed again 
TwoFactorAuthenticationSetupPanel.error.passcodeEmpty=Please input passcode
TwoFactorAuthenticationSetupPanel.error.passcodeError=Passcode incorrect
Passcode.placeholder=6-digits passcode
RecoveryCode.placeholder=Recovery code
Passcode.name=Passcode
RecoveryCode.name=Recovery code
ThirdPartyLogin=Sign in with
ExternalIssueTransformers.issuePattern.name=Issue Pattern
ExternalIssueTransformers.issuePattern.desc=Specify a &lt;a href='https://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html'&gt;regular expression&lt;/a&gt; to match issue references. For instance:&lt;br&gt; &lt;em&gt;(^|\\W)([A-Z][A-Z]+-\\d+)(?=\\W|$)&lt;/em&gt;
ExternalIssueTransformers.replaceWith.name=External Issue Link Template
ExternalIssueTransformers.replaceWith.desc=Specify a template for external issue links. You can use capture groups like $1 and $2. For example:&lt;br&gt;&lt;em&gt;$1&amp;lt;a href='https://example.com/issues/$2'&amp;gt;$2&amp;lt;/a&amp;gt;&lt;/em&gt;
ExternalIssueTransformersPage.tips=When using an external issue tracking system, you can configure transformers here to convert issue references into external links in places like commit messages and pull requests. Here are some example configurations for your reference:
ExternalIssueTransformersPage.tips.th1=Issue Reference
ExternalIssueTransformersPage.tips.th2=Original Link (Fictional)
ExternalIssueTransformersPage.githubDesc=Reference issue number 5 from a GitHub project
ExternalIssueTransformersPage.gitlabDesc=Reference issue number 1 from a GitLab project
ExternalIssueTransformersPage.jiraDesc=Reference issue DG-1 from a JIRA project
ExternalIssueTransformersPage.ytDesc=Reference issue IJPL-111016 from a YouTrack project
ExternalIssueTransformersPage.planeDesc=Reference issue DEVLA-16 from a Plane project
ExternalIssueTransformersPage.updateSuccess=Settings updated
ExternalIssueTransformersPage.topbarTitle=External Issue Linking
ShowMatchingAgents.title=Show matching agents
AnyAgentWithI=<i>Any agent</i>
DefaultBaseProjectRoleManager.usageRoleName=Role '%s'
ChildLinkPanel.exceededLimit=Exceeded the limit for displaying projects
PasswordRule.minLen.name=Minimum Length
PasswordRule.minLen.desc=Minimum number of characters for password
PasswordRule.containUppercase.name=Must Contain Uppercase
PasswordRule.containUppercase.desc=Require at least one uppercase letter in password
PasswordRule.containLowercase.name=Must Contain Lowercase
PasswordRule.containLowercase.desc=Require at least one lowercase letter in password
PasswordRule.containDigit.name=Must Contain Digit
PasswordRule.containDigit.desc=Require at least one number in password
PasswordRule.containSpecial.name=Must Contain Special Character
PasswordRule.containSpecial.desc=Require at least one special character in password, such as !@#$%^&*
PasswordRule.error.prefix=Password requirements:\n
PasswordRule.error.minLen=Minimum length: %s characters\n
PasswordRule.error.mustContainUppercase=Must contain at least one uppercase letter\n
PasswordRule.error.mustContainLowercase=Must contain at least one lowercase letter\n
PasswordRule.error.mustContainNumber=Must contain at least one number\n
PasswordRule.error.mustContainSpecial=Must contain at least one special character\n
Slogan=DevGrip - A powerful tool for your software development lifecycle, providing a more enjoyable experience for Git, CI/CD, packages, and issue management.
MarkdownEditor.restoreFromUnsaved=Below content is restored from an unsaved change.
MarkdownEditor.textFormat=Text format
MarkdownEditor.Bold=Bold
MarkdownEditor.Italic=Italic
MarkdownEditor.Heading=Format H1
MarkdownEditor.Heading2=Format H2
MarkdownEditor.Heading3=Format H3
MarkdownEditor.Heading4=Format H4
MarkdownEditor.Heading5=Format H5
MarkdownEditor.Heading6=Format H6
MarkdownEditor.Link=Link
MarkdownEditor.Image=Image
MarkdownEditor.UnorderedList=Unordered list
MarkdownEditor.OrderedList=Ordered list
MarkdownEditor.TaskList=Task list
MarkdownEditor.Code=Code
MarkdownEditor.Quote=Quote
MarkdownEditor.ShowEmoj=Show emojis
MarkdownEditor.Mention=Mention someone
MarkdownEditor.Reference=Reference issue, build, or pull request
MarkdownEditor.CodeSugg=Code suggestion
MarkdownEditor.ToggleFixedWidthFont=Toggle fixed width font
MarkdownEditor.FullScreen=Full screen
MarkdownEditor.Help=Help
MarkdownEditor.Preview=Preview
MarkdownEditor.Split=Split view
MarkdownEditor.attachFile=You may also drop file/image to the input box, or paste image from clipboard
MarkdownEditor.githubMarkdown=GitHub flavored markdown 
MarkdownEditor.acceptedWith=is accepted, with 
MarkdownEditor.mermaidAndKatex=mermaid and katex support.
MarkdownEditor.refIssue=Reference Issue
MarkdownEditor.refPr=Reference Pull Request
MarkdownEditor.refBuild=Reference Build
MarkdownEditor.nothingPreview=<div class='message'>Nothing to preview</div>
MarkdownViewer.pleaseTryAgain=Someone changed the content you are editing. The content has now been reloaded, please try again.
InsertUrlPanel.insert=Insert
InsertUrlPanel.commitAndInsert=Commit and Insert
InsertUrlPanel.insertLinkToFile=Insert link to this file
InsertUrlPanel.remove=Remove
InsertUrlPanel.removeFile=Remove this file
InsertUrlPanel.noImage=No image attachments
InsertUrlPanel.noFile=No File attachments
InsertUrlPanel.select=Select
InsertUrlPanel.urlLabel.imageUrl=Image URL
InsertUrlPanel.urlLabel.linkUrl=Link URL
InsertUrlPanel.urlHelp.imageHelp=Absolute or relative url of the image
InsertUrlPanel.urlHelp.linkHelp=Absolute or relative url of the link
InsertUrlPanel.urlTxt.imageTxt=Image Text
InsertUrlPanel.urlTxt.linkTxt=Link Text
InsertUrlPanel.error.image=Image URL should be specified
InsertUrlPanel.error.link=Link URL should be specified
InsertUrlPanel.deleteConfirm=Do you really want to delete '${attachName}'?
InsertUrlPanel.attachment=Attachment
InsertUrlPanel.inserLabel.image=Insert Image
InsertUrlPanel.inserLabel.link=Insert Link
InsertUrlPanel.inputUrl=Input URL
InsertUrlPanel.pickExisting=Pick Existing
InsertUrlPanel.upload=Upload
IssueActivitiesPanel.comment.placeholder=Leave a comment
IssueActivitiesPanel.error.submit=Please select fields to update
DropzoneField.dictDefaultMessage=Drop files here or click to upload
DropzoneField.dictFallbackMessage=Your browser does not support drag'n'drop file uploads.
DropzoneField.dictFallbackText=Please use the fallback form below to upload your files like in the olden days.
DropzoneField.dictFileTooBig=File is too big, Max filesize: ${maxFilesize}MiB.
DropzoneField.dictInvalidFileType=You can't upload files of this type.
DropzoneField.dictCancelUpload=Cancel upload
DropzoneField.dictCancelUploadConfirmation=Are you sure you want to cancel this upload?
DropzoneField.dictRemoveFile=Remove file
DropzoneField.dictRemoveFileConfirmation=Are you sure you want to remove this file?
DropzoneField.dictMaxFilesExceeded=You can not upload any more files.
allWithI=<i>All</i>
ServiceLocatorListEditPanel.colServices=Applicable Services
ServiceLocatorListEditPanel.colImages=Applicable Images
ServiceLocatorListEditPanel.colNodeSel=#Node Selector Entries
ServiceLocatorEditPanel.title=Service Locator
AgentStatusBadage.online=Online
AgentStatusBadage.offline=Offline
AgentStatusBadage.paused=Paused
WatchStatusPanel.defaultTitle=Watch if involved (default)
WatchStatusPanel.defaultDesc=Start to watch once I am involved
WatchStatusPanel.watchTitle=Watch
WatchStatusPanel.watchDesc=You will be notified of any activities
WatchStatusPanel.ignoreTitle=Ignore
WatchStatusPanel.ignoreDesc=Ignore activities irrelevant to me
SubscriptionStatusLink.titleWatched=Watched. Click to unwatch
SubscriptionStatusLink.titleUnwatched=Unwatched. Click to watch
FuzzyQueryTips=Performing fuzzy query. Enclosing search text with '~' to add more conditions, for instance: ~text to search~ and ${search}
groupchoice.notSpecified=Not Specified
groupchoice.chooseGroup=Choose group...
profileEditPanel.enable=Enable
profileEditPanel.disable=Disable
profileEditPanel.enable.success=User enabled
profileEditPanel.enable.confirm=Do you really want to enable this account?
profileEditPanel.disable.success=User disabled
profileEditPanel.disable.confirm=Disabling account will reset password, clear access tokens, and remove all references from other entities except for past activities. Do you really want to continue?
profileEditPanel.nameAlreadyInUsed=Login name already used by another account
profileEditPanel.updateSuccess=Profile updated
passwordEditPanel.buttonChange=Change
passwordEditPanel.buttonSet=Set
passwordEditPanel.changeSuccess=Password has been changed
passwordEditPanel.setSuccess=Password has been set
passwordEditBean.oldPassword=Old Password
passwordEditBean.newPassword=New Password
userDeleteLink.confirmDeleteYourAccount=Do you really want to remove your account?
userDeleteLink.confirm=Do you really want to delete user ${name}
userDeleteLink.deleteYourAccountSuccess=Your account is deleted
userDeleteLink.deleteSuccess=User ${name} deleted
AccessTokenEditBean.name=name
AccessTokenEditBean.hasOwnerPermissions.name=Has Owner Permissions
AccessTokenEditBean.hasOwnerPermissions.desc=Enable this if the access token has same permissions as the owner
AccessTokenEditBean.authorizedProjects.name=Authorized Projects
AccessTokenEditBean.authorizedProjects.desc=Only projects manageable by access token owner can be authorized
AccessTokenEditBean.expireDate.placeholder=Never expire
AccessTokenEditBean.expireDate.name=Expire Date
EmailAddressesPanel.desc1=Primary email address will be used to receive notifications, show gravatar (if enabled) etc.
EmailAddressesPanel.desc2=git email address will be used as git author/committer for commits created on web UI
EmailAddressesPanel.desc3=When determine if this user is author/committer of a git commit, all emails listed here will be checked.
EmailAddressesPanel.desc4=When determine if you are author/committer of a git commit, all emails listed here will be checked.
EmailAddressesPanel.desc5=Unverified email address is NOT applicable for above functionalities
EmailAddressesPanel.addLable=Add New Email Address
EmailAddressesPanel.resendSuccess=Verification email sent, please check it
EmailAddressesPanel.unableSend=Unable to send verification email as mail service is not configured yet
EmailAddressesPanel.deleteFail=At least one email address should be configured, please add a new one first
EmailAddressesPanel.deleteConfirm=Do you really want to delete this email address?
EmailAddressesPanel.emailAlreadyUsed=This email address is being used
EmailAddressesPanel.emailMalformed=Malformed email address
AvatarEditPanel.current=Current avatar
AvatarEditPanel.uploadTitle=Upload avatar
AvatarEditPanel.uploadButton=Upload
ConfirmativePasswordPropertyEditor.confirmIsEmpty=Please confirm the password.
ConfirmativePasswordPropertyEditor.passwordNotEq=Password and its confirmation should be identical.
modal.confirm.title=Please Confirm
InsertSshKeyPanel.title=Add a SSH Key
InsertSshKeyPanel.add.fail=This key is already in use
SshKeyListPanel.fingerprint=Fingerprint
SshKeyListPanel.comment=Comment
SshKeyListPanel.noComment=No Comment
SshKeyListPanel.createAt=Created At
SshKeyListPanel.deleteSuccess=SSH key deleted
SshKeyListPanel.deleteConfirm=Do you really want to delete this SSH key?
InsertGpgKeyPanel.title=Add a GPG Public Key
InsertGpgKeyPanel.addFail.alreadyInUsed=This key or one of its subkey is already in use
InsertGpgKeyPanel.addFail.emailNotVerified=This key is associated with ${email}, however it is NOT a verified email address of ${who}
InsertGpgKeyPanel.addFail.who1=yours
InsertGpgKeyPanel.addFail.who2=the user
GpgKeyListPanel.ineffective=ineffective
GpgKeyListPanel.KeyID=Key ID
GpgKeyListPanel.email=Email Addresses
GpgKeyListPanel.subKeys=Sub Keys
GpgKeyListPanel.None=<i>None</i>
GpgKeyListPanel.deleteSuccess=GPG key deleted
GpgKeyListPanel.deleteConfirm=Do you really want to delete this GPG key?
AccessTokenPanel.deleteConfirm=Do you really want to delete this access token?
AccessTokenPanel.regenTitle=Regenerate this access token
AccessTokenPanel.regenConfirm=This will invalidate current access token and generate a new one. Do you really want to continue?
AccessTokenPanel.regenSuccess=Access token regenerated successfully
AccessTokenEditPanel.addFail.nameAlreadyUsed=Name already used by another access token of the owner
ConfirmLeaveListener.confirmLeaveMsg=There are unsaved changes, do you want to discard and continue?
SavedQueriesPanel.useDefaultConfirm=This will discard all project specific queries, do you want to continue?
SavedQueriesPanel.saveFail=Duplicate name found: ${name}
SavedQueriesPanel.autoHide.title=Enable auto-hide. after the mouse moves away, the saved queries side automatically hides
JobPrivilegeEditPanel.title=Job Privilege
JobPrivilegeListEditPanel.jobNames=Job Names
JobPrivilegeListEditPanel.privilege=Privilege
JobPrivilegeListEditPanel.manageJob=manage job
JobPrivilegeListEditPanel.runJob=run job
JobPrivilegeListEditPanel.access.log=log
JobPrivilegeListEditPanel.access.pipeline=pipeline
JobPrivilegeListEditPanel.access.reports=reports: ${reports}
JobPrivilegeListEditPanel.access.artifacts=artifacts
JobPrivilegeListEditPanel.access.all=access [${all}]
JobPrivilegeListEditPanel.deleteConfirm=Do you really want to delete this privilege?
Role.pm.name=Project Management
Role.pm.desc=Administrative permission over a project
Role.ccp.name=Create Child Projects
Role.ccp.desc=Create child projects under a project
Role.prm.name=Pull Request Management
Role.prm.desc=Pull request administrative permission inside a project, including batch operations over multiple pull requests
Role.ccm.name=Code Comment Management
Role.ccm.desc=Code comment administrative permission inside a project, including batch operations over multiple code comments
Role.cp.name=Code Privilege
Role.pp.name=Package Privilege
Role.im.name=Issue Management
Role.im.desc=Issue administrative permission inside a project, including batch operations over multiple issues
Role.aci.name=Access Confidential Issues
Role.aci.desc=This permission enables one to access confidential issues
Role.att.name=Access Time Tracking
Role.att.desc=Whether to be able to access time tracking info of issues
Role.si.name=Schedule Issues
Role.si.desc=This permission enables one to schedule issues into iterations
Role.eif.name=Editable Issue Fields
Role.eif.desc=Optionally specify custom fields allowed to edit when open new issues
Role.eil.name=Editable Issue Links
Role.eil.desc=Optionally specify issue links allowed to edit
Role.bm.name=Build Management
Role.bm.desc=Build administrative permission for all jobs inside a project, including batch operations over multiple builds
Role.uc.name=Upload Cache
Role.uc.desc=Enable to allow to upload build cache generated during CI/CD job. Uploaded cache can be used by subsequent builds of the project as long as cache key matches
Role.jp.name=Job Privileges
ExcludeIssueFields.allExcept=All except
ExcludeIssueFields.excludeFields=Excluded Fields
IncludeIssueFields.specifiedFields=Specified fields
IncludeIssueFields.includeFields=Include Fields
JobPrivilege.jobNames.name=Job Names
JobPrivilege.jobNames.desc=Specify space-separated jobs. Use '*' or '?' for wildcard match. Prefix with '-' to exclude. <b class='text-danger'>NOTE: </b> Permission to access build artifacts will be granted implicitly in matched jobs even if no other permissions are specified here
JobPrivilege.manageJob.name=Manage Job
JobPrivilege.manageJob.desc=Job administrative permission, including deleting builds of the job. It implies all other job permissions
JobPrivilege.runJob.name=Run Job
JobPrivilege.runJob.desc=The permission to run job manually. It also implies the permission to access build log, build pipeline and all published reports
JobPrivilege.accessBuildLog.name=Access Build Log
JobPrivilege.accessBuildLog.desc=The permission to access build log
JobPrivilege.accessBuildPipeline.name=Access Build Pipeline
JobPrivilege.accessBuildPipeline.desc=The permission to access build pipeline
JobPrivilege.accessBuildReports.name=Access Build Reports
JobPrivilege.accessBuildReports.placeholder=No accessible reports
JobPrivilege.accessBuildReports.desc=Optionally specify space-separated reports. Use '*' or '?' for wildcard match. Prefix with '-' to exclude
newInvitation.sendLog=Sending invitation to '%s'...
newInvitation.title=Invite Users
newInvitation.sent=Invitations sent
newInvitation.configureMailSetting=please configure
newInvitation.mailSetting=mail setting first
inviteUserListFilter=Filter by email
resendInvitaion=Resend invitation
cancelInvitation=Cancel invitation
newInvitation.emailAddress=Email Address
mailServiceNotConfigured=Mail service not configured
invitationSentTo=Invitation sent to ${email}
confirmCancelInvitation=Do you really want to cancel invitation to ${email} ?
invitationDeleted=Invitation to ${email} deleted
inviteButton=Invite
invitationBean.email.name=Email Address
invitationBean.email.description=Specify email addresses to send invitations, with one per line
validate.emailAddress.invalid=invalid email address: 
validate.emailAddress.alreadyInUse=Email address already in use: 
validate.emailAddress.alreadyInvited=Email address already invited: 
validate.emailAddress.notFound=At least one email address should be specified
myPage.Title=My
myAccessTokenPage.desc=Access token is intended for api access and repository pull/push. It can not be used to sign in to web ui 
myAccessTokensPage.title=My Access Tokens
myAvatarPage.title=Edit My Avatar
myEmailAddressesPage.title=My Email Addresses
myGpgKeysPage.title=My GPG Keys
myGpgKeysPage.plusButtonTitle=Add GPG Key
myGpgKeysPage.desc=Add GPG keys here to verify commits/tags signed by this user
myGpgKeysPage.howToUse=Check <a href="https://docs.github.com/en/authentication/managing-commit-signature-verification/about-commit-signature-verification#gpg-commit-signature-verification" target="_blank">GitHub's guide</a> on how to generate and use GPG keys to sign your commits
myGpgKeysPage.notBelongTo=Email addresses with <span class="badge badge-warning badge-sm">ineffective</span> mark are those not belong to or not verified by key owner
myPasswordPage.title=Change My Password
myPreferencesPage.title=My Preferences
myProfilePage.title=My Profile
myProfilePage.authWithInternalDatabase.desc=You are authenticating via internal database
myProfilePage.authWithExternalSystem.desc=You are authenticating via external system
mySshKeysPage.title=My SSH Keys
mySshKeysPage_plusButtonTitle=Add SSH Key
myTwoFactorAuthentication.title=Two-factor Authentication
GeneralErrorPage.errorTitle=An unexpected exception occurred
PasswordResetBean.newPwd=Please type your new password
PasswordResetPage.resetPasswordTitle=Resetting Password
PasswordResetPage.resetSuccess=Password changed. Please login with your new password
PasswordResetPage.subject=[Password Reset] You are Requesting to Reset Your DevGrip Password
PasswordResetPage.checkEmail=Please check your email for password reset instructions
PasswordResetPage.unableSend=Unable to send password reset email as mail service is not configured
PasswordResetPage.noUserFound=No user found with login name or email: 
PasswordResetPage.disableUserOrServiceAccount=Can not reset password for service account or disabled user
PasswordResetPage.authViaExternaSystem=Can not reset password for user authenticating via external system
SignUpBean.email=Email Address
SignUpBean.descMsg1=Allowed email domains: 
SignUpBean.descMsg2=, disallowed email domains: 
SignUpBean.descMsg3=Disallowed email domains: 
CreateUserFromInvitationPage.submitError=Login name already used by another account
CreateUserFromInvitationPage.submitSuccess=Account set up successfully
LogoutPage.logoutSuccess=You've been logged out
simple.userSignup.nameAlreadyUsed=Login name already used by another account
simple.userSignup.emailAlreadyUsed=Email address already used by another user
simple.userSignup.emailNotAllowed=This email domain is not accepted for self sign-up
simple.userSignup.success=Account sign up successfully
simple.userSignup.signUp=Sign Up
simple.userSignup.enterYourDetails=Enter your details to create your account
Description=Description
No_Description=No description
Color=Color
None=None
Pull_Requests=Pull Requests
Projects=Projects
Applicable_Projects=Applicable Projects
Any_Project=Any project
System_Settings=System Settings
Security_Settings=Security Settings
Users=Users
Groups=Groups
Invitations=Invitations
User_Management=User Management
Role_Management=Role Management
Group_Management=Group Management
External_Authenticator=External Authenticator
Single_Sign_On=Single Sign On
Authenticator=Authentication Source
SSH_Server_Key=SSH Server Key
GPG_Signing_Key=GPG Signing Key
GPG_Trusted_Keys=GPG Trusted Keys
SSH_GPG_Keys=SSH & GPG Keys
Custom_Fields=Custom Fields
States=States
State_Transitions=State Transitions
Default_Boards=Default Boards
IssueLinks=Links
Time_Tracking=Time Tracking
Description_Templates=Description Templates
Commit_Message_Fix_Patterns=Commit Message Fix Issues
Check_Issue_Integrity=Check Issue Integrity
Issue_Settings=Issue Settings
Job_Executors=Job Executors
Agents=Agents
Mail_Service=Mail Service
Service_Desk_Settings=Service Desk Settings
Issue_Notification=Issue Notification
Issue_Notification_Unsubscribed=Issue Notification Unsubscribed
Service_Desk_Issue_Opened=Service Desk Issue Opened
Service_Desk_Issue_Open_Failed=Service Desk Issue Open Failed
Issue_Stopwatch_Overdue=Issue Stopwatch Overdue
Pull_Request_Notification=Pull Request Notification
Pull_Request_Notification_Unsubscribed=Pull Request Notification Unsubscribed
Build_Notification=Build Notification
Package_Notification=Package Notification
Commit_Notification=Commit Notification
User_Invitation=User Invitation
Email_Verification=Email Verification
Password_Reset=Password Reset
System_Alert=System Alert
Email_Templates=Email Templates
Alert_Settings=Alert Settings
Label_Management=Label Management
Performance_Settings=Performance Settings
Groovy_Scripts=Groovy Scripts
Branding=Branding
Database_Backup=Database Backup
Server_Log=Server Log
Server_Information=Server Information
System_Maintenance=System Maintenance
Administration=Administration
Administrator=Administrator
High_Availability_Scalability=High Avaliability & Scalability
Replicas=Replicas
Subscription_Management=Subscription Management
StorageSetting=Storage Setting
Type_To_Filter=Type To Filter...
Filter_By_Name=Filter by name...
Exited_Impersonation=Exited impersonation
Sign_Out=Sign Out
When=When
Message=Message
Dashboards=Dashboards
Try_Subsciption=Try Subsciption
Get_Support=Get Support
Documentation=Documentation
Support_And_Bug_Report=Support & Bug Report
Restful_API=RESTful API
Incompatibilities=Incompatibilities
about=about
devgrip=DevGrip
Current_Commit=Commit
Current_Version=current version
Copyright=Copyright
Alerts_Title=Alerts
New_Version_Status=New version available. Red for security/critical update, yellow for bug fix, blue for feature update. Click to show changes. Disable in system setting
Toggle_Mode=Toggle dark mode or toggle light mode
Expired=Expired
Role=Role
Project=Project
Git=Git
Sign_In=Sign In
You_Have_Unverified=You have unverified
Primary=Primary
Email_Address=email address
Not_Specified=Not specified
Unspecified=Unspecified
UnspecifiedWithI=<i>Unspecified</i>
Please_Choose=Please choose...
Profile=Profile
Edit_Avatar=Edit Avatar
Edit_Project_Avatar=Edit Avatar
SSH_Keys=SSH Keys
GPG_Keys=GPG Keys
Access_Tokens=Access Tokens
Preferences=Preferences
Two_Factor_Authentication=Two-factor Authentication
Delete_All=Delete All
Title_Hide_Save_Queries=Hide saved queries
More_Info=More info
Delete=Delete
Hide=Hide
Update=Update
Regular_Expression=Regular Expression
Whole_Word=Whole Word
Case_Sensitive=Case Sensitive
File_Name_Patterns=File Name Patterns (separated by comma)
File_Name_Patterns_Desc=File name patterns such as *.java, *.c
Symbol_Name=Symbol Name
Search_For=Search for
Jump_To=jump to
Help=help
Delete_All_Package=Delete All Queried Packages
Delete_Selected_Package=Delete Selected Packages
Query=Query
Save_Query=Save Query
Save_As_Mine=Save As Mine
Mine=Mine
All_Users=All Users
Order_By=Order By
Operations=Operations
Tokens=Tokens
Memory=Memory
Show_Saved_Queries=Show Saved Queries
Saved_Queries=Saved Queries
Edit_Save_Queries=edit save queries
Hide_Save_Queries=hide save queries
Specify_name_of_the_saved_query=Specify name of the saved query
Files=Files
SourceCodes=SourceCodes
Commits=Commits
Branches=Branches
Tags=Tags
Code_Comments=Code Comments
Code_Compare=Code Compare
Code_Search=Code Search
List=List
Boards=Boards
Iterations=Iterations
Builds=Builds
Packages=Packages
Code=Code
Issues=Issues
General_Settings=General
Authorization=Authorization
By_User=By User
By_Group=By Group
Branch_Protection=Branch Protection
Tag_Protection=Tag Protection
Code_Analysis=Code Analysis
Git_Pack_Config=Git Pack Config
Job_Secrets=Job Secrets
Job_Properties=Job Properties
Build_Preserve_Rules=Build Preserve Rules
Default_Fixed_Issue_Filters=Default Fixed Issue Filters
Cache_Management=Cache Management
Service_Desk=Service Desk
Web_Hooks=Web Hooks
Settings=Settings
Statistics=Statistics
Child_Projects=Child Projects
Notification=Notification
Timesheets=Timesheets
Login_Sub_Title=Please sign in with your email or username
Two_Factor_Auth_Enabled=Two-factor authentication is enabled. Please input passcode displayed on your TOTP authenticator.
Setup_Two_Factor_Auth=Set up two-factor authentication
Invalid_Credentials=Invalid credentials
Passcode_Verification_Failed=Passcode verification failed
Input_Recovery_Code=Please input one of your recovery codes saved when enable two-factor authentication
Recovery_Code_Verification_Failed=Recovery code verification failed
Sign_In_Indicator=Sign In 
User_Name=User name
Remember_Me=Remember me
Copy=Copy
Updated_On=Updated on ${lastUpdated}
Loading=Loading...
Specify_Value_Of_The_Environment_Variable=Specify value of the environment variable
Specify_Name_Of_The_Environment_Variable=Specify name of the environment variable
Created=created
Create_New_File=Create New File
Upload_Files=Upload Files
Cancel_Selected_Builds=Cancel Selected Builds
Re-run_Selected_Builds=Re-run Selected Builds
Delete_Selected_Builds=Delete Selected Builds
Cancel_All_Queried_Builds=Cancel All Queried Builds
Re-run_All_Queried_Builds=Re-run All Queried Builds
Delete_All_Queried_Builds=Delete All Queried Builds
Set_Selected_Comments_as_Resolved=Set Selected Comments as Resolved
Set_Selected_Comments_as_Unresolved=Set Selected Comments as Unresolved
Delete_Selected_Comments=Delete Selected Comments
Set_All_Queried_Comments_as_Resolved=Set All Queried Comments as Resolved
Set_All_Queried_Comments_as_Unresolved=Set All Queried Comments as Unresolved
Delete_All_Queried_Comments=Delete All Queried Comments
Set_All_Queried_Comments_as_Read=Set All Queried Comments as Read
Commit=Commit
Discard=Discard
From=From
Sync_Timing_of_Selected_Issues=Sync Timing of Selected Issues
Batch_Edit_Selected_Issues=Batch Edit Selected Issues
Move_Selected_Issues_To=Move Selected Issues To...
Copy_Selected_Issues_To=Copy Selected Issues To...
Delete_Selected_Issues=Delete Selected Issues
Sync_Timing_of_All_Queried_Issues=Sync Timing of All Queried Issues
Batch_Edit_All_Queried_Issues=Batch Edit All Queried Issues
Move_All_Queried_Issues_To=Move All Queried Issues To...
Copy_All_Queried_Issues_To=Copy All Queried Issues To...
Delete_All_Queried_Issues=Delete All Queried Issues
Watch/Unwatch_All_Queried_Issues=Watch/Unwatch All Queried Issues
Set_All_Queried_Issues_as_Read=Set All Queried Issues as Read
Move_Selected_Projects_To=Move Selected Projects To...
Set_Selected_As_Root_Projects=Set Selected As Root Projects
Delete_Selected_Projects=Delete Selected Projects
Move_All_Queried_Projects_To=Move All Queried Projects To...
Set_All_Queried_As_Root_Projects=Set All Queried As Root Projects
Delete_All_Queried_Projects=Delete All Queried Projects
Watch/Unwatch_Selected_Pull_Requests=Watch/Unwatch Selected Pull Requests
Discard_Selected_Pull_Requests=Discard Selected Pull Requests
Delete_Selected_Pull_Requests=Delete Selected Pull Requests
Watch/Unwatch_All_Queried_Pull_Requests=Watch/Unwatch All Queried Pull Requests
Discard_All_Queried_Pull_Requests=Discard All Queried Pull Requests
Delete_All_Queried_Pull_Requests=Delete All Queried Pull Requests
Set_All_Queried_Pull_Requests_as_Read=Set All Queried Pull Requests as Read
Set_As_Primary=Set As Primary
Use_For_Git_Operations=Use For Git Operations
Resend_Verification_Email=Resend Verification Email
Add_before=Add before
Add_after=Add after
Pause_Selected_Agents=Pause Selected Agents
Resume_Selected_Agents=Resume Selected Agents
Restart_Selected_Agents=Restart Selected Agents
Remove_Selected_Agents=Remove Selected Agents
Pause_All_Queried_Agents=Pause All Queried Agents
Resume_All_Queried_Agents=Resume All Queried Agents
Restart_All_Queried_Agents=Restart All Queried Agents
Remove_All_Queried_Agents=Remove All Queried Agents
Delete_Selected_Groups=Delete Selected Groups
Delete_All_Queried_Groups=Delete All Queried Groups
Delete_Selected_Memberships=Delete Selected Memberships
Delete_All_Queried_Memberships=Delete All Queried Memberships
Quick_Search=click to quick search
Advanced_Search=Advanced Search
Outline_Search=Outline Search
Select_Project=Select Project
Select_Branch/Tag=Select Branch/Tag
Set_Up_Your_Account=Set Up Your Account
Email_Address_Verification=Email Address Verification
OOPS_There_Is_An_Error=OOPS! There Is An Error
Unable_To_Delete_Right_Now=Unable To Delete/Disable Right Now
The_object_you_are_deleting_is_still_being_used=The object you are deleting/disabling is still being used
Importing_from=Importing from ${name}...
Test_importing_from=Test importing from ${name}...
Please_wait=Please wait...
Page_Not_Found=Page Not Found
Page_Not_Found_Desc=I didn't eat it. I swear!
Forgotten_Password=Forgotten Password?
Enter_New_Password=Enter New Password
Confirm_Approve=Confirm Approve
Confirm_Request_For_Changes=Confirm Request For Changes
Confirm_Discard=Confirm Discard
Confirm_Reopen=Confirm Reopen
Confirm_Delete_Source_Branch=Confirm Delete Source Branch
Confirm_Restore_Source_Branch=Confirm Restore Source Branch
Enter_your_user_name_or_email_to_reset_password=Enter your user name or email to reset password
Email_Verified=Your email address is now verified
Email_Failed_Verified=Failed to verify your email address
Back_To_Home=Back To Home
Go_Back=Go Back
Show_Error_Detail=Show Error Detail
Import=Import
Save=Save
Download=Download
Add=Add
Search=Search
Text_Search=Text Search
File_Search=Files
Symbol_Search=Symbols
In_Default_Branch=in default branch
In_Projects=In Projects
In_Projects_Placeholder=All projects with code read permission
In_Projects_Desc=Optionally specify space-separated projects to search in. Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude. Leave empty to search in all projects with code read permission
No_Matchs=No matchs
Matches_Found=Matches Found
Match_More=More
Indexing_default_branches=Indexing default branches...
Hint_Need_Quoted=Pattern containing spaces or starting with dash needs to be quoted
Hint_Path_Wildcard_2=Use '**', '*' or '?' for <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>path wildcard match</a>. Prefix with '-' to exclude
Hint_Path_Wildcard_1=Use '*' or '?' for wildcard match. Prefix with '-' to exclude
Hint_Name_Wildcard=Use '*' for wildcard match
Hint_Escape_Quotes=Use '\' to escape quotes
Hint_Escape_Brackets=Use '\' to escape brackets
space=space
To_Navigate= to navigate 
To_Complete= to complete
or=or
All=All
History=History
Clone=Clone
Select_File=Select file...
Search_Branch=Search branch
No_Branches=No branches Found
Create=Create
CreateNew=Create New
Create_Branch=Create Branch
Job=Job
Edit_job=Edit job
Submitted_By=Submitted By
Submitted_At=Submitted At
Submit_Reason=Submit Reason
Retried_At=Retried At
Queueing_Takes=Queueing Takes
Running_Takes=Running Takes
Cancelled_By=Cancelled By
Labels=Labels
Labels_Desc=Labels can be defined in Administration / Label Management
Depends_on=Depends on
Depends_On_Me=depends on me
Show_in_build_list=Show in build list
Display_Params=Display Params
Params_to_Display=Params to Display
Use_Default=Use Default
Use_default=Use default
Edit=Edit
View=View
Quote=Quote
Reply=Reply
Set_Resolved=Set Resolved
Set_Unresolved=Set Unresolved
Unresolved=Unresolved
Resolved=Resolved
Comment=Comment
File_Name=File Name
File_Name_Desc=(* = any string, ? = any character)
Command_Palette=Jump To
Command_Palette_Desc=As long as a feature can be accessed via url, you can input part of the url to match and jump 
No_suggestions=No suggestions
Tab_To_Search= to search
Enter_To_Go= to go
To_Move= to move
To_Close= close
Browse_code=Browse code
More_commits=More commits
Too_many_commits=Too many commits to load
Query_Commits_Hint=Query commits
Create_Tag=Create Tag
Filter_by_path=Filter by path
Revision_Index_In_Progress=Revision indexing in progress... (symbol navigation in revisions will be accurate after indexed)
Outdated=outdated
Please_Note=Please Note
Disabled=Disabled
No_Comment=No comment
Committed=committed
With=,
##### BEGIN 注意 这些都是提示器说明国际化，提示器有值和说明两部分，英文版的无需额外说明，所以国际化值都是空的，这是正常的。######
ValueNeedsToBeEnclosedInParenthesis=value needs to be enclosed in parenthesis
CommitQueryBehavior.fuzzySugg=enclose with ~ to query hash/message
CommitQueryDescriber.branch=
CommitQueryDescriber.tag=
CommitQueryDescriber.commit=
CommitQueryDescriber.build=
CommitQueryDescriber.since=
CommitQueryDescriber.until=
CommitQueryDescriber.defaultBranch=
CommitQueryDescriber.authoredByMe=
CommitQueryDescriber.committedByMe=
CommitQueryDescriber.committer=committed by
CommitQueryDescriber.author=authored by
CommitQueryDescriber.message=message
CommitQueryDescriber.before=before specified date
CommitQueryDescriber.after=after specified date
CommitQueryDescriber.path=touching specified path
CommitQueryBehavior.pm530=5:30pm
CommitQueryBehavior.today=today
CommitQueryBehavior.yesterday=yesterday
CommitQueryBehavior.threeDaysAgo=3 days ago
CommitQueryBehavior.lastWeek=last week
CommitQueryBehavior.fourWeeksAgo=4 weeks ago
CommitQueryBehavior.oneYearAgo=1 year ago
CommitQueryBehavior.yesterdayMidnight=yesterday midnight
PullRequestQueryDescriber.open=
PullRequestQueryDescriber.merged=
PullRequestQueryDescriber.discarded=
PullRequestQueryDescriber.needMyAction=
PullRequestQueryDescriber.toBeReviewedByMe=
PullRequestQueryDescriber.toBeChangedByMe=
PullRequestQueryDescriber.toBeMergedByMe=
PullRequestQueryDescriber.requestedForChangesByMe=
PullRequestQueryDescriber.assignedToMe=
PullRequestQueryDescriber.approvedByMe=
PullRequestQueryDescriber.submittedByMe=
PullRequestQueryDescriber.watchedByMe=
PullRequestQueryDescriber.commentedByMe=
PullRequestQueryDescriber.mentionedMe=
PullRequestQueryDescriber.readyToMerge=
PullRequestQueryDescriber.someoneRequestedForChanges=
PullRequestQueryDescriber.hasPendingReviews=
PullRequestQueryDescriber.hasUnsuccessfulBuilds=
PullRequestQueryDescriber.hasUnfinishedBuilds=
PullRequestQueryDescriber.hasMergeConflicts=
PullRequestQueryDescriber.needActionOf=
PullRequestQueryDescriber.toBeReviewedBy=
PullRequestQueryDescriber.toBeChangedBy=
PullRequestQueryDescriber.toBeMergedBy=
PullRequestQueryDescriber.requestedForChangesBy=
PullRequestQueryDescriber.assignedTo=
PullRequestQueryDescriber.approvedBy=
PullRequestQueryDescriber.submittedBy=
PullRequestQueryDescriber.watchedBy=
PullRequestQueryDescriber.commentedBy=
PullRequestQueryDescriber.mentioned=
PullRequestQueryDescriber.includesCommit=
PullRequestQueryDescriber.includesIssue=
OperatorDescriber.orderBy=
OperatorDescriber.is=
OperatorDescriber.isNot=
OperatorDescriber.contains=
OperatorDescriber.isGreaterThan=
OperatorDescriber.isLessThan=
OperatorDescriber.isSince=
OperatorDescriber.isUntil=
OperatorDescriber.and=
OperatorDescriber.or=
OperatorDescriber.not=
OperatorDescriber.any=
OperatorDescriber.hasAny=
OperatorDescriber.all=
OperatorDescriber.isMe=
OperatorDescriber.isNotMe=
OperatorDescriber.isAfter=
OperatorDescriber.isBefore=
OperatorDescriber.isEmpty=
OperatorDescriber.isNotEmpty=
OperatorDescriber.matching=
OperatorDescriber.anyone=
OperatorDescriber.except=
OperatorDescriber.asc=
OperatorDescriber.desc=
OperatorDescriber.never=
OperatorDescriber.reference=
OperatorDescriber.fuzzy=
OperatorDescriber.exclude=exclude,
OperatorDescriber.space=space
PullRequestQueryDescriber.findPullRequestByNumber=find pull request by number
OrMatchAnotherValue=or match another value
AddAnotherSorting=add another sorting
PullRequestQueryDescriber.field.number=
PullRequestQueryDescriber.field.status=
PullRequestQueryDescriber.field.title=
PullRequestQueryDescriber.field.label=
PullRequestQueryDescriber.field.targetProject=
PullRequestQueryDescriber.field.targetBranch=
PullRequestQueryDescriber.field.sourceProject=
PullRequestQueryDescriber.field.sourceBranch=
PullRequestQueryDescriber.field.description=
PullRequestQueryDescriber.field.comment=
PullRequestQueryDescriber.field.commentCount=
PullRequestQueryDescriber.field.submitDate=
PullRequestQueryDescriber.field.lastActivityDate=
PullRequestQueryDescriber.field.closeDate=
PullRequestQueryDescriber.field.mergeStrategy=
ValueShouldBeQuoted=value should be quoted
PullRequestQueryDescriber.fuzzySugg=enclose with ~ to query title/description/comment
PullRequestQueryDescriber.findPrWithThisNumber=find pull request with this number
ReactionDescriber.thumbsUpCount=
ReactionDescriber.thumbsDownCount=
ReactionDescriber.smileCount=
ReactionDescriber.tadaCount=
ReactionDescriber.confusedCount=
ReactionDescriber.heartCount=
ReactionDescriber.rocketCount=
ReactionDescriber.eyesCount=
EntityQuery.invalidNumber=Invalid number: %s
EntityQuery.invalidDecimal=Invalid decimal: %s
EntityQuery.undefinedLabel=Undefined label: %s
EntityQuery.invalidWorkingPeriod=Invalid working period: %s
EntityQuery.unableFindUser=Unable to find user with login: %s
EntityQuery.unableFindProject=Unable to find project '%s'
EntityQuery.invalidBoolean=Invalid boolean: %s
EntityQuery.unrecognizedDate=Unrecognized date: %s
EntityQuery.unableFindRevision=Unable to find revision: %s
EntityQuery.unableFindCommit=Unable to find commit: %s
EntityQuery.unableFindIssue=Unable to find issue: %s
EntityQuery.unableFindIteration=Unable to find iteration: %s
EntityQuery.unableFindPr=Unable to find pull request: %s
EntityQuery.unableFindBuild=Unable to find build: %s
ProjectQueryDescriber.fuzzySugg=enclose with ~ to query name/path
ProjectQueryDescriber.ownedby=
ProjectQueryDescriber.ownedByMe=
ProjectQueryDescriber.ownedByNone=
ProjectQueryDescriber.forksOf=
ProjectQueryDescriber.roots=
ProjectQueryDescriber.leafs=
ProjectQueryDescriber.forkRoots=
ProjectQueryDescriber.withoutEnoughReplicas=
ProjectQueryDescriber.hasOutdatedReplicas=
ProjectQueryDescriber.missingStorage=
ProjectQueryDescriber.pendingDelete=
ProjectQueryDescriber.childrenOf=
ProjectQueryDescriber.field.id=
ProjectQueryDescriber.field.name=
ProjectQueryDescriber.field.path=
ProjectQueryDescriber.field.key=
ProjectQueryDescriber.field.label=
ProjectQueryDescriber.field.description=
ProjectQueryDescriber.field.serviceDeskEmailAddress=
ProjectQueryDescriber.field.lastCommitDate=
ProjectQueryDescriber.field.lastActivityDate=
IssueQueryDescriber.fuzzySugg=enclose with ~ to query title/description/comment
IssueQueryDescriber.findIssueByNumber=find issue by number
IssueQueryDescriber.submittedBy=
IssueQueryDescriber.watchedBy=
IssueQueryDescriber.ignoredBy=
IssueQueryDescriber.commentedBy=
IssueQueryDescriber.mentioned=
IssueQueryDescriber.fixedInCommit=
IssueQueryDescriber.fixedInCurrentCommit=
IssueQueryDescriber.fixedInPullRequest=
IssueQueryDescriber.fixedInCurrentPullRequest=
IssueQueryDescriber.fixedInBuild=
IssueQueryDescriber.fixedInCurrentBuild=
IssueQueryDescriber.isCurrent=
IssueQueryDescriber.isPrevious=
IssueQueryDescriber.fixedBetween=
IssueQueryDescriber.submittedByMe=
IssueQueryDescriber.commentedByMe=
IssueQueryDescriber.watchedByMe=
IssueQueryDescriber.ignoredByMe=
IssueQueryDescriber.mentionedMe=
IssueQueryDescriber.confidential=
IssueQueryDescriber.currentIssue=
IssueQueryDescriber.build=
IssueQueryDescriber.commit=
IssueQueryDescriber.tag=
IssueQueryDescriber.branch=
IssueQueryDescriber.project=
IssueQueryDescriber.number=
IssueQueryDescriber.state=
IssueQueryDescriber.title=
IssueQueryDescriber.description=
IssueQueryDescriber.estimatedTime=
IssueQueryDescriber.spentTime=
IssueQueryDescriber.progress=
IssueQueryDescriber.comment=
IssueQueryDescriber.submitDate=
IssueQueryDescriber.lastActivityDate=
IssueQueryDescriber.voteCount=
IssueQueryDescriber.commentCount=
IssueQueryDescriber.iteration=
IssueQueryDescriber.boardPosition=
IssueQueryDescriber.totalEstimatedTime=Total estimated time
IssueQueryDescriber.totalSpentTime=Total spent time
IssueQueryDescriber.totalSpentAndEstimatedTime=Total spent time / total estimated time
IssueQueryDescriber.spentAndEstimatedTime=Spent time / estimated time
IssueQueryDescriber.workingPeriod=specify working period, modify as necessary
IssueQueryDescriber.decimal=specify decimal number, modify as necessary
BuildQueryDescriber.findBuildByNumber=find build by number
BuildQueryDescriber.findBuildByThisNumber=find build with this number
BuildQueryDescriber.fuzzySugg=enclose with ~ to query job/version
BuildQueryDescriber.successful=
BuildQueryDescriber.failed=
BuildQueryDescriber.cancelled=
BuildQueryDescriber.timedOut=
BuildQueryDescriber.finished=
BuildQueryDescriber.running=
BuildQueryDescriber.pending=
BuildQueryDescriber.waiting=
BuildQueryDescriber.submittedByMe=
BuildQueryDescriber.submittedBy=
BuildQueryDescriber.cancelledByMe=
BuildQueryDescriber.cancelledBy=
BuildQueryDescriber.dependsOn=
BuildQueryDescriber.dependenciesOf=
BuildQueryDescriber.ranOn=
BuildQueryDescriber.fixedIssue=
BuildQueryDescriber.field.project=
BuildQueryDescriber.field.job=
BuildQueryDescriber.field.status=
BuildQueryDescriber.field.number=
BuildQueryDescriber.field.branch=
BuildQueryDescriber.field.tag=
BuildQueryDescriber.field.version=
BuildQueryDescriber.field.label=
BuildQueryDescriber.field.pullRequest=
BuildQueryDescriber.field.commit=
BuildQueryDescriber.field.submitDate=
BuildQueryDescriber.field.pendingDate=
BuildQueryDescriber.field.runningDate=
BuildQueryDescriber.field.finishDate=
BuildMetricQueryDescriber.buildIsSuccessful=
BuildMetricQueryDescriber.buildIsFailed=
BuildMetricQueryDescriber.report=
CodeCommentQueryDescriber.createdByMe=
CodeCommentQueryDescriber.repliedByMe=
CodeCommentQueryDescriber.createdBy=
CodeCommentQueryDescriber.repliedBy=
CodeCommentQueryDescriber.mentionedMe=
CodeCommentQueryDescriber.mentioned=
CodeCommentQueryDescriber.resolved=
CodeCommentQueryDescriber.unresolved=
CodeCommentQueryDescriber.onCommit=
CodeCommentQueryDescriber.field.content=
CodeCommentQueryDescriber.field.reply=
CodeCommentQueryDescriber.field.path=
CodeCommentQueryDescriber.field.status=
CodeCommentQueryDescriber.field.createDate=
CodeCommentQueryDescriber.field.lastActivityDate=
CodeCommentQueryDescriber.field.replyCount=
CodeCommentQueryDescriber.fuzzySugg=enclose with ~ to query path/content/reply
PackQueryDescriber.publishedByMe=
PackQueryDescriber.publishedByUser=
PackQueryDescriber.publishedByBuild=
PackQueryDescriber.publishedByProject=
PackQueryDescriber.field.project=
PackQueryDescriber.field.type=
PackQueryDescriber.field.name=
PackQueryDescriber.field.version=
PackQueryDescriber.field.label=
PackQueryDescriber.field.publishDate=
PackQueryDescriber.fuzzySugg=enclose with ~ for fuzzy query
AgentQueryDescriber.online=
AgentQueryDescriber.offline=
AgentQueryDescriber.paused=
AgentQueryDescriber.hasRunningBuilds=
AgentQueryDescriber.hasAttribute=
AgentQueryDescriber.notUsedSince=
AgentQueryDescriber.everUsedSince=
AgentQueryDescriber.selectedByExecutor=
AgentQueryDescriber.ranBuild=
AgentQueryDescriber.field.name=
AgentQueryDescriber.field.ipAddress=
AgentQueryDescriber.field.osName=
AgentQueryDescriber.field.osVersion=
AgentQueryDescriber.field.osArch=
AgentQueryDescriber.field.lastUsedDate=
AgentQueryDescriber.fuzzySugg=enclose with ~ to query name/ip/os
SymbolTooltipPanel.possibleDeclaration=Possible declarations
SymbolTooltipPanel.allOccurrences=All occurrences
JobMatchDescriber.onBranch=the target branch where the build commit was merged
JobMatchDescriber.submittedByUser=
JobMatchDescriber.submittedByGroup=
JobMatchDescriber.project=
JobMatchDescriber.job=
ActionConditionDescriber.onBranch=the target branch where the build commit was merged
ActionConditionDescriber.field.project=project of the running job
ActionConditionDescriber.field.branch=branch the job is running against
ActionConditionDescriber.field.tag=tag the job is running against
ActionConditionDescriber.field.pullRequest=the pull request the job is running against is empty or not
ActionConditionDescriber.field.log=log the job is running against contains
ActionConditionDescriber.always=
ActionConditionDescriber.successful=
ActionConditionDescriber.failed=
ActionConditionDescriber.cancelled=
ActionConditionDescriber.timedOut=
ActionConditionDescriber.prevIsSuccessful=
ActionConditionDescriber.prevIsFailed=
ActionConditionDescriber.prevIsNotFailed=
ActionConditionDescriber.prevIsCancelled=
ActionConditionDescriber.prevIsTimedOut=
ValueNeedsToBeEnclosedInBrackets=value needs to be enclosed in brackets
ReviewRequirementBehavior.numberOfReviewersFromTheGroup=number of required reviewers from the group
ReviewRequirementBehavior.hintSpecifyReviewer=Specify required reviews from the group
ReviewRequirementBehavior.hintIfNotSpecified=If not specified, one review is required from the group
ReviewRequirementBehavior.requireOne=require one reviewer from the group
ReviewRequirementBehavior.requireTwo=require two reviewers from the group
ReviewRequirementBehavior.requireThree=require three reviewers from the group
ReviewRequirementBehavior.user=
ReviewRequirementBehavior.group=
RetryConditionDescriber.javaRegularExpression=A <a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>Java regular expression</a> is expected here
RetryConditionDescriber.field.log=
UserMatchDescriber.user=
UserMatchDescriber.group=
UserMatchDescriber.role=
NotificationReceiverDescriber.committers=
NotificationReceiverDescriber.authors=
NotificationReceiverDescriber.committersSincePreviousSuccessful=
NotificationReceiverDescriber.authorsSincePreviousSuccessful=
NotificationReceiverDescriber.submitter=
JobVariableDescriber.projectName=
JobVariableDescriber.projectPath=
JobVariableDescriber.jobName=
JobVariableDescriber.jobToken=
JobVariableDescriber.ref=
JobVariableDescriber.branch=
JobVariableDescriber.tag=
JobVariableDescriber.commitHash=
JobVariableDescriber.buildNumber=
JobVariableDescriber.buildVersion=
JobVariableDescriber.pullRequestNumber=
JobVariableDescriber.issueNumber=
JobVariableDescriber.server=
JobVariableDescriber.serverHost=
JobVariableDescriber.serverUrl=
CommandPaletteDescriber.packages=
CommandPaletteDescriber.rootPackages=
CommandPaletteDescriber.builds=
CommandPaletteDescriber.rootBuilds=
CommandPaletteDescriber.issues=
CommandPaletteDescriber.rootIssues=
CommandPaletteDescriber.pulls=
CommandPaletteDescriber.rootPulls=
CommandPaletteDescriber.projects=
CommandPaletteDescriber.projectsNew=
CommandPaletteDescriber.boards=
CommandPaletteDescriber.branches=
CommandPaletteDescriber.children=
CommandPaletteDescriber.codeComments=
CommandPaletteDescriber.commits=
CommandPaletteDescriber.compare=
CommandPaletteDescriber.files=
CommandPaletteDescriber.issuesNew=
CommandPaletteDescriber.iterations=
CommandPaletteDescriber.iterationsNew=
CommandPaletteDescriber.pullsNew=
CommandPaletteDescriber.tags=
CommandPaletteDescriber.timesheets=
CommandPaletteDescriber.settings.avatarEdit=
CommandPaletteDescriber.settings.branchProtection=
CommandPaletteDescriber.settings.build.buildPreserveRules=
CommandPaletteDescriber.settings.build.cacheManagement=
CommandPaletteDescriber.settings.build.defaultFixedIssuesFilter=
CommandPaletteDescriber.settings.build.jobProperties=
CommandPaletteDescriber.settings.build.jobSecrets=
CommandPaletteDescriber.settings.codeAnalysis=
CommandPaletteDescriber.settings.discordNotifications=
CommandPaletteDescriber.settings.general=
CommandPaletteDescriber.settings.gitPackConfig=
CommandPaletteDescriber.settings.groupAuthorizations=
CommandPaletteDescriber.settings.ntfyshNotifications=
CommandPaletteDescriber.settings.pullRequest=
CommandPaletteDescriber.settings.slackNotifications=
CommandPaletteDescriber.settings.tagProtection=
CommandPaletteDescriber.settings.userAuthorizations=
CommandPaletteDescriber.settings.webHooks=
CommandPaletteDescriber.settings.servicedesk=
CommandPaletteDescriber.settings.slackNotificationSetting=slack notification setting
CommandPaletteDescriber.settings.discordNotificationSetting=discord notification setting
CommandPaletteDescriber.settings.ntfyNotificationSetting=ntfy.sh notification setting
CommandPaletteDescriber.settings.dingtalkNotificationSetting=dingtalk notification setting
CommandPaletteDescriber.settings.wecomNotificationSetting=wecom notification setting
CommandPaletteDescriber.stats.build.duration=
CommandPaletteDescriber.stats.build.frequency=
CommandPaletteDescriber.stats.code.contribs=
CommandPaletteDescriber.stats.code.lines=
CommandPaletteDescriber.stats.coverage=
CommandPaletteDescriber.stats.issue.duration=
CommandPaletteDescriber.stats.issue.frequency=
CommandPaletteDescriber.stats.pullRequest.duration=
CommandPaletteDescriber.stats.pullRequest.frequency=
CommandPaletteDescriber.stats.unitTest=
CommandPaletteDescriber.stats.problem=
CommandPaletteDescriber.admin.agents=
CommandPaletteDescriber.admin.groups=
CommandPaletteDescriber.admin.groupsNew=
CommandPaletteDescriber.admin.invitations=
CommandPaletteDescriber.admin.invitationsNew=
CommandPaletteDescriber.admin.labels=
CommandPaletteDescriber.admin.roles=
CommandPaletteDescriber.admin.rolesNew=
CommandPaletteDescriber.admin.serverInformation=
CommandPaletteDescriber.admin.serverLog=
CommandPaletteDescriber.admin.settings.alert=
CommandPaletteDescriber.admin.settings.authenticator=
CommandPaletteDescriber.admin.settings.backup=
CommandPaletteDescriber.admin.settings.branding=
CommandPaletteDescriber.admin.settings.checkIssueIntegrity=
CommandPaletteDescriber.admin.settings.commitMessageFixPatterns=
CommandPaletteDescriber.admin.settings.emailTemplates.alert=
CommandPaletteDescriber.admin.settings.emailTemplates.buildNotification=
CommandPaletteDescriber.admin.settings.emailTemplates.commitNotification=
CommandPaletteDescriber.admin.settings.emailTemplates.emailVerification=
CommandPaletteDescriber.admin.settings.emailTemplates.issueNotification=
CommandPaletteDescriber.admin.settings.emailTemplates.issueNotificationUnsubscribed=
CommandPaletteDescriber.admin.settings.emailTemplates.packNotification=
CommandPaletteDescriber.admin.settings.emailTemplates.passwordReset=
CommandPaletteDescriber.admin.settings.emailTemplates.pullRequestNotification=
CommandPaletteDescriber.admin.settings.emailTemplates.pullRequestNotificationUnsubscribed=
CommandPaletteDescriber.admin.settings.emailTemplates.serviceDeskIssueOpenFailed=
CommandPaletteDescriber.admin.settings.emailTemplates.serviceDeskIssueOpened=
CommandPaletteDescriber.admin.settings.emailTemplates.stopwatchOverdue=
CommandPaletteDescriber.admin.settings.emailTemplates.userInvitation=
CommandPaletteDescriber.admin.settings.gpgSigningKey=
CommandPaletteDescriber.admin.settings.gpgTrustedKeys=
CommandPaletteDescriber.admin.settings.groovyScripts=
CommandPaletteDescriber.admin.settings.issueBoards=
CommandPaletteDescriber.admin.settings.issueFields=
CommandPaletteDescriber.admin.settings.issueLinks=
CommandPaletteDescriber.admin.settings.issueStates=
CommandPaletteDescriber.admin.settings.issueTemplates=
CommandPaletteDescriber.admin.settings.jobExecutors=
CommandPaletteDescriber.admin.settings.mailService=
CommandPaletteDescriber.admin.settings.performance=
CommandPaletteDescriber.admin.settings.security=
CommandPaletteDescriber.admin.settings.serviceDeskSetting=
CommandPaletteDescriber.admin.settings.sshServerKey=
CommandPaletteDescriber.admin.settings.ssoConnectors=
CommandPaletteDescriber.admin.settings.stateTransitions=
CommandPaletteDescriber.admin.settings.system=
CommandPaletteDescriber.admin.settings.storagesetting=
CommandPaletteDescriber.admin.settings.timetracking=
CommandPaletteDescriber.admin.subscriptionManagement=
CommandPaletteDescriber.admin.users=
CommandPaletteDescriber.admin.cluster=
CommandPaletteDescriber.admin.usersNew=
CommandPaletteDescriber.administrator=
CommandPaletteDescriber.my=
CommandPaletteDescriber.my.accessTokens=
CommandPaletteDescriber.my.avatar=
CommandPaletteDescriber.my.emailAddresses=
CommandPaletteDescriber.my.gpgKeys=
CommandPaletteDescriber.my.password=
CommandPaletteDescriber.my.preferences=
CommandPaletteDescriber.my.sshKeys=
CommandPaletteDescriber.my.twoFactorAuthentication=
CommandPaletteDescriber.codesearch.files=
CommandPaletteDescriber.codesearch.text=
CommandPaletteDescriber.codesearch.symbols=
CommandPaletteParamDescriber.commitParam=
CommandPaletteParamDescriber.revisionAndPathParam.searchInDefaultBranch=
CommandPaletteParamDescriber.revisionAndPathParam.tooGeneral=
OrderByDescriber.status=
OrderByDescriber.createDate=
OrderByDescriber.lastActivityDate=
OrderByDescriber.replyCount=
OrderByDescriber.path=
OrderByDescriber.id=
OrderByDescriber.name=
OrderByDescriber.key=
OrderByDescriber.serviceDeskEmailAddress=
OrderByDescriber.lastCommitDate=
OrderByDescriber.ipAddress=
OrderByDescriber.os=
OrderByDescriber.osVersion=
OrderByDescriber.osArch=
OrderByDescriber.lastUsedDate=
OrderByDescriber.job=
OrderByDescriber.number=
OrderByDescriber.submitDate=
OrderByDescriber.pendingDate=
OrderByDescriber.runningDate=
OrderByDescriber.finishDate=
OrderByDescriber.project=
OrderByDescriber.commit=
OrderByDescriber.closeDate=
OrderByDescriber.targetProject=
OrderByDescriber.sourceProject=
OrderByDescriber.targetBranch=
OrderByDescriber.sourceBranch=
OrderByDescriber.commentCount=
OrderByDescriber.voteCount=
OrderByDescriber.state=
OrderByDescriber.estimatedTime=
OrderByDescriber.spentTime=
OrderByDescriber.progress=
OrderByDescriber.boardPosition=
OrderByDescriber.type=
OrderByDescriber.priority=
OrderByDescriber.version=
OrderByDescriber.publishDate=
##### END 注意 这些都是提示器说明国际化，提示器有值和说明两部分，英文版的无需额外说明，所以国际化值都是空的，这是正常的。######

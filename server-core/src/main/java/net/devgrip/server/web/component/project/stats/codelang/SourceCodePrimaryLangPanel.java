package net.devgrip.server.web.component.project.stats.codelang;

import net.devgrip.server.AppServer;
import net.devgrip.server.model.Project;
import net.devgrip.server.xodus.CommitInfoManager;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.panel.GenericPanel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.HashMap;
import java.util.Map;

public class SourceCodePrimaryLangPanel extends GenericPanel<Project> {

	private final boolean hideParentContainerIfNonePrimaryLangFound;

	public SourceCodePrimaryLangPanel(String id, IModel<Project> projectModel, boolean hideParentContainerIfNonePrimaryLangFound) {
		super(id, projectModel);
		this.hideParentContainerIfNonePrimaryLangFound = hideParentContainerIfNonePrimaryLangFound;
		setOutputMarkupId(true);
	}

	private final IModel<String> primaryLangModel = new LoadableDetachableModel<>(){
		@Override
		protected String load() {
			Map<Integer, Map<String, Integer>> lineIncrements = getCommitInfoManager().getLineIncrements(getProject().getId());


			Map<String, Integer> totalByLanguage = new HashMap<>();
			String topLanguage = "";
			if (lineIncrements.isEmpty()) {
				return topLanguage;
			}
			int maxLines = 0;

			for (Map<String, Integer> dailyMap : lineIncrements.values()) {
				for (Map.Entry<String, Integer> entry : dailyMap.entrySet()) {
					String lang = entry.getKey();
					int lines = entry.getValue();

					int newTotal = totalByLanguage.getOrDefault(lang, 0) + lines;
					totalByLanguage.put(lang, newTotal);

					if (newTotal > maxLines) {
						maxLines = newTotal;
						topLanguage = lang;
					}
				}
			}
			return topLanguage;
		}
	};

	private CommitInfoManager getCommitInfoManager() {
		return AppServer.getInstance(CommitInfoManager.class);
	}
	
	private Project getProject() {
		return getModelObject();
	}


	@Override
	protected void onInitialize() {
		super.onInitialize();
		add(new Label("sourceLang", primaryLangModel).setVisible(!primaryLangModel.getObject().isEmpty()));
	}

	@Override
	protected void onConfigure() {
		super.onConfigure();
		// Hide the entire panel if primary language is empty
		boolean primaryLangFound = !primaryLangModel.getObject().isEmpty();
		setVisible(primaryLangFound);
		//hide parent container if no primary language found
		if (hideParentContainerIfNonePrimaryLangFound) {
			if (getParent() != null) {
				getParent().setVisible(primaryLangFound);
			}
		}
	}

	@Override
	protected void onDetach() {
		super.onDetach();
		primaryLangModel.detach();
	}
}

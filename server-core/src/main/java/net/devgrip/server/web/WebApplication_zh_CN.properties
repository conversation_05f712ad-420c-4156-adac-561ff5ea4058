#### Begin, 这些绝对不能删,因为它们都是代码中动态拼接出key的
RevisionDiffPanel.Unified=统一视图
RevisionDiffPanel.Split=分栏视图
IGNORE_TRAILING=忽略行尾空白字符
IGNORE_LEADING=忽略行首空白字符
IGNORE_CHANGE=忽略行首和行尾的空白字符
IGNORE_ALL=忽略所有空白字符
DO_NOT_IGNORE=不要忽略空白字符
Build.Status.waiting=等待中
Build.Status.pending=排队中
Build.Status.running=运行中
Build.Status.failed=已失败
Build.Status.cancelled=已取消
Build.Status.timed_out=已超时
Build.Status.successful=已成功
Build.Status.upper.waiting=等待中
Build.Status.upper.pending=排队中
Build.Status.upper.running=运行中
Build.Status.upper.failed=已失败
Build.Status.upper.cancelled=已取消
Build.Status.upper.timed_out=已超时
Build.Status.upper.successful=已成功
UploadStrategy.upload_if_not_hit=缓存未命中就上传
UploadStrategy.upload_if_changed=文件被更改就上传
ExecuteCondition.successful=均已成功
ExecuteCondition.always=总是执行
ExecuteCondition.never=绝不执行
PullRequest.Status.open=已开启
PullRequest.Status.merged=已合并
PullRequest.Status.discarded=已丢弃
PullRequest.Status.upper.open=已开启
PullRequest.Status.upper.merged=已合并
PullRequest.Status.upper.discarded=已丢弃
CodePrivilege.write=写
CodePrivilege.read=读
CodePrivilege.none=无
PackPrivilege.write=写
PackPrivilege.read=读
PackPrivilege.none=无
DateRangeType.month=月
DateRangeType.week=周
RowType.users=用户
RowType.issues=问题
StatsGroup.by_day=按天
StatsGroup.by_week=按周
StatsGroup.by_month=按月
FixType.change_to_another_value=改为其他值
FixType.delete_this_value=删除该值
FixType.change_to_another_state=改为其他状态
FixType.delete_this_state=删除该状态
FixType.change_to_another_field=改为其他字段
FixType.delete_this_field=删除该字段
EventType.code_push=代码推送
EventType.pull_request=拉取请求
EventType.issue=问题
EventType.code_comment=代码评论
EventType.build=构建
EventType.package=包与镜像库
UnitTestReport.Status.not_passed=未通过
UnitTestReport.Status.other=其他
UnitTestReport.Status.not_run=未运行
UnitTestReport.Status.passed=通过
Severity.critical=严重
Severity.high=高
Severity.medium=中
Severity.low=低
#### End, 这些绝对不能删,因为它们都是代码中动态拼接出key的
Create\ Administrator\ Account=请创建管理员账号
Specify\ System\ Setting=请指定系统设置
Select\ Language=请选择语言
Specify\ Service\ Desk\ Setting=请指定服务台设置
Specify\ Backup\ Setting=请指定备份设置
Server\ is\ Starting=服务正在启动...
Server\ Setup=服务配置
Please\ Wait=请稍等
IssueLink.relatesTo=相关问题
IssueLink.duplicates=重复问题
IssueLink.childIssue=子问题
IssueLink.parentIssue=父问题
IssueNamedQuery.open=已开启
IssueNamedQuery.assignedToMeAndOpen=由我负责且已开启
IssueNamedQuery.submittedByMeAndOpen=由我提交且已开启
IssueNamedQuery.assignedToMe=由我负责
IssueNamedQuery.submittedByMe=由我提交
IssueNamedQuery.submittedRecently=最近提交
IssueNamedQuery.mentionedMe=提到我
IssueNamedQuery.hasActivityRecently=最近有新的进展
IssueNamedQuery.openAndCritical=已开启且严重
IssueNamedQuery.openAndUnassigned=已开启且未指定负责人
IssueNamedQuery.openAndUnscheduled=已开启且未安排迭代
IssueNamedQuery.closed=已关闭
IssueNamedQuery.all=全部
DefaultIssueFieldName.type=类型
DefaultIssueFieldName.assignee=负责人
DefaultIssueFieldName.priority=优先级
DefaultIssueFieldValue.type.newFeature=新功能
DefaultIssueFieldValue.type.bug=Bug
DefaultIssueFieldValue.type.task=任务
DefaultIssueFieldValue.type.improvement=改进
DefaultIssueFieldValue.priority.minor=轻微
DefaultIssueFieldValue.priority.normal=普通
DefaultIssueFieldValue.priority.major=重要
DefaultIssueFieldValue.priority.critical=严重
DefaultIssueFieldValue.assignee.empty=未指定
DefaultIssueFieldValue.state.open=已开启
DefaultIssueFieldValue.state.closed=已关闭
DefaultIssueFieldValue.state.inProgress=进行中
DefaultIssueBoard.name=默认看板
PullRequestNamedQuery.open=已开启
PullRequestNamedQuery.needMyAction=待我操作
PullRequestNamedQuery.toBeReviewedByMe=待我评审
PullRequestNamedQuery.toBeChangedByMe=待我更改
PullRequestNamedQuery.toBeMergedByMe=待我合并
PullRequestNamedQuery.requestedForChangesByMe=我发起的请求更改
PullRequestNamedQuery.assignedToMe=由我负责
PullRequestNamedQuery.approvedByMe=由我批准
PullRequestNamedQuery.submittedByMe=由我提交
PullRequestNamedQuery.submittedRecently=最近提交
PullRequestNamedQuery.updatedRecently=最近更新
PullRequestNamedQuery.merged=已合并
PullRequestNamedQuery.discarded=已丢弃
PullRequestNamedQuery.all=全部
ProjectNamedQuery.my=我的项目
ProjectNamedQuery.all=全部
PackNamedQuery.all=全部
AgentNamedQuery.all=全部
AgentNamedQuery.online=在线
AgentNamedQuery.offline=离线
AgentNamedQuery.linux=Linux 代理
AgentNamedQuery.windows=Windows 代理
AgentNamedQuery.macos=macOS 代理
AgentNamedQuery.freebsd=FreeBSD 代理
AgentNamedQuery.paused=已暂停
AgentNamedQuery.notUsedFor1Month=1个月未使用
BuildNamedQuery.all=全部
BuildNamedQuery.successful=已成功
BuildNamedQuery.failed=已失败
BuildNamedQuery.cancelled=已取消
BuildNamedQuery.timedout=已超时
BuildNamedQuery.running=正在运行
BuildNamedQuery.waiting=正在等待
BuildNamedQuery.pending=正在排队
BuildNamedQuery.buildRecently=最近构建
CommitNamedQuery.all=全部
CommitNamedQuery.defaultBranch=默认分支
CommitNamedQuery.committedByMe=由我提交
CommitNamedQuery.committedRecently=最近提交
CodeCommentNamedQuery.all=全部
CodeCommentNamedQuery.unresolved=未解决
CodeCommentNamedQuery.resolved=已解决
CodeCommentNamedQuery.createdByMe=我创建的
CodeCommentNamedQuery.mentionedMe=提到我
CodeCommentNamedQuery.createdRecently=最近创建
CodeCommentNamedQuery.updatedRecently=最近更新
Please_Wait=请稍候...
WebsocketError=页面发生错误，请重新加载
Step_Indicator=第${index}步 / 共${len}步: ${title}
Login_Name=登录名
Password=密码
Login_Name_Or_Email=用户名或邮箱
Verify=验证
Verify_by_Recovery_Code=通过恢复代码验证
TOTP_Not_Access=无法访问您的身份验证器？
Full_Name=全名
Previous=上一步
Name=名称
Type=类型
Value=值
Next=下一步
Finish=完成
Reset=重置
Ok=确认
Cancel=取消
Submit=提交
Error=错误
Password_Reset_Url_Hint=密码重置链接无效或已过期
Password_Holder=在此输入密码
Confirm_Password_Holder=请再次输入密码
SystemSetting.validateError.1=URL错误，URL未指定协议部分
SystemSetting.validateError.2=URL错误，URL协议应该为http或者https
SystemSetting.validateError.3=URL错误，URL未指定Host部分
SystemSetting.validateError.4=URL错误，URL不应该指定Path部分
SystemSetting.validateError.5=URL错误，应该以 http:// 或 https:// 开头
SystemSetting.validateError.6=URL错误
SystemSetting.validateError.7=URL错误，URL应该以 ssh:// 开头
SystemSetting.asl.name=请输入第三方的用户头像服务的URL
SystemSetting.asl.desc=用户头像将通过在此URL后附加哈希值来请求
SystemSetting.uas.name=使用第三方的用户头像服务
SystemSetting.uas.desc=是否使用第三方的用户头像服务
DisableDashboard=禁用仪表盘
Server_Url=服务器网址
Server_Url_Desc=指定访问此服务器的根网址，如https://www.example.com
SSH_Root_URL=SSH根地址
SSH_Root_Desc=选填，指定一个ssh根地址，用于通过SSH协议来克隆项目。留空则表示从服务器网址提取。
disableAutoUpdateCheck=禁止自动检查更新
Auto_Update_Check_Desc=自动更新检查是通过在浏览器中向devgrip.net请求一张图片来执行的，该图片会显示新版本的可用性，并用颜色表示更新的类型。红色是安全更新，黄色是bug修复，蓝色是新功能更新。如果禁用，强烈建议定期手动检查更新，以查看是否有任何安全/关键修复。
Git_Location=Git命令行
Git_Location_Desc=DevGrip需要git命令行来管理代码库。最低要求版本为2.11.1。如果您想在构建任务中检索LFS文件，请确保已安装git-lfs
Curl_Location=curl命令行
Curl_Location_Desc=DevGrip配置的git钩子要使用curl与自身通信
Use_Specified_Curl=使用指定路径下的curl
Use_Specified_Git=使用指定路径下的git
Use_System_Path_Curl=使用系统路径下的curl
Use_System_Path_Git=使用系统路径下的git
Specified_Git_Path_Name=请输入git路径
Specified_Curl_Path_Name=请输入curl路径
Specified_Git_Path_Desc=指定一个git路径，比如: <tt>/usr/bin/git</tt>
Specified_Curl_Path_Desc=指定一个curl路径，比如: <tt>/usr/bin/curl</tt>
Language=语言
Language_Desc=请选择语言
System_Setting=系统设置
Settings_Saved=保存成功
security=安全
security.enableAnonymousAccess=启用匿名访问
security.enableAnonymousAccess.description=是否允许匿名用户访问此服务器
security.enableSelfRegister.name=启用用户自主注册
security.enableSelfRegister.description=启用此选项后用户可以自行注册
security.allowedSelfRegisterEmailDomain.name=允许或禁止哪些邮件域名进行自主注册
security.allowedSelfRegisterEmailDomain.placeholder=任何域名
security.allowedSelfRegisterEmailDomain.description=请输入允许或禁止哪些邮件域名进行自主注册，使用'*'或'?'进行通配符匹配，使用'-'开头的域名，会被判定为禁止注册的域名。多个域名中间使用空格隔开。
security.defaultGroup.name=默认用户组
security.defaultGroup.description=选填，将新用户添加到指定的默认分组中
security.enableSelfDeregister.name=启用账户自主注销
security.enableSelfDeregister.description=是否允许用户自行注销账户
security.enforce2FA.name=强制双因素认证
security.enforce2FA.description=选中此项为系统中的所有用户强制启用双因素认证
security.enforcePwdRule.name=强制使用更灵活的密码规则
security.enforcePwdRule.desc=对新用户强制使用更灵活的密码规则
security.usage.prefix=安全设置
security.usage.desc=用于登录用户的默认组
security.corsAllowedOrigins.name=允许跨域请求的域名
security.corsAllowedOrigins.placeholder=输入允许跨域请求的域名，按回车键添加
security.corsAllowedOrigins.description=选填，指定允许的<a href='https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS' target='_blank'>CORS</a> 源。对于简单的跨域请求或预检请求，如果请求头 <code>Origin</code> 的值包含在此处，响应头 <code>Access-Control-Allow-Origin</code> 将被设置为相同的值
um.filterByNameOrEmail=请输入登录名、全名或者电子邮件进行筛选
um.impersonateThisUser=作为该用户登录
um.enableSelectedUsers=启用选定的用户
um.enableSelectedUsers.success=用户已成功启用
um.enableSelectedUsers.noUserSelected=请选择要启用的用户
um.disableSelectedUsers=禁用选定的用户
um.disableSelectedUsers.success=用户已成功禁用
um.disableSelectedUsers.confirm=禁用账户将会清除密码、清除访问令牌，并从其他实体中移除所有引用（除过去的活动记录外）。请输入 <code>yes</code> 以确认
um.disableSelectedUsers.canNotDisableRoot=无法禁用 root 账户
um.disableSelectedUsers.canNotDisableYourself=无法禁用自己的账户
um.disableSelectedUsers.noUserSelected=请选择要禁用的用户
um.enableAllQueriedUsers=启用所有查询到的用户
um.enableAllQueriedUsers.success=用户已成功启用
um.enableAllQueriedUsers.noUser=没有可启用的用户
um.disableAllQueriedUsers=禁用所有查询到的用户
um.disableAllQueriedUsers.noUser=没有可禁用的用户
um.botAccount.label=机器人
um.disabledAccount.label=已禁用
um.notApplicableWithI=<i>无意义</i>
um.includeDisabledUsers=包含已禁用的用户
um.foundUsersCount=查到${userCount}个用户
um.loginName=登录名
um.fullName=全名
um.primaryEmail=主电子邮件
um.authSource=认证方式
um.internalDatabase=系统内建
um.externalSystem=外部认证
um.emailUnverified=未验证
um.continueCreate=继续创建下一个用户
um.userCreated=创建成功
um.emailAlreadyUsed=电子邮件已被其他用户使用
um.nameAlreadyUsed=登录名已经被其他用户使用
um.createUserTitle=创建用户
um.email.name=电子邮件
um.notifyOwnEvents.name=是否发送自身事件的通知
um.notifyOwnEvents.description=要不要为自己生成的事件发送通知
um.botAccount.name=机器人账号
um.botAccount.desc1=是否创建为机器人账号，机器人账号主要用于任务自动化。机器人账号没有密码和邮箱地址，也不会为其动态产生相关的通知。<b class='text-warning'>注意:</b> 机器人账号是企业版功能。点此<a href='https://devgrip.net/pricing' target='_blank'>免费试用</a>30天
um.botAccount.desc2=是否创建为机器人账号，机器人账号主要用于任务自动化。机器人账号没有密码和邮箱地址，也不会为其动态产生相关的通知。
userList.title=用户列表
userList.deleteSelectedUsers=删除选定用户
userList.cannotDeleteRoot=无法删除root账户
userList.cannotDeleteYourself=无法删除自己
userList.deleteUsersSuccess=用户删除成功
userList.deleteUsersConfirm=输入 <code>yes</code> 确认删除选定的用户
userList.deleteAllQueriedUsersConfirm=输入 <code>yes</code> 确认删除所有查询到的用户
userList.selectUsersToDelete=请选择要删除的用户
userList.noUserFound=找不到任何用户，无法操作
userList.deleteAllQueriedUsers=删除所有查询到的用户
UserAccessTokenPage.desc=访问令牌(Access Token)可用于api的调用、git的版本库拉取/推送，但它不能用于登录网页界面。
UserProfilePage.authWithInternalDatabase.desc=该用户正在使用内建的身份认证。
UserProfilePage.authWithInternalDatabase.removeLink=删除密码
UserProfilePage.authWithInternalDatabase.useExternalSystem=可以强制该用户使用外部系统进行身份认证。
UserProfilePage.authWithExternalSystem.desc=该用户正在使用外部系统的身份认证。为了使用系统内建的身份认证，您可以
UserProfilePage.authWithExternalSystem.setLink=为该用户设置一个密码
UserProfilePage.authWithExternalSystem.tellUserResetLink=告诉该用户去重置密码
UserProfilePage.removeSuccess=密码删除成功
UserProfilePage.removePassword.confirm=确定要删除该用户的密码吗？
UserProfilePage.tellUserResetPassword.mailSubject=[重置密码] 请重置您的密码
UserProfilePage.tellUserResetPassword.success=密码重置请求已经发送
UserProfilePage.tellUserResetPassword.noVerifiedPrimaryEmail=无法通知用户，因为该用户没有一个验证通过的主电子邮件。
UserProfilePage.tellUserResetPassword.unableNotify=无法通知用户，因为邮件服务还没有配置。
UserProfilePage.botAccountDesc=该账号是一个机器人账号，主要用于任务自动化
UserProfilePage.thisAccountIsDisabled=该账号目前已被禁用
UserProfilePage.disabledBotAccount=该机器人账号目前已被禁用
UserPasswordPage.desc=该用户正在使用外部系统进行身份认证，设置一个新密码将会切换为内建的身份认证。
UserPage.Profile=个人信息
UserPage.EmailAddress=电子邮件
UserPage.EditAvatar=头像
UserPage.Password=密码
UserPage.BelongingGroups=所在的用户组
UserPage.AuthorizedProjects=项目授权
UserPage.SSHKeys=SSH密钥
UserPage.GPGKeys=GPG密钥
UserPage.AccessTokens=访问令牌
UserPage.TwofactorAuthentication=双因素认证
UserPage.queryWatches=关注的查询
UserMembershipsPage.filterGroups.placeholder=按名称筛选
UserMembershipsPage.deleteLink=退出用户组
UserMembershipsPage.addToGroup.placeholder=把用户添加进哪个组
UserMembershipsPage.addToGroup.success=成功添加到组里
UserMembershipsPage.deleteSelected.confirm=请输入<code>yes</code>来退出选定的用户组
UserMembershipsPage.deleteSelected.title=请选择要退出的用户组
UserMembershipsPage.deleteAllQueried.confirm=请输入<code>yes</code>来退出所有查询到的用户组
UserMembershipsPage.noQueriedFound=没有找到任何用户组
UserMembershipsPage.groupsColumn.name=名称
UserMembershipsPage.groupsColumn.desc=描述
UserAuthorizationsPage.desc=授权项目时，所有子项目也将被授权该角色
UserAuthorizationsPage.save.success=项目授权更新成功
UserAuthorizationsPage.save.fail=重复的项目:${project}
UserGpgKeysPage.desc=在此处添加GPG密钥，以验证由该用户签名的提交(commit)/标记(tag)
UserGpgKeysPage.howToUse=访问<a href="https://docs.github.com/en/authentication/managing-commit-signature-verification/about-commit-signature-verification#gpg-commit-signature-verification" target="_blank">GitHub文档</a>学习如何生成和使用GPG密钥对你的提交进行签名
UserGpgKeysPage.notBelongTo=<b>请记住</b>带有<span class="badge badge-warning badge-sm">无效</span>标记的邮件地址要么不属于密钥所有者要么没有通过密钥所有者的验证。
UserGpgKeysPage.plusButtonTitle=创建GPG密钥
RoleListPage.deleteConfirm=确定要删除角色${name}吗?
RoleListPage.deleteSuccess=角色${name}删除成功
RoleListPage.cannotDelete=无法删除系统内置的角色
RoleDetailPage.projectOwner.desc=是个系统内置角色，无法删除，它有项目的所有权限。
RoleDetailPage.addFail=该名称已被使用。
RoleDetailPage.submitSuccess=更新成功
RoleDetailPage.deleteSuccess=角色${name}删除成功
RoleDetailPage.deleteConfirm=确定要删除角色${name}吗?
GroupListPage.columnName=名称
GroupListPage.columnIsAdmin=是否管理员
GroupListPage.columnCanCreateRootProjects=能否创建根项目
GroupListPage.deleteConfirm=确定要删除用户组[${name}]吗?
GroupListPage.deleteSuccess=删除用户组[${name}]成功
NewGroupPage.createGroup=创建用户组
NewGroupPage.createSuccess=用户组创建成功
NewGroupPage.createFail=该名称已经被使用
GroupPage.Profile=基本信息
GroupPage.Members=组员
GroupPage.Authorized=项目授权
GroupProfilePage.saveFail=该名称已被使用
GroupProfilePage.saveSuccess=更新成功
GroupProfilePage.deleteConfirm=确定要删除吗？
GroupProfilePage.deleteSuccess=删除成功
GroupMembershipsPage.addMember.placeholder=添加组员...
GroupMembershipsPage.addMember.success=组员添加成功
GroupMembershipsPage.deleteSelected.confirm=请输入<code>yes</code>移除选定的组员
GroupMembershipsPage.deleteSelected.title=请先选择组员
GroupMembershipsPage.deleteAllQueried.confirm=请输入<code>yes</code>移除所有查询到的组员
GroupMembershipsPage.deleteAllQueried.title=没有组员
GroupMembershipsPage.deleteMembers=移除
GroupMembershipsPage.name=名称
GroupMembershipsPage.primaryEmail=电子邮件
GroupMembershipsPage.notSpecified=<i>未指定</i>
GroupAuthorizationsPage.desc=授权项目时，所有子项目也将被授权该角色
GroupAuthorizationsPage.addFail=重复的项目: ${name}
GroupAuthorizationsPage.addSuccess=项目授权更新成功
Group.description.desc=选填，请简述一下该组的作用
Group.isAdmin.name=是否管理员
Group.canCreateRootProjects.name=能否创建根项目
Group.canCreateRootProjects.desc=项目之间可以有父子层级结构，而根项目指父子结构中层级最高的项目，根项目没有父项目。
Group.enable2fa.name=强制双因素认证
Group.enable2fa.desc=选中后将为该组内的所有用户强制启用双因素认证
BrandSettingEditBean.url.name=链接
BrandSettingEditBean.url.desc=您的品牌链接
BrandSettingEditBean.light.name=浅色模式的Logo
BrandSettingEditBean.light.desc=上传一个128x128透明的png文件作为浅色模式的logo
BrandSettingEditBean.dark.name=深色模式的Logo
BrandSettingEditBean.dark.desc=上传一个128x128透明的png文件作为深色模式的logo
BrandingSettingPage.updateSuccess=品牌更新成功
BrandingSettingPage.useDefault=恢复默认品牌成功
StorageSetting.lfs.name=Git LFS存储
StorageSetting.lfs.desc=选填，指定单独的目录来存储 git lfs文件，如果填的不是绝对路径，则一律视为相对路径，且相对于站点目录。
StorageSetting.artifact.name=构建产物存储
StorageSetting.artifact.desc=选填，指定单独的目录来存储构建产物，如果填的不是绝对路径，则一律视为相对路径，且相对于站点目录。
StorageSetting.pack.name=包与镜像存储
StorageSetting.pack.desc=选填，指定单独的目录来存储包和镜像文件，如果填的不是绝对路径，则一律视为相对路径，且相对于站点目录。
ClusterManagementPage.saveAndRedistributeProjects=保存设置并重新分配项目
ClusterManagementPage.replicas=项目副本
ClusterManagementPage.servers=服务器集群
ClusterManagementPage.desc=添加/删除群集成员时，必须重新分配项目。系统不会自动执行此操作，因为这会耗费大量资源，建议您可以在集群负载平稳时再执行此操作。
ClusterManagementPage.saveSuccess=设置成功，项目将被重新分配
ClusterManagementPage.leadBadage= <span class='badge badge-info badge-sm ml-1'>主</span>
ClusterSetting.replicaCount.name=副本数量
ClusterSetting.replicaCount.desc=项目副本的总数量(主+备)。
ClusterSetting.tooManyReplicasCount=副本数量不能超过集群成员的数量
PerformanceSettingPage.saveSuccess=保存成功
PerformanceSetting.cpuTask.name=CPU密集型任务并发
PerformanceSetting.cpuTask.desc=指定服务器可并发运行的CPU密集型任务（如Git仓库拉/推、仓库索引等）的最大数量。
PerformanceSetting.lfsSize.name=最大的Git LFS文件大小(MB)
PerformanceSetting.lfsSize.desc=指定 Git LFS文件的最大大小，单位MB
PerformanceSetting.uploadSize.name=最大的上传文件大小(MB)
PerformanceSetting.uploadSize.desc=指定Web界面上最大的上传文件大小，同样也包括上传到版本库的文件、markdown内容（例如:问题评论里的附件等）和构建产物。单位MB
PerformanceSetting.codeSearchItems.name=最大的代码搜索条目
PerformanceSetting.codeSearchItems.desc=搜索代码时最多返回多少个条目
LabelManagementPage.desc=定义的分类标签可以分配给项目、拉取请求、构建。而如果在问题模块，你更应该使用自定义字段，它比分类标签更强大。
LabelManagementPage.updateSuccess=分类标签更新成功
LabelManagementBean.nameDup=重复的名称: 
LabelSpec.name=名称
LabelSpec.colorName=颜色
AlertSettingPage.updateSuccess=告警设置更新成功
AlertSetting.notifyUsers.name=告警通知的用户
AlertSetting.notifyUsers.desc=请选择用户，这样就能在数据库自动备份失败、群集节点无法访问等事件发生时发送告警邮件
SubscriptionManagementPage.desc=您还没有订阅，系统功能已经受限，要启用所有功能，您需要先<a href="https://devgrip.net/pricing">购买订阅</a>。
SubscriptionManagementPage.order=购买订阅密钥
SubscriptionManagementPage.installKey=安装订阅密钥
SubscriptionManagementPage.extend=延长试用订阅
SubscriptionManagementPage.licensedTo=授权给: 
SubscriptionManagementPage.licensedGroup=用户组授权: 
SubscriptionManagementPage.licensedGroup.desc=<b>注意:</b>使用本许可，即表示您同意将所有工作人员用户添加到本组，进行剩余时长(即剩余用户月的数量)检查
SubscriptionManagementPage.expiration=到期日: 
SubscriptionManagementPage.notFound=没找到
SubscriptionManagementPage.requestChange=更改
SubscriptionManagementPage.remain=剩余用户月数: 
SubscriptionManagementPage.orderMore=续费
SubscriptionManagementPage.orderMoreUserMonths=购买更多用户月
SubscriptionManagementPage.requestTrialSubscription=请求试用
SubscriptionManagementPage.trial.expireTitle1=此订阅为试用订阅，所有功能都可正常使用。
SubscriptionManagementPage.trial.expireTitle2=此订阅为试用订阅，但是订阅已经过期，系统功能已经受限。
SubscriptionManagementPage.trial.expireTitle3=** 您的试用订阅已经过期，功能已经受限，请购买订阅Key来启用。如果您需要延长您的试用订阅，请联系 <EMAIL> **
SubscriptionManagementPage.subscription.expireTitle1=此订阅已经过期，系统功能已经受限。
SubscriptionManagementPage.subscription.expireTitle2=** 您已经没有多余的用户月数了，系统功能已经受限，请您进行续费。 **
SubscriptionManagementPage.subscription.expireTitle3=此订阅已激活成功，所有功能都可正常使用。
SubscriptionManagementPage.subscription.msg1=按照当前在用户组'${group}'中的用户数(${userCount})计算，此订阅在<b>${expDate}</b>之前会一直有效。
SubscriptionManagementPage.subscription.msg2=按照当前系统中非访客的用户数(${userCount})计算, 此订阅在<b>${expDate}</b>之前会一直有效。
SubscriptionManagementPage.subscription.installSuccess=订阅密钥安装成功
SubscriptionManagementPage.subscription.deactiveConfirm=这会撤销当前的订阅，系统的功能也会受限，确定继续吗？
SubscriptionManagementPage.disabledUserAreExcludeUserMonthCheck=在计算剩余用户月时，已禁用的用户不会参与计算
SubscriptionKeyEditBean.name=安装订阅密钥
SubscriptionKeyEditBean.installKey.placeholder=请把订阅密钥粘贴在这里
SubscriptionKeyEditBean.installError.1=密钥无法使用，该密钥是用来激活试用订阅的。
SubscriptionKeyEditBean.installError.2=试用订阅密钥不能在这使用
SubscriptionKeyEditBean.installError.3=密钥无法使用，该密钥是用来更新已经在的订阅的。
SubscriptionKeyEditBean.installError.expire=该订阅密钥已过期
SubscriptionKeyEditBean.installError.alreadyUsed=该订阅密钥已经被使用
SubscriptionKeyEditBean.installError.invalidKey=非法的密钥
SupportRequestPanel.title=请求支持
SupportRequestPanel.contactEmail=联系人电子邮件地址
SupportRequestPanel.contactName=联系人姓名
SupportRequestPanel.notEmpty=不能为空
SupportRequestPanel.description.placeholder=请使用文字描述您的预期
SupportRequestPanel.summary=概要
SupportRequestPanel.description=详细描述
SupportRequestPanel.infoToSent=请知晓: 以下信息将会被发送
SupportRequestPanel.basic=基本信息
SupportRequestPanel.pv=产品版本
SupportRequestPanel.ac=代理器数量
SupportRequestPanel.luc=授权的用户数
SupportRequestPanel.ted=试用截止日期
SupportRequestPanel.rum=剩余的用户月
XSubscriptionManager.alert1=您的试用版订阅已过期，目前的功能已受限。请到<a href='/~administration/subscription-management'>付费订阅管理</a>查看详情
XSubscriptionManager.alert2=您的试用版订阅将在1周后到期。请到<a href='/~administration/subscription-management'>付费订阅管理</a>查看详情
XSubscriptionManager.alert3=您的订阅已过期，目前的功能已受限。请到<a href='/~administration/subscription-management'>付费订阅管理</a>查看详情
XSubscriptionManager.alert4=您的订阅将在1周后到期。请到<a href='/~administration/subscription-management'>付费订阅管理</a>查看详情
XSubscriptionManager.alert5=您的订阅将在1个月后到期。请到<a href='/~administration/subscription-management'>付费订阅管理</a>查看详情
SiteInfo.clusterMembers=集群信息
SiteInfo.keyUuids=订阅密钥的识别码
ServerInfo.systemDate=系统当前时间
ServerInfo.mem=已使用的内存
ServerInfo.totalMem=总内存
ServerInfo.jvm=Java虚拟机版本
ServerInfo.os=操作系统版本
ServerInfo.installType=安装类型
ServerInfo.installType.bareMetal=物理服务器或虚拟机
ServerLogPage.tooManyEntry=日志太多了，无法全部显示，只能显示最近的${count}行，如果您需要全部日志，请点击下载。
ServerInformationPage.heapUsage=内存使用率
ServerInformationPage.total=总内存
ServerInformationPage.usedHeap=已使用的内存
ServerInformationPage.Jvm=Java虚拟机版本
ServerInformationPage.OS=操作系统
ServerInformationPage.OsUserName=操作系统用户
ServerInformationPage.forceGC=强制进行垃圾回收(非专业人士慎用)
ServerInformationPage.SystemDate=系统日期
DatabaseBackupPage.desc=如果开启，备份将会在主服务器上运行，当前的主服务器是:
DatabaseBackupPage.backupNow=立即备份
DatabaseBackupPage.backupSettingsUpdated=备份设置更新成功
BackupSetting.schedule.name=备份计划
BackupSetting.schedule.desc=指定一个cron表达式来安排数据库自动备份. cron 表达式格式为 <em>&lt;几秒&gt; &lt;几分&gt; &lt;几时&gt; &lt;几日&gt; &lt;几月&gt; &lt;周几&gt;</em>。例如，<em>0 0 1 * * ?</em> 表示 每天上午1:00。具体的格式，请参考 <a href='http://www.quartz-scheduler.org/documentation/quartz-2.3.0/tutorials/crontrigger.html#format' target='_blank'>Quartz指南</a>。备份文件会放在名叫<em>db-backup</em>的文件夹下，这个文件夹在本系统的安装目录下。如果集群有多台服务器，自动备份会放在<a href='https://docs.devgrip.net/concepts#lead-server' target='_blank'>主服务器</a>上。
BackupSettingHolder.enableAutoSchedule.name=启用自动备份
ContributedAdministrationSettingPage.saveSuccess=保存成功
ServiceDeskSettingPage.desc1=服务台功能可让用户通过发送电子邮件来创建问题、通过电子邮件讨论问题，而无需登录DevGrip。
ServiceDeskSettingPage.desc2=邮件设置中定义的系统邮件地址应作为此类邮件的收件人，项目名称可附加到该地址中，用于指明在哪里创建问题。例如，如果系统邮件地址指定为<tt><EMAIL></tt>，则发送邮件到<tt><EMAIL></tt>会在<tt>myproject</tt>中创建问题。如果未附加项目名称，系统将使用这里的设置策略来查找项目。
ServiceDeskSettingPage.desc3=<b>注意: </b>您必须先设置
ServiceDeskSettingPage.desc4=并启用其<tt>收件箱检查</tt>选项，服务台才会生效。此外，系统电子邮件地址需要启用<a href='https://en.wikipedia.org/wiki/Email_address#Subaddressing' target='_blank' class='text-primary'>子寻址(Subaddressing)，也叫加号寻址(plus addressing)</a>，具体请查看 <a href='https://docs.devgrip.net/' target='_blank' class='text-primary'>文档</a>。
ServiceDeskSettingPage.saveSuccess=服务台设置保存成功
ServiceDeskSettingHolder.enable.name=启用
ServiceDeskSetting.issueCreate.name=问题创建设置
ServiceDeskSetting.issueCreate.desc=创建问题设置，对于指定的发件人，系统将优先使用第一个匹配到的。如果匹配不到任何结果，则无法创建问题。
DefaultProjectSetting.se.name=适用于哪些发件人
DefaultProjectSetting.se.placeholder=任何人
DefaultProjectSetting.se.desc=指定适用于哪些发件人，多个地址用空格分隔。使用'*'或'?'进行通配符匹配。使用'-'前缀排除。留空则表示适用于所有人
DefaultProjectSetting.project.name=默认项目
IssueCreationSetting.ap.name=适用于哪些项目
IssueCreationSetting.ap.desc=指定适用于哪些项目，多个项目用空格分隔。使用'*'或'?'进行通配符匹配。使用'-'前缀排除。留空则表示适用于所有项目
IssueCreationSetting.confidential.name=私密
IssueCreationSetting.confidential.desc=创建的问题是否私密问题
IssueCreationSettingEditPanel.title=问题创建设置
IssueCreationSettingListEditPanel.projects=适用于哪些项目
SshServerKeyPage.desc=指定SSH服务器用于与客户端建立连接的私钥（PEM 格式）
SshServerKeyPage.regen=重新生成私钥
SshServerKeyPage.saveSuccess=SSH设置已保存，SSH服务器已重新启动
SshServerKeyPage.saveConfirm=这将重新启动SSH服务器，是否继续？
SshServerKeyPage.reKeySuccess=私钥已重新生成，SSH服务器已重新启动
GpgServerKeyPage.desc=GPG签名密钥将用于签署系统生成的提交，既包括拉取请求的合并提交也包括通过WebUI或RESTful API 创建的用户提交。
GpgServerKeyPage.gen=生成
GpgServerKeyPage.email=邮件地址
GpgServerKeyPage.pk=公钥
GpgServerKeyPage.keyId=密钥ID
GpgServerKeyPage.copy=复制公钥
GpgServerKeyPage.deleteConfirm=如果删除此密钥，系统之前生成的提交将显示为未验证。如果要继续，请在下面输入 <code>yes</code>。
GpgTrustedKeysPage.desc=在此处添加要信任的GPG公钥。用信任密钥签署的提交将显示为已验证。
GpgTrustedKeysPage.title=添加GPG公钥
GpgTrustedKeysPage.addFail=此密钥或其子密钥已被使用
GpgTrustedKeysPage.subKey=子密钥
GpgTrustedKeysPage.deleteSuccess=GPG密钥删除成功
GpgTrustedKeysPage.deleteConfirm=您确定要删除此GPG密钥吗？
GroovyScriptListPage.canBeUsed=能否被任务使用
GroovyScriptListPage.jobAuth=授权给哪些任务
AnyJobWithI=<i>任何任务</i>
GroovyScriptListPage.na=<i>不适用</i>
GroovyScriptListPage.deleteConfirm=确定要删除吗?
GroovyScriptEditPanel.title=Groovy脚本
GroovyScriptEditPanel.saveFail=该名称已经被使用
GroovyScript.content=内容
GroovyScript.canUsedByJobs.name=能否被构建任务使用
GroovyScript.canUsedByJobs.desc=能否在构建中使用此脚本
GroovyScript.anyJob.name=授权给哪些构建
AnyJob=任何任务
GroovyScript.anyJob.desc=选填，指定允许使用该脚本的构建任务
Test_Settings=测试一下
Save_Settings=保存设置
AutenticatorPage.authTestTitle=身份认证测试
AutenticatorPage.saveSuccess=保存成功
AutenticatorPage.testMsg.fullName=全名: 
AutenticatorPage.testMsg.email=电子邮件: 
AutenticatorPage.testMsg.groups=用户组: 
AutenticatorPage.testMsg.sshKeys=SSH密钥数量: 
AutenticatorPage.testMsg.testPassed=测试成功: 认证通过
AutenticatorPage.testMsg.retrieved= 获取到如下信息:
AuthenticationToken.username.name=用户名
AuthenticationToken.username.desc=指定认证所需的用户名
AuthenticationToken.password.name=密码
AuthenticationToken.password.desc=指定认证所需的密码
AuthenticatorBean.noAuth=无外部认证
Authenticator.defaultGroup.placeholder=无默认组
Authenticator.defaultGroup.desc=选填，将新认证的用户添加到指定组中（如果未查到到成员信息）
Authenticator.defaultGroup.name=默认组
Authenticator.timeout.name=超时
Authenticator.timeout.desc=指定通过此系统进行身份验证时的网络超时（秒）
LdapAuthenticator.name=通用LDAP
LdapAuthenticator.ldapUrl.name=LDAP URL
LdapAuthenticator.ldapUrl.desc=指定LDAP URL，例如：<i>ldap://localhost</i>，或 <i>ldaps://localhost</i>。如果您的 LDAP 服务器使用自签名证书进行 ldaps 连接，则需要配置DevGrip信任该证书，请参考<a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates' target='_blank'>教程</a>
LdapAuthenticator.authRequired.name=必须进行认证
LdapAuthenticator.authRequired.desc=DevGrip需要搜索并确定用户DN，同时在启用群组检索的情况下搜索群组信息。勾选此选项，如果这些操作需要认证，你还需要指定“Manager DN” 和密码，
LdapAuthenticator.managerDN.name=Manager DN
LdapAuthenticator.managerDN.desc=指定 Manager DN 以便DevGrip认证时使用 LDAP 服务器
LdapAuthenticator.managerPwd.name=Manager密码
LdapAuthenticator.managerPwd.desc=指定上述Manager DN的密码
LdapAuthenticator.usb.name=用户搜索基点
LdapAuthenticator.usb.desc=指定用户搜索的基础路径。例如：<i>ou=users, dc=example, dc=com</i>
LdapAuthenticator.usb.placeholder=输入用户搜索基点，按回车键添加
LdapAuthenticator.usf.desc=此筛选器用于确定当前用户的 LDAP 条目。例如：<i>(&(uid={0})(objectclass=person))</i>。在此示例中，<i>{0}</i> 代表当前用户的登录名
LdapAuthenticator.usf.name=用户搜索筛选器
LdapAuthenticator.ufna.name=用户全名属性
LdapAuthenticator.ufna.desc=选填，指定用户 LDAP 条目中的属性名，其值将作为用户的全名。根据 RFC 2798，此字段通常设置为 <i>displayName</i>。若留空，将不会检索用户的全名
LdapAuthenticator.uea.name=用户电子邮件属性
LdapAuthenticator.uea.desc=选填，指定用户 LDAP 条目中的属性名，其值将作为用户的电子邮件。根据 RFC 2798，此字段通常设置为 <i>mail</i>
LdapAuthenticator.uska.name=用户SSH密钥属性
LdapAuthenticator.uska.desc=选填，指定用户 LDAP 条目中的属性名，其值将作为用户的 SSH 密钥。仅当设置此字段时，SSH 密钥将由 LDAP 管理
LdapAuthenticator.gr.name=群组检索
LdapAuthenticator.gr.desc=指定检索群组成员信息的策略。要为 LDAP 组授予适当权限，在DevGrip中应定义具有相同名称的用户组。使用策略 <tt>不检索群组</tt> 则仅在DevGrip端管理群组成员关系
DoNotRetrieveGroups.name=不检索群组
GetGroupsUsingAttribute.name=使用属性检索群组
GetGroupsUsingAttribute.uga.name=用户的群组信息
GetGroupsUsingAttribute.uga.desc=指定要在LDAP用户信息中查找的群组信息，其值包含所属群组的DN。例如，一些 LDAP 服务器使用 <i>memberOf</i> 属性列出群组
GetGroupsUsingAttribute.gna.name=群组名称
GetGroupsUsingAttribute.gna.desc=指定要在LDAP群组中查找的群组名称。该属性的值将映射到DevGrip的用户组。通常设置为 <i>cn</i>
SearchGroupsUsingFilter.name=使用筛选器检索群组
SearchGroupsUsingFilter.gsb.name=群组搜索基点
SearchGroupsUsingFilter.gsb.desc=若在群组端维护成员信息，则此属性指定群组搜索的基础路径。例如：<i>ou=groups, dc=example, dc=com</i>
SearchGroupsUsingFilter.gsf.name=群组搜索过滤器
SearchGroupsUsingFilter.gsf.desc=若在群组端维护组关系，此过滤器用于确定当前用户的所属群组。例如：<i>(&(uniqueMember={0})(objectclass=groupOfUniqueNames))</i>。在此示例中，<i>{0}</i> 表示当前用户的 DN
SearchGroupsUsingFilter.gna.name=群组名称
SearchGroupsUsingFilter.gna.desc=指定要在LDAP群组中查找的群组名称。该属性的值将映射到 DevGrip 用户组。通常设置为 <i>cn</i>
ActiveDirectoryAuthenticator.name=Active Directory
ActiveDirectoryAuthenticator.ldapUrl.name=LDAP URL
ActiveDirectoryAuthenticator.ldapUrl.desc=指定 Active Directory 服务器的 LDAP URL，例如：<i>ldap://ad-server</i> 或 <i>ldaps://ad-server</i>。如果您的 LDAP 服务器使用自签名证书进行 ldaps 连接，则需要 <a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates' target='_blank'>配置 DevGrip 以信任该证书</a>
ActiveDirectoryAuthenticator.mdn.name=Manager DN
ActiveDirectoryAuthenticator.mdn.desc=指定 Manager DN 以便DevGrip认证时使用Active Directory。管理者 DN 应以 <i><账号名>@<域名></i> 的形式指定，例如：<i><EMAIL></i>
ActiveDirectoryAuthenticator.usb.name=用户搜索基点
ActiveDirectoryAuthenticator.usb.desc=指定用户搜索的基础路径。例如：cn=Users, dc=example, dc=com
ActiveDirectoryAuthenticator.usb.placeholder=输入用户搜索基点，按回车键添加
ActiveDirectoryAuthenticator.gsb.placeholder=不检索组
ActiveDirectoryAuthenticator.gsb.name=群组搜索基点
ActiveDirectoryAuthenticator.gsb.desc=若希望检索用户的组成员信息，选填，指定组搜索基础路径。例如：<i>cn=Users, dc=example, dc=com</i>。要为 Active Directory 组授予适当权限，在DevGrip中应定义具有相同名称的用户组。留空则在DevGrip端管理组成员关系
SsoConnectorListPage.addTitle=添加第三方登录提供方
SsoConnectorListPage.colName=名称
SsoConnectorListPage.colCallback=回调地址
SsoConnectorListPage.deleteConfirm=确定要删除吗？
SsoConnectorEditPanel.h5=第三方登录提供方
SsoConnectorEditPanel.saveFail=该名称已经被使用
SsoConnector.name.name=名称
SsoConnector.name.desc=第三方登录提供方的名称，该名称会显示在登录按钮上
SsoConnector.defaultGroup.name=默认的用户组
SsoConnector.defaultGroup.placeholder=无
SsoConnector.defaultGroup.desc=选填，是否将通过此方式认证的用户添加到指定的用户组中
OpenIdConnector.desc=具体用法请参考文档<a href='https://docs.devgrip.net/tutorials/security/sso-with-okta' target='_blank'>第三方登录指南</a>
OpenIdConnector.name.name=名称
OpenIdConnector.name.desc=提供方名称将起到两个作用：<ul><li>显示在登录按钮上<li>构成授权回调URL，其格式为 <i>&lt;服务器URL&gt;/~sso/callback/&lt;名称&gt;</i></ul>
OpenIdConnector.cdu.name=配置发现URL
OpenIdConnector.cdu.desc=指定OpenID提供方的配置发现URL，例如：<code>https://openid.example.com/.well-known/openid-configuration</code>。请确保使用HTTPS协议，因为系统依赖TLS加密来确保token的有效性
OpenIdConnector.clientId.name=客户端ID
OpenIdConnector.clientId.desc=当你在OpenID提供方注册一个应用时，OpenID提供方会给你分配一个客户端ID和客户端密钥。
OpenIdConnector.clientSecret.name=客户端密钥
OpenIdConnector.clientSecret.desc=当你在OpenID提供方注册一个应用时，OpenID提供方会给你分配一个客户端ID和客户端密钥。
OpenIdConnector.rs.name=请求范围
OpenIdConnector.rs.desc=指定要请求的OpenID范围，一个典型的请求范围值: openid email profile，即可以获取用户的openid、email和基本信息。
OpenIdConnector.group=更多设置
OpenIdConnector.gc.name=用户组声明(Groups Claim)
OpenIdConnector.gc.desc=选填，指定后，用以从OpenID提供方处检索用户的组信息。根据提供方的不同，您可能需要在上面请求额外的范围以使此声明可用。
OpenIdConnector.biu.name=按钮图片URL
OpenIdConnector.biu.desc=指定登录按钮上的图片，一般就是提供方的logo。
MicrosoftEntraId.clientId.name=应用(客户端) ID
MicrosoftEntraId.clientId.desc=指定Microsoft Entra的应用(客户端) ID
MicrosoftEntraId.tenantId.name=目录(租户)ID
MicrosoftEntraId.tenantId.desc=指定Microsoft Entra的目录(租户)ID
MicrosoftEntraId.clientSecret.name=客户端密钥
MicrosoftEntraId.clientSecret.desc=指定Microsoft Entra应用的客户端密钥
MicrosoftEntraId.groups.name=获取用户组
MicrosoftEntraId.groups.desc=是否检索用户的组信息。如果启用此选项，请确保通过在Entra ID 中注册的应用的令牌配置添加了用户组声明，这样，用户组声明就可以通过各种令牌类型返回group信息（默认选项)，如：{"GroupsClaim": ["Midwest Sales","Everyone"]}
ProjectListPanel.queryPlaceholder=查询或排序项目
ProjectListPanel.toggleMoreInfo=显示更多信息
ProjectListPanel.snf=项目已被删除或存储项目数据的服务器节点出现异常。
ProjectListPanel.childProjectsCount=${count}个子项目
Qns=无法提交查询
CanNotSaveMq=无法保存格式错误的查询
ProjectListPanel.moveError.noPrivilage=需要项目管理权限才能移动'${project}'
ProjectListPanel.moveError.underItself=无法将项目'${project}'移动到自身或其子项目下
ProjectListPanel.moveError.nameAlreadyUsed=项目'${project}'下已存在名为'${child}'的子项目
ProjectListPanel.moveSuccess=项目移动成功
ProjectListPanel.moveTitle=请选择要移动的项目
ProjectListPanel.moveConfirm=请在下方输入<code>yes</code>以确认将选定项目移动到'${project}'下
ProjectListPanel.selectAsRoot.noPrivilage=需要项目管理权限才能修改'${project}'
ProjectListPanel.selectAsRoot.nameAlreadyUsed=已存在名为'${project}'的根项目
ProjectListPanel.selectAsRootSuccess=项目修改成功
ProjectListPanel.selectAsRootConfirm=请在下方输入<code>yes</code>以确认设置选定项目为根项目
ProjectListPanel.selectAsRootTitle=请选择要修改的项目
ProjectListPanel.deleteSelected.noPrivilage=需要项目管理权限才能删除'${project}'
ProjectListPanel.deleteSelectedSuccess=项目删除成功
ProjectListPanel.deleteSelectedConfirm=请在下方输入<code>yes</code>以确认要删除选定项目
ProjectListPanel.deleteSelectedTitle=请选择要删除的项目
ProjectListPanel.moveAllQueriedError.noPrivilage=需要项目管理权限才能移动'${project}'
ProjectListPanel.moveAllQueriedError.underItself=无法将项目'${project}'移动到自身或其子项目下
ProjectListPanel.moveAllQueriedError.nameAlreadyUsed=项目'${project}'下已存在名为'${child}'的子项目
ProjectListPanel.moveAllQueriedSuccess=项目移动成功
ProjectListPanel.moveAllQueriedConfirm=请请在下方输入<code>yes</code>以确认将所有查询到的项目移动到'${project}'下
ProjectListPanel.moveAllQueriedTitle=没有要移动的项目
ProjectListPanel.selectAllQueriedAsRoot.noPrivilage=需要项目管理权限才能修改'${project}'
ProjectListPanel.selectAllQueriedAsRoot.nameAlreadyUsed=已存在名为'${project}'的根项目
ProjectListPanel.selectAllQueriedAsRootSuccess=项目修改成功
ProjectListPanel.selectAllQueriedAsRootConfirm=在下方输入<code>yes</code>以确认将所有查询到的项目设为根项目
ProjectListPanel.selectAllQueriedAsRootTitle=没有要修改的项目
ProjectListPanel.deleteAllQueried.noPrivilage=需要项目管理权限才能删除'${project}'
ProjectListPanel.deleteAllQueriedSuccess=项目删除成功
ProjectListPanel.deleteAllQueriedConfirm=请在下方输入<code>yes</code>以确认将删除所有查询到的项目
ProjectListPanel.deleteAllQueriedTitle=没有要删除的项目
ProjectListPanel.helpMsg=权限只在实际操作时才进行检查
ProjectListPanel.import.nameLable=从${name}导入
ProjectListPanel.AddChildTitle=添加子项目
ProjectListPanel.foundCount=查到${count}个项目
ProjectListPanel.hiddenMsg=某些项目可能因权限策略而隐藏
StatsPanel.filesCount=${filesCount}个文件
StatsPanel.commitsCount=${commitsCount}个提交
StatsPanel.branchesCount=${branchesCount}个分支
StatsPanel.tagsCount=${tagsCount}个git标签
StatsPanel.issuesCount=${issuesCount}个问题
StatsPanel.buildsCount=${buildsCount}个构建
StatsPanel.packsCount=${packsCount}个包和镜像库
StatsPanel.pullRequestsCount=${pullRequestsCount}个拉取请求
ConfirmDeleteModal.deleteSuccess=项目'${project}'删除成功
ConfirmDeleteModal.deleteConfirm=此项目中的所有内容及其子项目都将被删除且无法恢复，请在下方输入项目路径 <code>${project}</code> 进行确认。
ProjectSelector.search=搜索项目
ProjectSelector.noProject=无
ProjectInfoPanel.forkFrom=，分叉自
ProjectInfoPanel.forkNow=立即分叉
ProjectInfoPanel.serviceDeskDesc=您也可以通过发送邮件到此地址来在此项目中创建问题。
ProjectInfoPanel.replicasSync=同步
ProjectInfoPanel.forksCountTitle=${count}个分叉
ProjectInfoPanel.keyTitle=，简写:${key}
ProjectInfoPanel.serverinfo.active=${server} <span class='badge badge-sm badge-info ml-1'>已激活</span>
ProjectInfoPanel.serverinfo.upToDate=${server} <span class='badge badge-sm badge-success ml-1'>已更新</span>
ProjectInfoPanel.serverinfo.outdate=${server} <span class='badge badge-sm badge-warning ml-1'>已失效</span>
ProjectInfoPanel.syncError=同步项目副本出错，出错的项目id为
ProjectInfoPanel.syncSuccess=已请求同步，请稍后查看状态。
GitProtocolPanel.desc1=您的账户中未配置SSH密钥，您可以
GitProtocolPanel.desc2=添加密钥
GitProtocolPanel.desc3=或切换为
GitProtocolPanel.sshCloneUrl=使用 SSH URL 进行克隆
GitProtocolPanel.httpCloneUrl=使用 HTTP(S) URL 进行克隆
GitProtocolPanel.switchToSsh=切换到SSH
GitProtocolPanel.switchToHttp=切换到HTTP(S)
GitProtocolPanel.fingerprint=服务器指纹: ${fingerprint}
ForkOptionPanel.forkProject=分叉
ForkOptionPanel.forkFail.keyAlreadyUsed=此简写已被其他项目使用
ForkOptionPanel.forkFail.nameAlreadyUsed=此名称已被其他项目使用
ForkOptionPanel.forkSuccess=项目分叉成功
ProjectChoice.choosePro.placeholder=请选择项目...
NoProjectStoragePage.noStoreFound=未找到项目存储
NoProjectStoragePage.psd=项目存储目录
NoProjectStoragePage.notFound=在任何服务器上都找不到
NoProjectStoragePage.noStore=无存储
NewProjectPage.createSuccess=新项目创建成功
NewProjectPage.topbarTitle1=创建子项目
NewProjectPage.topbarTitle2=创建项目
Project.name=项目名称
Project.key.name=项目简写
Project.key.desc=可以为项目定义一个由两个或更多大写字母组成的唯一简写。使用简写引用问题、构建和拉取请求将更加简单高效，格式为 <code>&lt;项目简写&gt;-&lt;编号&gt;</code>，而不是 <code>&lt;项目路径&gt;#&lt;编号&gt;</code>。例如：NE-1
Project.description.name=项目描述
Project.codeManagement.name=代码管理
Project.codeManagement.desc=是否为项目启用代码管理功能
Project.issueManagement.name=问题管理
Project.issueManagement.desc=是否为项目启用问题管理功能
Project.timeTracking.name=时间跟踪
Project.timeTracking.desc1=<b class='text-warning'>注意：</b><a href='https://docs.devgrip.net/tutorials/issue/time-tracking' target='_blank'>时间跟踪</a>是企业版功能。<a href='https://devgrip.net/pricing' target='_blank'>点此免费试用</a>30 天
Project.timeTracking.desc2=启用此项目的<a href='https://docs.devgrip.net/tutorials/issue/time-tracking' target='_blank'>时间跟踪</a>用来跟踪项目进度和生成时间表
Project.packManagement.name=包与镜像库管理
Project.packManagement.desc=为此项目启用<a href='https://docs.devgrip.net/tutorials/package/working-with-packages' target='_blank'>包与镜像库管理</a>
DefaultRolesBean.roleName.name=默认角色
DefaultRolesBean.roleName.rootPlaceholder=无默认角色
DefaultRolesBean.roleName.desc=默认角色会分配给系统所有人都拥有该项目的相关权限。而实际上，用户的权限是此处的默认角色和其父项目的默认角色的<b class='text-warning'>权限之和</b>
ParentBean.parentPath.name=父项目
ParentBean.parentPath.placeholder=无父项目
ParentBean.parentPath.desc=父项目的设置和权限将被此项目继承
GeneralProjectSettingPage.updateFail.noParent=未找到父项目
GeneralProjectSettingPage.updateFail.canNotUse=无法将当前项目或后代项目用作父项目
GeneralProjectSettingPage.updateFail.notAuthMove=无权将项目移动到该父项目下
GeneralProjectSettingPage.updateFail.notAuthSetAsRoot=无权将项目设置为根项目
GeneralProjectSettingPage.updateFail.nameAlreadyUsed=该名称已经被使用
GeneralProjectSettingPage.updateFail.keyAlreadyUsed=该简写已经被使用
GeneralProjectSettingPage.updateSuccess=常规设置更新成功
GeneralProjectSettingPage.deleteSuccess=项目删除请求已发送，即将删除项目
GeneralProjectSettingPage.deleteConfirm=该项目及其所有子项目都将删除，且无法恢复，请在下面输入项目路径 <code>${project}</code> 进行确认。
NoCommitsPanel.h3=该项目还没有任何代码
NoCommitsPanel.youMay=您可以使用以下几种方式初始化项目
NoCommitsPanel.addFiles=添加文件
NoCommitsPanel.settingUp=配置构建
NoCommitsPanel.pushing=从已有代码库中推送代码
NoCommitsPanel.run=在你的git仓库中运行如下命令:
NoCommitsPanel.runNoGit=若已有代码库还没有被git跟踪，请运行如下命令:
NoCommitsPanel.gitinit=git init
NoCommitsPanel.gitadd=git add .
NoCommitsPanel.gitinitialcommit=git commit -m "initial commit"
NoCommitsPanel.gitremote=git remote add origin
NoCommitsPanel.gitpush=git push -u origin main
MailServicePage.title=邮件服务测试
MailServicePage.saveSuccess=邮件服务设置保存成功
MailServicePage.testMail.htmlBody=[测试]DevGrip发送的测试邮件
MailServicePage.testMail.textBody=这是一封由DevGrip发过来的测试邮件。
MailServicePage.testMail.success=非常好，您的邮件服务已经正常工作了。
MailServicePage.testMail.sent=测试邮件已经发送到${email}，请检查你的邮箱。
MailServicePage.testMail.noPrimary=您的账户没有设置主邮件地址。
TaskButton.pleaseWait=请稍等...
MailServicePage.waitTestMail=正在接收测试邮件...
MailServicePage.receivedTestMail=收到测试邮件
MailServicePage.sendingTo=正在发送测试邮件...
MailServiceBean.noMailService=无邮件服务
SmtpImapMailService.smtpHost.name=SMTP服务器
SmtpImapMailService.smtpHost.desc=指定你的smtp服务器地址，通常由您的邮件服务商提供，如: smtp.example.com
SmtpImapMailService.ssl.name=SSL设置
SmtpImapMailService.ssl.desc=<ul><li>隐式TLS会在连接建立之初就启用加密，通常为465端口，因此是直接在加密通道上进行通信。</li><li>显式TLS(StartTLS)是一种通过未加密连接（通常最初为纯文本连接）启动TLS加密的方式，通常为587端口，客户端和服务器会在通信初始阶段协商是否启用加密。</li><li>无论使用隐式TLS（465端口）还是显式TLS（587端口），最终都会建立一个TLS加密连接，因此在安全性上没有实质差别。</li></ul>
SmtpImapMailService.smtpUser.name=SMTP用户名
SmtpImapMailService.smtpPwd.name=SMTP密码
MailService.systemUserAddress.name=系统邮件地址
MailService.systemUserAddress.desc=该地址将作为发件人来发送email通知。若下面的<code>收件箱检查</code>功能启用，用户也可以通过发送邮件到该地址上来创建问题、拉取请求等，详情请参见服务台设置。
MailService.checkIncoming.name=收件箱检查
MailService.checkIncoming.desc=启用以通过邮件处理问题、拉取请求和评论等。<b class='text-danger'>注意:</b>该功能需要您刚才配置的邮件系统支持<a href='https://en.wikipedia.org/wiki/Email_address#Subaddressing' target='_blank'>邮件子寻址</a>，也叫邮件加号寻址(plus addressing)功能，这样DevGrip才能跟踪问题和拉取请求等等。
MailService.timeout.name=网络超时
MailService.timeout.desc=指定和邮件服务器通讯时的超时时间，单位秒
InboxPollSetting.imapHost.name=IMAP服务器
InboxPollSetting.imapUser.name=IMAP用户名
InboxPollSetting.imapUser.desc=指定您的IMAP用户名。<b class='text-danger'>注意: </b> 该账号必须要能收到发往您刚刚配置的'系统邮件地址'的邮件。
InboxPollSetting.imapPwd.name=IMAP密码
InboxPollSetting.pollInterval.name=检查收件箱间隔
InboxPollSetting.pollInterval.desc=指定收件箱检查间隔，单位秒
SmtpImapSSL.noSSL.name=无SSL
SmtpImapSSL.port=端口(Port)
SmtpImapSSL.bcc.name=绕过证书验证(Bypass Certificate Check)
SmtpImapSSL.bcc.desc=如果SMTP主机证书是自签名的，或者其CA根证书未被接受，可以让DevGrip绕过证书检查。<b class='text-danger'>警告：</b>在不受信任的网络中，这可能导致中间人攻击。在这种情况下，您最好<a href='https://docs.devgrip.net/administration-guide/trust-self-signed-certificates#trust-self-signed-certificates-on-server' target='_blank'>将证书导入DevGrip</a>，而不是绕过证书检查。
SmtpImapSSL.explicit.name=显式SSL(Explicit SSL 或 StartTLS)
SmtpImapSSL.implicit.name=隐式SSL(implicit SSL)
Office365MailService.upn.name=用户主体名称
Office365MailService.upn.desc=用于登录Office 365邮件服务器以发送/接收电子邮件的账户主体名称。请确保该账户是上述应用(客户端)ID所指的应用程序的拥有者。
MailService.rt.name=刷新令牌
Office365MailService.rt.desc=上述账户的长期有效刷新令牌，用于生成访问Office 365邮件服务器的访问令牌。提示：该字段的值，需要您点击最右侧的小人图标的按钮来获取。注意，每当租户ID、客户端ID、客户端密钥或用户主体名称更改时，刷新令牌都应重新生成。
Office365MailService.checkIncoming.desc=启用以通过邮件处理问题、拉取请求和评论等。<b class='text-danger'>注意:</b>该功能需要您刚才配置的邮件系统支持<a href='https://learn.microsoft.com/en-us/exchange/recipients-in-exchange-online/plus-addressing-in-exchange-online' target='_blank'>邮件子寻址(Sub addressing)或加号寻址(plus addressing)</a>功能，这样DevGrip才能跟踪问题和拉取请求等等。
RefreshTokenPropertyEditor.login=登录并生成刷新令牌
GmailMailService.clientId.name=客户端Id(Client Id)
GmailMailService.clientId.desc=请输入您在Google cloud注册后的客户端ID
GmailMailService.clientSecret.name=客户端密钥(Client Secret)
GmailMailService.clientSecret.desc=请输入您在Google cloud注册后的客户端密钥
GmailMailService.account.name=账户名
GmailMailService.account.desc=请指定您要登录的gmail的账户名
GmailMailService.rt.desc=上述账户的长期有效刷新令牌，用于生成访问Gmail邮件服务器的访问令牌。提示：该字段的值，需要您点击最右侧的小人图标的按钮来获取。注意，每当租户ID、客户端ID、客户端密钥或用户主体名称更改时，刷新令牌都应重新生成。
GmailMailService.checkIncoming.desc=启用后可以通过email来处理问题、拉取请求评论等等。
AgentsListPage.title=任务代理主要用于在远程机器上运行任务，加快构建速度。它本身是免维护的，一旦启动，它会在必要时自动更新自己。
AgentEditBean.editError=重复的属性(%s)
AgentEditBean.attr=附加信息
AgentEditBean.attr.desc=指定该任务代理的额外信息，例如: env=prod 或 env=test，这些附加信息有助于快速查询到你想要的代理。
AgentDetailPage.colOverview=概况
AgentDetailPage.colBuilds=构建
AgentDetailPage.colLog=日志
AgentLogPage.tooManyLogs=日志太多了，只显示最近的${count}条
AgentLogPage.offlineAgent=您的任务代理已下线，日志暂不可用
AgentOverviewPage.restartSended=重启命令已下发，请稍等...
AgentOverviewPage.restartConfirm=确定要重启吗？
AgentOverviewPage.removed=任务代理删除成功
AgentOverviewPage.removeConfirm=确定要删除吗？
AgentOverviewPage.resume=恢复
AgentOverviewPage.pause=暂停
AgentOverviewPage.atr=访问令牌(Access token)已经重新生成了，请您确保在代理侧使用新的令牌。
AgentOverviewPage.attrSaved=附加信息保存成功
AgentOverviewPage.restartLabel=重启
AgentOverviewPage.removeLabel=删除
AgentOverviewPage.ip=IP地址
AgentOverviewPage.os=操作系统
AgentOverviewPage.arch=平台架构
AgentOverviewPage.statusLabel=状态
AgentOverviewPage.atLabel=访问令牌(Access Token): 
AgentOverviewPage.regen=重新生成
AgentOverviewPage.attrLabel=附加信息(只能在任务代理在线的时候编辑)
AgentOverviewPage.noAttrLabel=还没有定义过附加信息(只能在任务代理在线的时候编辑)
AgentListPanel.query.placeholder=查询代理
AgentListPanel.cpuLabel.desc=CPU能力，以毫秒为单位，通常为 (CPU核心数) * 1000。
AgentListPanel.memLabel.desc=物理内存，单位MB
AgentListPanel.pausedSuccess=选定的代理已经成功暂停
AgentListPanel.pauseTitle=请选择要暂停的代理
AgentListPanel.resumedSuccess=恢复成功
AgentListPanel.resumeTitle=请选择要恢复的代理
AgentListPanel.restartSended=重启命令已发送成功，请稍后
AgentListPanel.restartConfirm=请输入<code>yes</code>来确认要重启选定的代理
AgentListPanel.restartTitle=请选择要重启的代理
AgentListPanel.removeConfirm=请输入<code>yes</code>来确认要删除选定的代理
AgentListPanel.removeTitle=请选择要删除的代理
AgentListPanel.pausedAllQueriedSuccess=已经成功暂停所有查询到的代理
AgentListPanel.pauseAllQueriedConfirm=请输入<code>yes</code>来确认要暂停所有查询到的代理
AgentListPanel.pauseAllQueriedTitle=找不到任何代理
AgentListPanel.resumedAllQueriedSuccess=已经成功恢复所有查询到的代理
AgentListPanel.resumeAllQueriedConfirm=请输入<code>yes</code>来确认要恢复所有查询到的代理
AgentListPanel.resumeAllQueriedTitle=找不到任何代理
AgentListPanel.restartAllQueriedSended=重启命令已发送成功，请稍后
AgentListPanel.restartAllQueriedConfirm=请输入<code>yes</code>来确认要重启所有查询到的代理
AgentListPanel.restartAllQueriedTitle=找不到任何代理
AgentListPanel.removedAllQueriedConfirm=请输入<code>yes</code>来确认要删除所有查询到的代理
AgentListPanel.removeAllQueriedTitle=找不到任何代理
AgentListPanel.foundCount=查到${count}个代理
TokenListPanel.h6=可用的代理令牌(Token)
TokenListPanel.desc=代理令牌(Token)用于授权代理可以连接服务器。如果通过Docker容器运行代理，则应通过环境变量<tt>agentToken</tt>进行配置；如果代理在物理服务器或虚拟机上运行，则应在文件 <tt>&lt;agent dir&gt;/conf/agent.properties</tt> 中配置<tt>agentToken</tt>。如果使用该令牌的代理连接到服务器，则该令牌被标记为已使用，并从此列表中删除。
TokenListPanel.gen=生成新令牌(Token)
TokenListPanel.deleteAllConfirm=确定要删除这些还未使用的令牌吗？
AddAgentPanel.h6=连接新代理
AddAgentPanel.showCmd=显示命令
AddAgentPanel.showCmdTips=每次按下此按钮时，将生成一个新的代理令牌
AddAgentPanel.installOnBmTips=按照以下步骤在远程机器上安装代理(支持 Linux/Windows/Mac OS X/FreeBSD):
AddAgentPanel.bmTips1=确保安装了<a href="https://openjdk.java.net" target="_blank">Java 11 或更高版本</a>
AddAgentPanel.bmTips2=确保安装了Docker，并且docker命令在系统路径中可用。
AddAgentPanel.bmTips3=确保当前用户有权限运行 Docker 容器。
AddAgentPanel.bmTips4=确保安装了Git 2.11.1 或更高版本，并在系统路径中可用。
AddAgentPanel.bmTips5=如果要获取LFS文件，请确保安装了 git-lfs，并在系统路径中可用。
AddAgentPanel.bmTips6=，该压缩包内已经包含了一个新的代理令牌。
AddAgentPanel.bmTips7=将压缩包解压到一个文件夹中。<b class="text-warning">注意：</b>请确保有足够的权限，否则无法启动代理
AddAgentPanel.bmTips8=如有必要，请在文件 <code>conf/agent.properties</code>中修改<code>serverUrl</code>。默认值取自 <i>管理员/系统设置</i> 中指定的服务器 URL
AddAgentPanel.bmTips9=从解压后的文件夹中，在 Windows 上以管理员身份运行 <code>bin/agent.bat</code>，或在其他操作系统上运行 <code>bin/agent.sh start</code>
AddAgentPanel.agentTips=<li>代理服务设计为免维护，一旦连接到服务器，它将在服务器升级时自动更新</li><li>查看 <a href="https://docs.devgrip.net/administration-guide/agent-management" target="_blank">代理管理</a> 以获取详细信息，包括如何将代理作为服务运行的说明</li>
AddAgentPanel.dockerAgentTips=<li>上述命令中的环境变量 <code>serverUrl</code> 取自 <i>管理员/系统设置</i> 中指定的服务器URL，如有必要，您可以自行更改</li>
AddAgentPanel.installOnDockerTips=通过运行以下命令在远程Linux机器上启动代理：
AddAgentPanel.runOnDocker=通过 Docker 容器运行
AddAgentPanel.runOnBm=在物理服务器或虚拟机上运行
AbstractTemplatePage.saveTpl=保存模版
AbstractTemplatePage.saveTplSuccess=模版保存成功
AbstractTemplatePage.useDefaultConfirm=确定要使用默认模版吗？
AbstractTemplatePage.msgWhen=。若要对模版求值，可用以下变量：<ul class='mb-0'>
AbstractTemplatePage.htmlVersion=true为html邮件，false为文本邮件
AbstractNotificationTemplatePage.event=触发通知的事件对象
AbstractNotificationTemplatePage.eventSummary=表示该事件的概要，字符串
AbstractNotificationTemplatePage.eventBody=表示该事件正文内容，可能为 <code>null</code>，字符串
AbstractNotificationTemplatePage.eventUrl=表示该事件详细信息URL，字符串
AbstractNotificationTemplatePage.replyable=指示是否可以通过回复电子邮件直接创建主题评论，布尔值
AbstractNotificationTemplatePage.unsubscribable=退订信息。<code>null</code> 值表示通知不能被退订，对象类型
AlertTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成系统告警邮件的内容
AlertTemplatePage.alert=要显示的告警
AlertTemplatePage.serverUrl=DevGrip服务器的根URL
AlertTemplatePage.topbarTitle=系统告警模板
BuildNotificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成构建通知邮件的内容
BuildNotificationTemplatePage.build=表示要通知的构建对象
BuildNotificationTemplatePage.topbarTitle=构建通知模板
CommitNotificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成提交通知邮件的内容
CommitNotificationTemplatePage.commit=表示要通知的提交对象
CommitNotificationTemplatePage.topbarTitle=提交通知模板
EmailVerificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成邮件地址验证的邮件内容
EmailVerificationTemplatePage.serverUrl=DevGrip服务器的根URL
EmailVerificationTemplatePage.user=要验证电子邮件的用户
EmailVerificationTemplatePage.emailAddress=要验证的电子邮件地址
EmailVerificationTemplatePage.verificationUrl=用于验证电子邮件地址的URL
EmailVerificationTemplatePage.topbarTitle=电子邮件验证模板
IssueNotificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成各种问题通知邮件的内容
IssueNotificationTemplatePage.issue=表示要通知的问题对象
IssueNotificationTemplatePage.topbarTitle=问题通知模板
IssueNotificationUnsubscribedTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成退订问题通知时的反馈邮件的内容
IssueNotificationUnsubscribedTemplatePage.issue=表示已退订的问题
IssueNotificationUnsubscribedTemplatePage.topbarTitle=问题通知模板
PackNotificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成软件包与镜像库通知邮件的内容
PackNotificationTemplatePage.pack=表示要通知的包对象
PackNotificationTemplatePage.topbarTitle=包通知模板
PasswordResetTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成密码重置邮件的内容
PasswordResetTemplatePage.user=要重置密码的用户
PasswordResetTemplatePage.passwordResetUrl=用于重置密码的URL
PasswordResetTemplatePage.topbarTitle=密码重置模板
PullRequestNotificationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成各种拉取请求通知邮件的内容
PullRequestNotificationTemplatePage.pullRequest=表示要通知的拉取请求对象
PullRequestNotificationTemplatePage.topbarTitle=拉取请求通知模板
PullRequestNotificationUnsubscribedTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成退订拉取请求通知时的反馈邮件的内容
PullRequestNotificationUnsubscribedTemplatePage.pullRequest=表示退订的拉取请求
PullRequestNotificationUnsubscribedTemplatePage.topbarTitle=拉取请求通知退订模板
ServiceDeskIssueOpenedTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成通过服务台打开问题时的反馈邮件的内容
ServiceDeskIssueOpenedTemplatePage.issue=表示通过服务台打开的问题
ServiceDeskIssueOpenedTemplatePage.topbarTitle=服务台问题已打开模板
ServiceDeskIssueOpenFailedTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成通过服务台打开问题失败时的反馈邮件的内容
ServiceDeskIssueOpenFailedTemplatePage.exception=表示通过服务台打开问题时遇到的异常
ServiceDeskIssueOpenFailedTemplatePage.topbarTitle=服务台问题打开失败模板
StopwatchOverdueTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成问题计时器超时邮件的内容
StopwatchOverdueTemplatePage.stopwatch=计时器超时
StopwatchOverdueTemplatePage.topbarTitle=问题计时器超时通知模板
UserInvitationTemplatePage.helpText=使用了${groovyLink}的模版技术，动态生成用户邀请邮件的内容
UserInvitationTemplatePage.setupAccountUrl=用于设置用户帐户的URL
UserInvitationTemplatePage.topbarTitle=用户邀请模板
JobExecutorBean.type.name=类型
JobExecutor.spe.name=启用站点发布
JobExecutor.spe.desc=是否启用此选项以允许运行站点发布步骤。DevGrip将按原样提供项目站点文件。为防止XSS攻击，请确保此执行器仅由受信任的构建任务使用
JobExecutor.hrpe.name=启用HTML报告发布
JobExecutor.hrpe.desc=是否启用此选项以允许运行HTML报告发布步骤。为防止XSS攻击，请确保此执行器仅由受信任的构建任务使用
JobExecutor.jr.name=可用于哪些任务
JobExecutor.jr.desc=选填，指定该执行器可用于哪些任务
ServerDockerExecutor.name=本地Docker执行器
ServerDockerExecutor.desc=该执行器将在DevGrip服务器上使用Docker容器来运行所有的构建相关的任务。
JobExecutor.concurrency.name=并发数
JobExecutor.concurrency.placeholder=CPU核心数
JobExecutor.concurrency.desc=指定该执行器可同时运行的最大构建任务数。留空将设置为CPU核心数
ServerDockerExecutor.rl.name=镜像仓库登录
ServerDockerExecutor.rl.desc=如有必要，请先对镜像仓库进行登录。对于内置镜像库，使用<code>@server_url@</code>作为镜像库的URL，<code>@job_token@</code>作为用户名，密码需要指定一个访问令牌(access token)
ServerDockerExecutor.mds.name=挂载Docker Sock
ServerDockerExecutor.mds.desc=是否将docker sock挂载到构建任务的容器中，这样就支持在任务命令中执行docker的相关命令和操作<br><b class='text-danger'>警告</b>：恶意构建任务可能通过操作挂载的docker sock来控制整个系统。如果启用此选项，请确保构建任务是受信任的。
ServerDockerExecutor.alwasyPull.name=始终拉取镜像
ServerDockerExecutor.alwasyPull.desc=运行容器或构建镜像时是否始终拉取镜像。应启用此选项以避免被同一台机器或同一节点上运行的恶意任务替换镜像
ServerDockerExecutor.concurrency.desc=指定该执行器可同时运行的最大任务数/服务数。留空将设置为CPU核心数
ServerDockerExecutor.resouceLimit.placeholder=无限制
ServerDockerExecutor.cpuLimit.name=CPU限制
ServerDockerExecutor.cpuLimit.desc=选填，指定每个使用该执行器的任务/服务的CPU限制。该选项将用作相关容器的<a href='https://docs.docker.com/config/containers/resource_constraints/#cpu' target='_blank'>--cpus</a>选项
ServerDockerExecutor.memLimit.name=内存限制
ServerDockerExecutor.memLimit.desc=选填，指定每个使用该执行器的任务/服务的内存限制。该选项将用作相关容器的<a href='https://docs.docker.com/config/containers/resource_constraints/#memory' target='_blank'>--memory</a>选项
ServerDockerExecutor.dsp.name=Docker Sock路径
ServerDockerExecutor.dsp.placeholder=默认
ServerDockerExecutor.dsp.desc=选填，指定要使用的docker sock。默认值为Linux系统的/var/run/docker.sock，Windows系统的//./pipe/docker_engine
ServerDockerExecutor.bxb.name=Buildx构建器
ServerDockerExecutor.bxb.desc=指定用于构建docker镜像时的dockerx构建器。如果不存在，DevGrip会自动创建此构建器。查看<a href='https://docs.devgrip.net/tutorials/cicd/insecure-docker-registry' target='_blank'>本教程</a>了解如何使用自定义builder进行构建，发布到私有的仓库中。
ServerDockerExecutor.ro.name=其他docker选项
ServerDockerExecutor.ro.desc=选填，指定运行docker命令的其他选项。多个选项应以空格分隔，包含空格的单个选项应加引号。
ServerDockerExecutor.networkOptions.name=docker网络相关选项
ServerDockerExecutor.networkOptions.desc=选填，指定创建网络的docker选项。多个选项应以空格分隔，包含空格的单个选项应加引号。
ServerDockerExecutor.de.name=Docker可执行文件
ServerDockerExecutor.de.placeholder=使用默认值
ServerDockerExecutor.de.desc=选填，指定docker可执行文件的路径，例如<i>/usr/local/bin/docker</i>。留空以使用系统环境变量PATH中的docker可执行文件
JobExecutor.group.ps=权限设置
JobExecutor.group.ms=更多设置
JobExecutor.group.rs=资源设置
ServerDockerExecutor.testData.name=指定一个要测试的Docker镜像
ServerDockerExecutor.dockerimage.placeholder=Docker镜像
ServerDockerExecutor.validate.err1=私有容器镜像仓库登录信息重复
ServerDockerExecutor.validate.err2=docker官方镜像仓库登录信息重复
ServerShellExecutor.name=本地Shell执行器
ServerShellExecutor.desc=该执行器会在DevGrip服务器的Shell中运行构建任务。<br><b class='text-danger'>警告</b>：使用该执行器运行的任务具有与DevGrip服务器进程相同的权限。确保该任务是受信的任务。
ServerShellExecutor.testData.name=指定要运行的Shell/批处理命令
ServerShellExecutor.testData.commands.placeholder=命令
RemoteDockerExecutor.name=远程Docker执行器
RemoteDockerExecutor.desc=该执行器通过<a href='/~administration/agents' target='_blank'>任务代理</a>在远程机器上使用Docker容器运行构建任务。
RemoteExecutor.agentQuery.name=选择任务代理
RemoteExecutor.agentQuery.placeholder=任意代理
RemoteExecutor.agentQuery.desc=指定适用于该执行器的任务代理
RemoteDockerExecutor.concurrency.desc=指定该执行器在每个匹配的代理上可以并发运行的最大任务数/服务数。留空将设置为代理的CPU核心数
RemoteShellExecutor.name=远程Shell执行器
RemoteShellExecutor.desc=该执行器通过<a href='/~administration/agents' target='_blank'>任务代理</a>在远程机器上使用Shell运行构建任务<br><b class='text-danger'>警告</b>：使用该执行器运行的任务具有与相应代理进程相同的权限。确保它是受信的任务。
RemoteShellExecutor.concurrency.desc=指定该执行器在每个匹配的代理上可以并发运行的最大作业数。留空将设置为代理的CPU核心数
K8sExecutor.name=Kubernetes执行器
K8sExecutor.desc=该执行器在Kubernetes集群中作为pod运行构建任务，无需代理。<b class='text-danger'>注意：</b>确保在系统设置中正确指定服务器URL，因为任务 pod需要访问它，如：下载源代码和构建产物
K8sExecutor.bpv.name=构建时使用持久卷(PV)
K8sExecutor.bpv.desc=启用该选项以将任务执行所需的中间文件放置在动态分配的持久卷(persistent volume)上，而不是emptyDir
K8sExecutor.sc.name=构建时使用存储类(Storage Class)
K8sExecutor.sc.placeholder=使用默认存储类(Storage Class)
K8sExecutor.sc.desc=选填，指定一个存储类以动态地为构建分配卷存储。留空将使用默认存储类。<b class='text-warning'>注意：</b>存储类的回收策略应设置为<code>删除</code>，因为该卷仅用于存储临时构建文件
K8sExecutor.ss.name=构建时卷存储(Volume Storage)大小
K8sExecutor.ss.desc=指定要为构建请求的卷存储大小。大小应符合<a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/#setting-requests-and-limits-for-local-ephemeral-storage' target='_blank'>Kubernetes资源容量格式</a>，例如<i>10Gi</i>
K8sExecutor.cpuReq.name=CPU请求
K8sExecutor.cpuReq.desc=指定每个使用该执行器的任务/服务的CPU请求。详情请查阅<a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes资源管理</a>
K8sExecutor.memReq.name=内存请求
K8sExecutor.memReq.desc=指定每个使用该执行器的任务/服务的内存请求。详情请查阅<a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes资源管理</a>
K8sExecutor.cpuLimit.name=CPU限制
K8sExecutor.cpuLimit.desc=选填，指定每个使用此执行器的任务/服务的CPU限制。详情请查阅<a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes资源管理</a>
K8sExecutor.memLimit.name=内存限制
K8sExecutor.memLimit.desc=选填，指定每个使用此执行器的任务/服务的内存限制。详情请查阅<a href='https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/' target='_blank'>Kubernetes资源管理</a>
K8sExecutor.ns.name=节点选择器(Node selector)
K8sExecutor.ns.desc=选填，指定构建任务Pod的节点选择器
K8sExecutor.cr.name=集群角色(Cluster Role)
K8sExecutor.cr.desc=选填，指定构建 Pod的服务账户绑定的集群角色。如果您希望在任务命令中运行其他Kubernetes pod等操作，那么这就是必须指定的。
K8sExecutor.sl.name=服务定位器(Service Locators)
K8sExecutor.sl.desc=选填，类似于节点选择器，指定执行任务的Pod运行在哪些服务下。优先使用第一个匹配的条目。如果没有找到匹配的服务，将使用节点选择器进行匹配。
K8sExecutor.kcf.name=Kubectl配置文件
K8sExecutor.kcf.desc=指定kubectl用于访问集群的配置文件(config file)的绝对路径。留空时kubectl将自动获得集群的访问信息
K8sExecutor.ptk.name=kubectl路径
K8sExecutor.ptk.desc=指定kubectl工具的绝对路径，例如：<i>/usr/bin/kubectl</i>。如果留空，DevGrip将尝试从系统路径中找到此工具
ServiceLocator.an.name=适用于哪些服务
ServiceLocator.an.placeholder=全部
ServiceLocator.an.desc=选填，指定适用于此定位器服务名称，使用空格分隔。使用'*'或'?'通配符进行匹配。用 '-' 前缀表示排除。留空以匹配所有
ServiceLocator.ai.name=适用于哪些镜像
ServiceLocator.ai.placeholder=全部
ServiceLocator.ai.desc=选填，指定适用于此定位器的服务镜像，使用空格分隔。使用'**'、'*'或'?'进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径通配符匹配</a>。用 '-' 前缀表示排除。留空以匹配所有
ServiceLocator.nl.name=节点选择器
ServiceLocator.nl.desc=指定该定位器的节点选择器
NodeSelectorEntry.ln.name=Label名称
NodeSelectorEntry.lv.name=Label值
RegistryLogin.name=镜像库登录
RegistryLogin.ru.name=镜像库地址
RegistryLogin.ru.placeholder=Docker Hub
RegistryLogin.ru.desc=指定镜像库地址URL，留空则代表docker官方镜像库地址。
RegistryLogin.un.name=用户名
RegistryLogin.un.desc=指定所选镜像库的用户名
RegistryLogin.pwd.name=密码
RegistryLogin.pwd.desc=指定所选镜像库的密码或访问令牌
RegistryLogin.pwdSecret.name=任务密钥
RegistryLogin.pwdSecret.desc=指定<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>作为镜像库的密码或者访问令牌
JobExecutor.log.exeJob=执行任务
JobExecutor.log.exeWith=正在执行任务，使用的执行器为: 
JobExecutor.log.cpd=拷贝任务依赖...
JobExecutor.log.pra=等待资源分配...
JobExecutor.log.cewc1=命令已经退出，code为 
JobExecutor.log.cewc2=容器已经退出，code为 
JobExecutor.log.coc=正在下载源代码...
ServerDockerExecutor.log.testDocker=测试指定的容器镜像...
ServerDockerExecutor.log.busybox=检查busybox是否可用...
RemoteExecutor.test.testOn=正在代理上测试
RemoteExecutor.test.paa=等待分配合适的代理...
JobExecutorsPage.hasExecutorsTitle=对于某个具体的任务，将使用第一个适用的执行器。
JobExecutorsPage.noExecutorsTitle=没有找到任务执行器。系统会使用自动发现的执行器，这个执行器的设置都使用系统默认的值。
JobExecutorsPage.editExecutor=编辑任务执行器
JobExecutorsPage.addExecutor=添加任务执行器
JobExecutorPanel.deleteConfirm=确定要删除吗？
JobExecutorEditPanel.saveFail=该名称已经被使用
JobExecutorEditPanel.testSuccess=很好，您的任务执行器测试成功了。
JobExecutorEditPanel.testingTitle=检测执行器
IssueTemplateListPage.deleteConfirm=确定要删除吗？
IssueTemplateListPage.title=在这里可以定义问题描述的模版，这样创建问题时，问题内容会使用预先定义的模版进行填充。通常一个问题描述的模版包含一句话描述、复现步骤、期望行为、实际行为、环境信息、附加信息这几部分，可根据实际情况进行修改。当存在多个模版时，系统会优先使用第一个匹配到的模版。
Issue_Description_Templates=问题描述模版
IssueTemplateEditPanel.h6=问题描述模版
IssueTemplate.issue.desc=选填，指定哪些问题适用于该模版，留空则代表全部
IssueTemplate.issueDesc.name=模版内容
IssueTemplate.issueDesc.desc=请指定问题描述模版的内容，通常包含一句话描述、复现步骤、期望行为、实际行为、环境信息、附加信息这几部分，可根据实际情况修改。
LinkSpecListPage.title=问题关联可以把某个问题下的所有相关问题聚集在一起，方便进一步跟踪。系统内置了子问题、重复问题和相关问题，同时可以根据情况创建自己的问题关联。
LinkSpecListPage.colName=名称
LinkSpecListPage.colOtherName=另一侧的名称
LinkSpecListPage.deleteConfirm=确定要删除吗？
LinkSpecEditPanel.title=问题关联
LinkSpecEditPanel.saveFail.sameName=该名称应与另一侧的名称不同
LinkSpecEditPanel.saveFail.nameAlreadyUsed=该名称已被使用
TimeTrackingSettingPage.saveSuccess=时间跟踪设置保存成功
Time_Tracking_Settings=时间跟踪设置
StateEditPanel.title=问题状态
StateEditPanel.saveFail=该名称已被使用
IssueStateListPage.title=您可以在这里定义所有的问题状态，注意：第一个状态将作为新建问题的初始状态。
IssueStateListPage.colName=名称
IssueStateListPage.colColor=颜色
IssueStateListPage.colDesc=描述
IssueStateListPage.deleteConfirm=确定要删除吗？
IssueStateListPage.topbarTitle=问题状态
IssueStateListPage.noDesc=<i>无</i>
IssueStateListPage.inital=<span class='badge badge-light-info badge-sm ml-2'>初始</span>
CheckIssueIntegrityPage.title=在少数情况下，您的部分问题可能会与现有问题工作流设置不同步（例如，中途修改了问题状态、修改了问题字段、删除了问题状态等）。请运行下方的立即检查，以发现问题并进行修复。
CheckIssueIntegrityPage.run=立即检查
WorkflowReconcilePanel.fixing=正在修复...
WorkflowReconcilePanel.checking.state=正在检查状态...
WorkflowReconcilePanel.checking.fields=正在检查字段...
WorkflowReconcilePanel.checking.value=正在检查字段值...
WorkflowReconcilePanel.checking.sf=正在检查状态和字段顺序...
WorkflowReconcilePanel.complete=您的工作流已经核对完成
StateSpec.color.desc=指定状态的颜色，用于显示和区分
DefaultBoardListPage.title=这里可以为所有项目定义默认的问题看板。当然各个项目也可以覆盖此设置，定义自己的问题看板。注意：如果有多个，第一个看板将作为默认的看板展示(在看板页面可以切换不同看板)
DefaultBoardListPage.deleteConfirm=确定要删除吗？
DefaultBoardListPage.columns=看板列
DefaultBoardListPage.identifyField=标识字段
DefaultBoardListPage.defaultBoards=默认问题看板
BoardEditPanel.title=问题看板
BoardEditPanel.saveFail=该名称已被使用
BoardSpec.bq.name=基础查询
BoardSpec.bq.desc=选填，指定一个基础查询来过滤/排序看板的问题
BoardSpec.blbq.name=待办问题的基础查询
BoardSpec.blbq.desc=选填，指定一个基础查询来过滤/排序待办中的问题。待办问题指的是那些未分配到当前迭代的问题。
BoardSpec.if.name=标识字段
BoardSpec.if.placeholder=请选择一个字段
BoardSpec.if.desc=指定用于标识看板不同列的问题字段。这里只能使用状态和单值枚举字段。例如：问题状态的已开启、进行中、已关闭三种值，看板的列就包含三列，已开启、进行中、已关闭。
BoardSpec.col.name=看板列
BoardSpec.col.placeholder=请至少选择两列
BoardSpec.col.desc=指定看板的列。每一列对应于上面指定的标识字段的一个值，注意：至少选择两列
BoardSpec.ip.name=迭代前缀
BoardSpec.ip.desc=选填，如果指定了前缀，系统将只显示具有该前缀的迭代
BoardSpec.df.name=要显示哪些字段
BoardSpec.df.placeholder=不要显示任何字段
BoardSpec.df.desc=指定在看板的卡片视图中直接显示哪些字段。比如：类型、优先级、迭代等
BoardSpec.dl.name=显示问题关联
BoardSpec.dl.placeholder=不要显示任何关联
BoardSpec.dl.desc=指定在看板的卡片视图中显示哪些问题关联
CommitMessageFixPatternsPage.title=通过在问题编号前后加上特定格式的提交消息来修复问题。每行提交消息都会与这里定义的每个正则表达式进行匹配，来查找待修复的问题编号。系统内置了两种格式:<ul><li>关键字冒号或空格，如提交消息为 fix:issue14。这会提取issue14为问题编号，进而关闭该问题。这里的fix就是关键字，冒号也可以换成空格，同类的关键字还有close,resolve等，还有结尾的句号，也可以换成其他符号，但不能是英文单词字符。</li><li>括号，这种是把问题编号用括号包裹，如(issue14)。</li></ul>
CommitMessageFixPatternsPage.updateSuccess=更新成功
CommitMessageFixPatternsPage.Entry.prefixPattern.name=问题编号前正则表达式
CommitMessageFixPatternsPage.Entry.prefixPattern.desc=在问题编号前指定一个<a href='https://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html'>正则表达式</a>
CommitMessageFixPatternsPage.Entry.suffixPattern.name=问题编号后正则表达式
CommitMessageFixPatternsPage.Entry.suffixPattern.desc=在问题编号后指定一个<a href='https://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html'>正则表达式</a>
CommitMessageFixPatternsPage.validateError=正则表达式语法错误
LinkSpec.name.desc=关联名称
LinkSpec.multi.name=允许多个
LinkSpec.multi.desc=是否允许多个问题被关联起来
LinkSpec.li.name=哪些问题可以被关联
LinkSpec.li.placeholder=所有问题
LinkSpec.li.desc=选填，指定哪些问题可以被关联。留空则表示所有问题都可以被关联。
LinkSpec.asy.name=不对称
LinkSpec.asy.desc=是否该关联不对称，不对称关联即关联两侧的信息是不同的。例如父子关联就是非对称的，在父这边显示子问题，而在子这边显示父问题，而相似关联是个对称的，不论在哪边都显示相似问题。
LinkSpec.sa.name=总是显示
LinkSpec.sa.desc=总是显示该关联，即便没有问题被关关联
LinkSpecOpposite.name.name=另一侧的名称
LinkSpecOpposite.name.desc=该关联的另一侧的名称。例如：如果这一侧的名称为<tt>子问题</tt>，那么另一侧的名称为<tt>父问题</tt>
LinkSpecOpposite.multi.name=另一侧是否允许多个
LinkSpecOpposite.multi.desc=是否允许多个问题被关联到另一侧。以父子问题为例：这一侧的子问题在另一侧是父问题，那么在那一侧允许多个应该不能被勾选，因为不能有多个父亲。
LinkSpecOpposite.li.name=哪些问题可被另一侧关联
LinkSpecOpposite.li.placeholder=所有问题
LinkSpecOpposite.li.desc=选填，指定哪些问题可以被另一侧关联。留空则表示所有问题都可以被关联。
StateTransitionListPage.title=定义一些规则，描述问题状态如何从一个状态转换到另一个状态，可以手动进行，也可以在某些事件发生时自动进行。还可为特定的项目和问题配置规则，甚至还可以为特定的状态只能由特定的角色操作配置规则。
StateTransitionListPage.topbarTitle=问题状态转换
TransitionSpec.from.name=原状态
AnyState=任何状态
NoFieldsRemove=无
TransitionSpec.rf.name=要删掉哪些字段
TransitionSpec.rf.desc=选填，当状态转换发生时，要删掉哪些字段。
BranchUpdatedSpec.name=代码提交后
ApplicableBranches.name=适用于哪些分支
ApplicableBranches.placeholder=任何分支
ApplicableBranches.desc=选填，指定适用于本次状态转换的分支，多个分支中间用空格分隔。 使用 '**', '*' 或者 '?' 进行 <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。'-'不能出现在结尾。留空则表示所有分支都适用。
ApplicableIssues.name=适用于哪些问题
ApplicableJobs.name=适用于哪些任务
ApplicableIssues.desc=选填，指定适用于本次状态转换的问题。留空则表示所有问题都适用。
BranchUpdatedSpec.codeIsCommitedToBranch=代码提交到分支'%s'后
BranchUpdatedSpec.codeIsCommitedToAnyBranch=代码交到到任何分支后
BuildSuccessfulSpec.name=构建成功后
BuildSuccessfulSpec.jn.desc=选填，指定适用于本次状态转换的任务，多个任务中间用空格分隔。 使用 '*' 或 '?' 进行通配符匹配。以'-'开头的会被排除。'-'不能出现在结尾。留空则表示所有任务都适用。
BuildSuccessfulSpec.triggerDesc1=任务 '%s' 在分支 '%s' 上构建成功
BuildSuccessfulSpec.triggerDesc2=任务 '%s' 在任何分支上构建成功
BuildSuccessfulSpec.triggerDesc3=任何任务在分支'%s' 上构建成功
BuildSuccessfulSpec.triggerDesc4=任何任务，在任意分支上构建成功
IssueStateTransitedSpec.name=其他问题的状态发生转换后
IssueStateTransitedSpec.states.name=其他问题的状态转换为哪些状态
IssueStateTransitedSpec.states.desc=例如：它的父问题状态转换为关闭后，该问题自己也需要关闭。
IssueStateTransitedSpec.triggerDesc=其他问题的状态转换为
ManualSpec.name=手动进行状态转换
ToState.name=要转换到哪个状态
AnyUser=任何用户
ManualSpec.ar.name=授权角色
ManualSpec.ar.desc=选填，只有指定的角色才能进行状态转换操作。如果没有指定，则所有的用户均可操作。
ManualSpec.pf.name=哪些字段需要一同变更
ManualSpec.pf.placeholder=无
ManualSpec.pf.desc=选填，有些字段也需要在状态转换时进行相应地变更，在此选择那些需要变更的字段，当问题状态变更时，界面会有这些要一同变更的字段可供修改。
ManualSpec.triggerDesc1=任何用户手动地进行状态转换时
ManualSpec.triggerDesc2=由角色'%s'下的用户手动地进行状态转换时
ManualSpec.roleName.issueSubmitter=<问题提交人>
ManualSpec.roleName.issueSubmitter.desc=该问题的提交人
ManualSpec.userAssociated.desc=该字段所关联的用户
ManualSpec.groupAssociated.desc=该字段所关联的用户组
NoActivitySpec.name=长时间没有任何动态
NoActivitySpec.days.name=多少天没有任何动态
NoActivitySpec.triggerDesc=%s天没有任何动态
PullRequestSpec.branch.name=哪些分支
PullRequestSpec.branch.placeholder=任何分支
PullRequestSpec.branch.desc=选填，检查拉取请求的哪些分支，多个分支以空格分隔。使用'**', '*' 或 '?' 进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除，'-'不能放在结尾。留空则表示匹配所有分支。
PullRequestOpenedSpec.name=拉取请求开启后
PullRequestOpenedSpec.triggerDesc1=在分支'%s'上的拉取请求开启后
PullRequestOpenedSpec.triggerDesc2=在任何分支上的拉取请求开启后
PullRequestMergedSpec.name=拉取请求合并后
PullRequestMergedSpec.triggerDesc1=在分支'%s'上的拉取请求被合并后
PullRequestMergedSpec.triggerDesc2=在任何分支上的拉取请求被合并后
PullRequestDiscardedSpec.name=拉取请求丢弃后
PullRequestDiscardedSpec.triggerDesc1=在分支'%s'上的拉取请求被丢弃后
PullRequestDiscardedSpec.triggerDesc2=在任何分支上的拉取请求被丢弃后
TransitionEditPanel.title=问题状态转换
StateTransitionListPage.deleteConfirm=确定要删除吗？
StateTransitionListPage.anyState=[任何状态]
StateTransitionListPage.when=当 ${triggerDesc}
StateTransitionListPage.issueQuery=适用于这些问题: ${issueQuery}
StateTransitionListPage.allIssues=适用于所有问题
IssueFieldListPage.desc=在这里随你定义问题的字段。<b class="text-warning">注意: </b> 默认情况下，新定义的字段只会出现在新创建的问题中。如果希望这些新字段出现在已有的问题中，可以从“问题列表”页面上批量编辑现有问题，将这些新字段添加进去。
IssueFieldListPage.topbarTitle=自定义问题字段
IssueFieldListPage.colType=类型
IssueFieldListPage.deleteConfirm=确定要删除这些字段吗？
FieldEditPanel.title=自定义问题字段
FieldEditPanel.validateError.1=该名称已被使用
FieldSpec.description.desc=选填，请对该字段进行说明，还可以使用html标签。
FieldSpec.am.name=允许多个值
FieldSpec.am.desc=是否允许为该字段指定多个值
FieldSpec.sc.name=满足一定条件才显示
FieldSpec.sc.placeholder=总是
FieldSpec.sc.desc=如果该字段是否显示取决于其他字段，则启用
FieldSpec.aev.name=允许空值
FieldSpec.aev.desc=是否允许该字段接受空值
FieldSpec.iwiio.name=在问题创建时包含该字段
FieldSpec.iwiio.desc=是否在问题创建时包含此字段。如果不包含，可以通过问题状态转换规则在状态转换到其他状态时再包含该字段。
FieldSpec.ApplicableProjects.desc=指定上方选项适用于哪些项目，多个项目之间用空格分隔。使用 '**', '*' 或 '?' 进行 <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以 '-' 开头的会被排除，同时'-'不能放在结尾。留空表示所有项目都适用。
All_Projects=所有项目
Applicable_Projects_Desc=指定适用于哪些项目，多个项目之间用空格分隔。使用 '**', '*' 或 '?' 进行 <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以 '-' 开头的会被排除，同时'-'不能放在结尾。留空表示所有项目都适用。
FieldSpec.nameEmpty.name=空值的名称
FieldSpec.nameEmpty.desc=指定空值的名称，该名称会以占位符的形式显示出来。如：无，请选择，空。
ShowCondition.when.name=当
ShowCondition.values.placeholder=请选择值...
ValueIsEmpty.name=为空
ValueIsOneOf.name=值为以下其中一个
ValueIsNotAnyOf.name=值不为以下任何一个
ValueIsNotEmpty.name=不为空
Text=文本
Date=日期
Date\ Time=日期和时间
Secret=任务密钥
Checkbox=布尔
Integer=整数
Float=浮点数
Working\ Period=工时
Enumeration=枚举
User=用户
Group=用户组
Issue=问题
Iteration=迭代
Build=构建
Pull\ Request=拉取请求
TextField.ml.name=允许多行输入
TextField.pattern.name=校验该参数的正则表达式
TextField.pattern.desc=选填，指定一个<a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>正则表达式</a>用来校验文本输入的值是否有效
SpecifiedDefaultValue.name=使用指定的默认值
SpecifiedDefaultValue.desc=对于指定的项目，将优先使用第一个匹配到的
SpecifiedDefaultValue.value.name=请指定默认值
ScriptingDefaultValue.name=使用脚本获取默认值
ScriptingDefaultValue.script.name=请选择脚本
ScriptingDefaultValue.script.desc=要执行的Groovy脚本，该脚本应返回一个<i>String</i>，您可以参考<a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>groovy脚本指南</a>来获取更多帮助。
ScriptingDefaultMultiValue.script.desc=要执行的Groovy脚本，该脚本应返回<i>String</i>或<i>List&lt;String&gt;</i>，您可以参考<a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>groovy脚本指南</a>来获取更多帮助。
UserChoiceField.ac.name=请在以下方式中选择
DefaulValue.value.name=默认值
Field.dv.name=默认值
Field.dv.placeholder=无默认值
SpecifiedChoices.name=使用指定的选项
SpecifiedChoices.choices.name=指定选项
ScriptingChoices.name=使用脚本获取选项
ScriptingChoices.script.desc=要执行的Groovy脚本，该脚本应返回这样一个Map结构：<br> <code>return [\"Successful\":\"#00ff00\", \"Failed\":\"#ff0000\"]</code>，如果该值没有颜色，返回<tt>null</tt>。 详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>
ScriptingUserChoices.script.desc=要执行的Groovy脚本，该脚本应返回登录名的列表，即List&lt;String&gt;。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
AllUsers.name=所有用户
AllGroups.name=所有组
GroupChoiceField.ac.name=请在以下方式中选择
GroupChoiceField.editEtime.name=能否编辑估算时间
GroupChoiceField.editEtime.desc=如果勾选，启用时间跟踪后，该字段指示的组可以编辑相应问题的估算时间
ScriptingGroupDefaultValue.script.desc=要执行的Groovy脚本。该脚本应返回用户组名称。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
ScriptingGroupChoice.script.name=要执行的Groovy脚本。该脚本应返回一个用户组的列表。 详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
ScriptingBooleanDefaultValue.script.desc=要执行的Groovy脚本。该脚本应返回一个 <i>boolean</i>，即true或false 。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
Boolean.false=否
Boolean.true=是
IntegerField.min.name=最小值
IntegerField.min.desc=选填，指定该字段的最小值。
IntegerField.max.name=最大值
IntegerField.max.desc=选填，指定该字段的最大值。
ScriptingIntegerDefaultValue.script.desc=要执行的Groovy脚本。该脚本应返回一个 <i>Integer</i> 。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
ScriptingFloatDefaultValue.script.desc=要执行的Groovy脚本。该脚本应返回一个 <i>Float</i> 。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
ScriptingDateDefaultValue.script.desc=要执行的Groovy脚本。该脚本应返回一个 <i>Date</i> 。详情请参考 <a href='https://docs.devgrip.net/appendix/scripting' target='_blank'>脚本指南</a>。
TimeTrackingSetting.hpd.name=每天工作几小时
TimeTrackingSetting.hpd.desc=指定每天工作几小时。这会影响到将影响工作时间的解析和显示。例如：如果该值设置为<tt>8</tt>，则这时<tt>1d</tt>和<tt>8h</tt>是一样的。
TimeTrackingSetting.dpw.name=每周工作几天
TimeTrackingSetting.dpw.desc=指定每周工作几天。这会影响到将影响工作时间的解析和显示。例如：如果该值设置为<tt>5</tt>，则这时<tt>1w</tt>和<tt>5d</tt>是一样的。
TimeTrackingSetting.al.name=汇总关联问题
TimeTrackingSetting.al.placeholder=不汇总
TimeTrackingSetting.al.desc=如果指定，则在计算问题的预估/花费时间时包含这些关联问题后进行汇总。
TimeTrackingSetting.getWorkingPeriodHelp=格式为一次或多次的数字和w或d或h或m的组合。例如： <tt>1w 1d 1h 1m</tt> 表示1周1天1小时和1分钟，<tt>5w1d</tt> 表示5周1天
TimeTrackingSetting.workingPeriodHelpOnlyUseHours=格式为一次或多次的数字与h或m的组合。例如: <tt>1h 1m</tt> 表示1小时1分钟，<tt>1h</tt> 表示1小时
TimeTrackingSetting.useHoursAndMinutesOnly.name=只使用小时和分钟
TimeTrackingSetting.useHoursAndMinutesOnly.desc=是否在输入和展示预估/花费时间时只使用小时和分钟
TimeTrackingSetting.deleteLinkUsaege=时间汇总关联
TimeTrackingSetting.parse.invalid=格式错误
WorkflowChangeAlertPanel.alert1=您的问题工作流发生变更，
WorkflowChangeAlertPanel.alert2=请点击此处进行核对。
WorkflowChangeAlertPanel.alert3=注意，该操作需要您来执行以确保问题的一致性。当然您可以先把手头上重要事项做完，再回到这里执行该操作。
WorkflowChangeAlertPanel.reconcile.name=立即核对(需要管理员权限)
WorkflowReconcilePanel.fus=修复未定义的状态
WorkflowReconcilePanel.fuf=修复未定义的字段
WorkflowReconcilePanel.fufv=修复未定义的字段值
WorkflowReconcilePanel.alert=请解决下方所示的未定义状态。注意如果你选择删除未定义状态，那么所有的带有该状态的问题也会被删除。
WorkflowReconcilePanel.alertFields=请解决下方所示的未定义字段
WorkflowReconcilePanel.alertValues=请解决下方所示的未定义字段值
WorkflowReconcilePanel.undefined=未知状态
WorkflowReconcilePanel.changeTo=更改为
WorkflowReconcilePanel.fix=修复
WorkflowReconcilePanel.fn=字段名称
WorkflowReconcilePanel.fv=字段值
WorkflowReconcilePanel.res=解决方案
FieldInstanceIgnoreValue.displayName=忽略该字段
FieldInstanceSpecifiedValue.displayName=使用指定的值
FieldInstanceSpecifiedValue.secretDisplayName=使用指定的任务密钥
FieldInstanceScriptingValue.displayName=执行脚本获取值
FieldInstanceScriptingValue.secretDisplayName=执行脚本获取密钥
ProjectBlobPage.btnClone.title=代码克隆或下载
ProjectBlobPage.btnAdd.title=添加文件到当前文件夹中
ProjectBlobPage.btnSearch.title=搜索文件、符号和文本
ProjectBlobPage.btnHistroy.title=查看当前分支的提交历史
ProjectBlobPage.projectTitle=源代码
ProjectBlobPage.revisionIndexing=正在索引修订版本... (搜索会在索引完成后才会精准)
ProjectBlobPage.enableBuildDesc=要使用构建(CI/CD)功能，请为您的项目
ProjectBlobPage.adding=添加${blobPath}文件
ProjectBlobPage.storedLFS=使用了 Git LFS 存储
ProjectBlobPage.missing=该文件是个Git LFS 对象，但是文件已经丢失。
ProjectBlobPage.bf=二进制文件
ProjectBlobPage.tooLarge=文件太大了，无法加载
ProjectBlobPage.addOnBranch=在分支中添加 
ProjectBlobPage.deleteFromBranch=从分支中删除 
ProjectBlobPage.reviewNeed=无法删除，因为分支保护配置了review要求，请使用拉取请求。
ProjectBlobPage.buildNeed=无法删除，因为分支保护配置了构建要求，请使用拉取请求。
ProjectBlobPage.loginFirst=请先登录
ProjectBlobPage.noPerm=该操作需要代码写权限
ProjectBlobPage.disallow=该操作被分支保护规则被禁止
ProjectBlobPage.getPermalink=请按'y'获取固定链接
QuickSearchPanel.title=搜索文件和符号
QuickSearchPanel.title2=在当前分支中搜索
QuickSearchPanel.showMore=更多
QuickSearchPanel.noMatch=无结果
AdvancedSearchPanel.title=高级搜索
AdvancedSearchPanel.tree=在当前文件夹中搜索
SearchInput.validate.1=搜索过于笼统
SearchInput.validate.2=语法错误
SearchInput.validate.3=该字段是必填的
TextQueryOptionEditor.fileName=文件名
TextQueryOptionEditor.sName=符号名
SearchResultPanel.result=搜索结果
SearchResultPanel.prev=上一个
SearchResultPanel.next=下一个
SearchResultPanel.exp=全部展开
SearchResultPanel.coll=全部折叠
SearchResultPanel.nothing=查不到任何结果
SearchResultPanel.tooManyResults=(搜索到的结果太多了，仅显示${num}条)
GetCodePanel.download1=下载zip
GetCodePanel.download2=下载tar.gz
GetCodePanel.copy=拷贝
GetCodePanel.clone1=在VSCode中克隆
GetCodePanel.clone2=在IntelliJ中克隆
BlobUploadPanel.title=上传文件
BlobUploadPanel.dir=选填，指定一个相对目录来上传文件
BlobUploadPanel.cm=提交消息
BlobUploadPanel.addViaUpload=这是通过上传添加的文件
BlobUploadPanel.upload=上传
BlobUploadPanel.dirTitle=目录
BlobUploadPanel.file=文件
BlobNavigator.typeName=请输入文件名
BlobNavigator.root=根目录
NoNameFormComponent.title=请先在上方输入文件名，然后才能编辑文件内容。
BlobEditPanel.editSource=编辑源代码
BlobEditPanel.toUtf8=将被转码为UTF-8
BlobEditPanel.commitToUtf8=<svg class='icon icon-sm mr-1'><use xlink:href='%s'/></svg> %s 提交的文件将被转码为UTF-8
CommitOptionPanel.title=提交您的更改
CommitOptionPanel.commitMsgDelete=删除 ${oldName}
CommitOptionPanel.commitMsgAdd1=添加 ${newName}
CommitOptionPanel.commitMsgAdd2=添加新文件
CommitOptionPanel.commitMsgEdit=编辑 ${oldName}
CommitOptionPanel.commitMsgRename=重命名 ${oldName}
CommitOptionPanel.error.filenameNeed=请指定文件名
CommitOptionPanel.error.reviewNeed=本次更改无法提交，因为分支保护配置了评审要求，请使用拉取请求代替
CommitOptionPanel.error.buildNeed=本次更改无法提交，因为分支保护配置了构建要求，请使用拉取请求代替
CommitOptionPanel.error.signNeed=本次更改无法提交，因为分支保护配置了验证签名要求，而系统还未生成GPG签名，请先生成系统的GPG签名密钥。
CommitOptionPanel.error.nameAlreadyUsed=名称已被使用，请选择其他的名称再试尝试。
CommitOptionPanel.error.fileExists=在您试图创建子目录的地方存在一个文件，请选择新的路径并重试
CommitOptionPanel.warn.someoneChanged=在您开始编辑后，有人做了以下更改
FolderViewPanel.loadLastCommit=<span class='text-warning'>正在加载最近的提交信息...</span>
FolderViewPanel.binaryFile=它似乎是个二进制文件！
BlobViewPanel.viewSource=查看源代码
BlobViewPanel.blame=变更记录
BlobViewPanel.download=下载源文件
BlobViewPanel.fileTooLarge=由于文件过大，仅显示前 ${lines} 行。
BlobViewPanel.editFileTooLarge=该文件文件过大，无法编辑
BlobViewPanel.editOnBranch=在分支上编辑
BlobViewPanel.deleteFromBranch=从分支上删除
SourceFormComponent.title=以下内容是从最近一次未保存的更改中恢复而来的，若不需要可以丢弃它们。
SourceViewPanel.codecomment=代码评论
SourceViewPanel.hidecomment=隐藏评论
SourceViewPanel.sccs=显示带有评论的代码片段
SourceViewPanel.outline=代码结构
SourceViewPanel.lineTooLong=无法评论，该行太长了
SourceViewPanel.tooLong=评论内容过长
SymbolLinkPanel.title=符号链接: 
RevisionSelector.title.focb=查找或创建分支
RevisionSelector.title.fb=查找分支
RevisionSelector.title.foct=查找或创建git标签
RevisionSelector.title.ft=查找git标签
RevisionSelector.colBranches=分支
RevisionSelector.colTags=git标签
RevisionSelector.createBranchesLabel=基于${revision}创建分支<b>${ref}</b>
RevisionSelector.createTagsLabel=基于${revision}创建git标签<b>${ref}</b>
RevisionSelector.noBranch=找不到分支
RevisionSelector.noTag=找不到git标签
RevisionSelector.canNotFindRevision=无法找到该修订版本${revision}对应的提交。
RevisionSelector.inputPlaceholder=请输入修订版
RevisionPicker.choose=选择修订版本
RevisionPicker.projectNotSpecify=项目还未指定
RevisionPicker.selectProject=<i>请先选择项目</i>
GitLinkPanel.subMoudle=git子模块: 
ProjectBranchesPage.base=基础
ProjectBranchesPage.fb=根据分支名称筛选
ProjectBranchesPage.default=默认
ProjectBranchesPage.setDefault=设为默认
ProjectBranchesPage.create=创建分支
ProjectBranchesPage.pleaseConfirm=请您确认
ProjectBranchesPage.openPr1=有
ProjectBranchesPage.openPr2=已开启的拉取请求
ProjectBranchesPage.openPr3=在分支
ProjectBranchesPage.openPr4=上，如果删除该分支，这些拉取请求会被丢弃。
ProjectBranchesPage.createError.nameAlreadyExists=分支名'${branchName}'已经存在，请选别的名称
ProjectBranchesPage.createError.unable=无法创建分支，根据分支保护规则，分支创建已被禁止。
ProjectBranchesPage.createError.needSign=无法创建分支，根据分支保护规则，该分支最新的提交需要有效签名。
ProjectBranchesPage.createSuccess=分支'${branchName}'创建成功
ProjectBranchesPage.prOpen=已开启
ProjectBranchesPage.prOpenTitle=此次更改已经开启了一个新的拉取请求
ProjectBranchesPage.prMerged=已合并
ProjectBranchesPage.prMergedTitle=此次更改已通过拉取请求变基到基础分支
ProjectBranchesPage.deleteTitle1=无法删除默认分支
ProjectBranchesPage.deleteTitle2=由于有分支保护规则，您无法删除该分支
ProjectBranchesPage.deleteTitle3=您选择删除分支 ${branch}
ProjectBranchesPage.deleteSuccess=分支'${branch}'删除成功
ProjectBranchesPage.ahead=领先
ProjectBranchesPage.ahead.title=比基础分支领先${count}个提交
ProjectBranchesPage.behind=落后
ProjectBranchesPage.behind.title=比基础分支落后${count}个提交
BranchBean.branchName.name=分支名称
BranchBean.branchName.invalid=分支名称格式错误
CommitDetailPage.browseCode=浏览代码
CommitDetailPage.moreActions=更多操作
CommitDetailPage.moreActions.createBranch=创建分支
CommitDetailPage.moreActions.createTag=创建git标签
CommitDetailPage.moreActions.cherryPick=挑捡提交(cherry-pick)
CommitDetailPage.moreActions.revert=回滚(revert)
CommitDetailPage.cherryPick.error=没有分支，无法挑捡
CommitDetailPage.cherryPick.selectBranch=请选择要进行挑捡的分支
CommitDetailPage.revert.error=没有可回滚的分支
CommitDetailPage.revert.selectBranch=请选择要回滚到哪个分支
CommitDetailPage.specifyCommitMessage=指定提交信息
CommitDetailPage.cherryPick.error2=挑拣提交到分支 ${branch} 时出错: ${errMsg}
CommitDetailPage.revert.error2=回滚到分支 ${branch} 时出错: ${errMsg}
CommitDetailPage.cherryPick.error3=挑拣提交到分支 ${branch} 时出错: 检测到合并冲突
CommitDetailPage.revert.error3=回滚到分支 ${branch} 时出错: 检测到合并冲突
CommitDetailPage.cherryPick.success=挑捡成功
CommitDetailPage.revert.success=回滚成功
CommitDetailPage.revert.msg=回滚 "${commitMsg}" \n\n本次回滚产生的提交为 ${commitName}
CommitDetailPage.parent.commit=显示父提交
CommitDetailPage.parent.compare=和父提交比较
CommitDetailPage.1parent=1个父提交
CommitDetailPage.multiParent=${parentCount}个父提交
CommitListPanel.compare=和基础版本比较
CommitListPanel.commitsError=获取提交时出错，详细错误请检查日志
CommitMessagePanel.toggleDetail=显示详情
ProjectTagsPage.ft=根据git标签名称筛选
ProjectTagsPage.toggleMessage=显示详情
ProjectTagsPage.createdBy=创建人为 
ProjectTagsPage.createTag=创建git标签
ProjectTagsPage.createError.nameAlreadyExists=标签名'${tagName}'已经存在，请选别的名称
ProjectTagsPage.createError.unable=无法创建，根据git标签保护规则，创建已被禁止。
ProjectTagsPage.createSuccess=git标签'${tagName}'创建成功
ProjectTagsPage.deleteConfirm=确定要删除git标签'${tagName}'吗?
ProjectTagsPage.deleteTitle=根据标签保护规则，禁止删除
ProjectTagsPage.deleteSuccess=git标签'${tagName}'删除成功
TagBean.tagName.name=标签名
TagBean.tagMessage.name=说明
TagBean.tagName.invalid=标签名格式错误
CommitStatusLink.title1=部分构建处于${status}，点此查看详细信息
CommitStatusLink.title2=构建处于${status}，点此查看详细信息
CommitStatusLink.title3=无构建
RevisionDiffPanel.diffOps=差异选项
RevisionDiffPanel.bs=批量建议
RevisionDiffPanel.toggleNav=显示/隐藏文件导航
RevisionDiffPanel.downloadPatch=下载补丁文件
RevisionDiffPanel.noDiff=无差异
RevisionDiffPanel.error.prUpdated=拉取请求已被别人更新，请重新尝试
RevisionDiffPanel.error.sugOnMark=请删除已失效的建议: ${mark}
RevisionDiffPanel.discardConfirm=您确定要丢弃批量建议吗？
RevisionDiffPanel.pathHint=包含空格或以短横线开头的路径需要用引号括起来
RevisionDiffPanel.reviewedLabel=已评审 ${progress}
RevisionDiffPanel.tooManyFiles=由于文件过多，仅显示前 ${size} 个文件
RevisionDiffPanel.totalLabel=总共${totalFileChangesCount}个文件有更改(已显示${displayFileChangesCount}个)新增了${additionsCount}行和删减了${deletionsCount}行
RevisionDiffPanel.noDiffToNavigate=<i>无差异</i>
RevisionDiffPanel.malformedPathFilter=路径筛选器格式错误
DiffStatBar.tooltipTitle=${additions}新增、${deletions}删减
CommentPanel.saveWarn=你正在编辑的内容已被人修改了，请重新加载页面并重试。
CommentPanel.deleteConfirm=确定要删除该评论吗？
BlobDiffPanel.blobMessage.missing=文件已经丢失
BlobDiffPanel.blobMessage.fileTooLarge=无法比对差异，因为文件过大。
BlobDiffPanel.blobMessage.someLineTooLarge=无法比对差异，因为一些行过大。
BlobDiffPanel.blobMessage.diffTooLarge=该文件差异过大默认不渲染。
BlobDiffPanel.blobMessage.fileWasDeleted=该文件已被删除默认不渲染。
BlobDiffPanel.blobMessage.lockFile=锁文件或依赖清单默认不渲染。
BlobDiffPanel.blobMessage.testFile=测试相关的文件默认不渲染。
BlobDiffPanel.blobMessage.generatedFile=自动生成的文件默认不渲染。
BlobDiffPanel.blobMessage.totalDiffsExceed=总差异行数已超过最大限制默认不渲染。
BlobDiffPanel.blobMessage.unknownDiff=未知差异。
BlobDiffPanel.blobMessage.emptyAdded=成功添加了一个空文件。
BlobDiffPanel.blobMessage.emptyRemoved=成功删除了一个空文件。
BlobDiffPanel.blobMessage.bf=二进制文件不渲染。
BlobDiffPanel.blobMessage.cii=内容完全相同不渲染。
BlobDiffPanel.setUnreviewed=设为未评审
BlobDiffPanel.setReviewed=设为已评审
BlobDiffPanel.editOnSourceBranch=在源分支上编辑
BlobDiffPanel.editOnBranch=在${revision}上编辑
BlobDiffPanel.loadDiff=加载
BlobDiffPanel.viewSource=查看源代码
BlobImageDiffPanel.before=修改前
BlobImageDiffPanel.after=修改后
InvalidCodeCommentPage.missingCommits=有些相关提交的代码评论已经丢失
InvalidCodeCommentPage.missingCommitsDesc=当项目指向错误的 git 仓库或提交被垃圾回收时，可能会出现这种情况。
InvalidCodeCommentPage.missingCommitsLabel=丢失的提交
InvalidCodeCommentPage.commentFileLabel=在文件上评论
InvalidCodeCommentPage.codeComments=代码评论
InvalidCodeCommentPage.commentContentLabel=评论内容
InvalidCodeCommentPage.deleteButton=删除评论
InvalidCodeCommentPage.confirmDeleteMessage=确定要删除该评论吗？
InvalidCodeCommentPage.deleteSuccessMessage=代码评论'${codeCommentId}'删除成功
CodeCommentPanel.action.replied=回复于
CodeCommentPanel.action.commented=评论于
CodeCommentPanel.showCommentContext=当前上下文与添加此评论时的上下文不同，点击以显示评论上下文。
CodeCommentPanel.showReplyContext=当前上下文与添加此回复时的上下文不同，点击以显示回复上下文。
CodeCommentPanel.deleteConfirm=确定要删除该评论吗？
CodeCommentPanel.deleteReplyConfirm=您确定要删除该回复吗？
CodeCommentPanel.deleteRepliesConfirm=删除该评论也会删除所有回复，您确定要继续吗？
CodeCommentPanel.leaveANote.placeholder=请在这里备注
CodeCommentPanel.contentLabel=内容
CodeCommentPanel.resolved=已解决
CodeCommentPanel.unresolved=未解决
CodeCommentPanel.permanentLink=固定链接
StatusChangeOptionBean.name=确认您的操作
CodeCommentListPanel.search.placeholder=查询或排序评论
CodeCommentListPanel.selectToResolved=请选择要设置为已解决的评论
CodeCommentListPanel.noComentsToResolved=没有评论可设置为已解决
CodeCommentListPanel.noComentsToUnresolved=没有评论可设置为未解决
CodeCommentListPanel.noComentsToDelete=没有评论可删除
CodeCommentListPanel.noComentsToRead=没有评论可设置为已读
CodeCommentListPanel.selectToUnresolved=请选择要设置为未解决的评论
CodeCommentListPanel.deleteSelectedConfirm=请在下方输入 <code>yes</code> 以删除选中的问题
CodeCommentListPanel.deleteAllQueriedConfirm=请在下方输入 <code>yes</code> 以删除所有查询到的评论
CodeCommentListPanel.deleteSelectedTitle=请选择要删除的问题
CodeCommentListPanel.foundCommentsCount=查到${count}条评论
CodeCommentListPanel.onFile=源文件 ${path}。
RevisionComparePage.commonAncestor=共同祖先
RevisionComparePage.swap=交换
RevisionComparePage.compare=比较
RevisionComparePage.base=基础
RevisionComparePage.createPr=为此次更改创建拉取请求
RevisionComparePage.selectToCompare=请选择要比较的修订版本
RevisionComparePage.historyOfComparing=要比较的两个修订版本没有共同的历史记录
RevisionComparePage.tooltip.onlyCompreWithCommonAncestor=在涉及不同项目时只能与共同祖先进行比较
RevisionComparePage.tooltip.checkToCompareRightSideWithCommonAncestor=选中此项，系统将使用左右两侧的共同祖先与“右侧”进行比较
RevisionComparePage.effectiveRequest.labelPrOpen=此次更改已经开启了一个拉取请求，编号为
RevisionComparePage.effectiveRequest.label=此次更改已通过压缩(squashed)/变基(rebased)的方式到基础分支，编号为
RevisionComparePage.prLink.label=拉取请求#${number}
RevisionComparePage.fileChanges=文件更改
InvalidPullRequestPage.missingCommits=拉取请求的相关提交信息已经丢失
InvalidPullRequestPage.description=这可能是因为项目指向了错误的Git仓库，或者这些提交已被垃圾回收。
InvalidPullRequestPage.missingCommitsTitle=提交信息已经丢失
InvalidPullRequestPage.branches=拉取请求的分支
InvalidPullRequestPage.title=拉取请求的标题
InvalidPullRequestPage.descriptionTitle=拉取请求的说明
InvalidPullRequestPage.delete=删除拉取请求
InvalidPullRequestPage.deleteSuccess=拉取请求#${number}删除成功
InvalidPullRequestPage.deleteConfirm=确定要删除拉取请求#${number}吗？
PullRequestListPanel.queryHint=查询/排序拉取请求
PullRequestListPanel.openNew=开启新的拉取请求
PullRequestListPanel.comments=评论
PullRequestListPanel.watchSuccess=关注状态已更改
PullRequestListPanel.noPrWatchUnWatch=没有拉取请求可关注/取消关注
PullRequestListPanel.noPrDiscard=没有拉取请求可以丢弃
PullRequestListPanel.noPrDelete=没有拉取请求可以删除
PullRequestListPanel.noPrRead=没有拉取请求可标记为已读
PullRequestListPanel.selectToWatchUnWatch=请选择要关注/取消关注的拉取请求
PullRequestListPanel.selectToDiscard=请选择要丢弃的拉取请求
PullRequestListPanel.discardConfirm=请在下方输入 <code>yes</code> 以丢弃选中的拉取请求
PullRequestListPanel.error.alreadyClosed=拉取请求#${number}已关闭
PullRequestListPanel.deleteSelectedConfirm=请在下方输入 <code>yes</code> 以删除选中的拉取请求
PullRequestListPanel.deleteSelectedTitle=请选择要删除的拉取请求
PullRequestListPanel.discardAllQueriedConfirm=请在下方输入 <code>yes</code> 以丢弃所有查询到的拉取请求
PullRequestListPanel.deleteAllQueriedConfirm=请在下方输入 <code>yes</code> 以删除所有查询到的拉取请求
PullRequestListPanel.foundCount=查到${count}个拉取请求
ReviewStatusIcon.title.pending=待审核
ReviewStatusIcon.title.approved=已批准
ReviewStatusIcon.title.requestedForChanges=请求更改
ReviewListPanel.requestView.title=请求评审
ReviewListPanel.removeReviewer.title=移除该评审人
ReviewListPanel.reviewAnotherConfirm=确定要再次向'${reviewer}'请求评审吗？
ReviewListPanel.resetMyReview=重置我的评审
ReviewListPanel.deleteReviewer=确定要移除评审者'${reviewer}'吗？
ReviewListPanel.canNotDeleteReviewer=评审人'${reviewer}'是必需的，不能移除。
ReviewerChoice.addReviewer.placeholder=添加评审人...
PullRequestSingleChoice.choose.placeholder=选择拉取请求...
AssigneeChoice.addAssignee=添加负责人...
AssignmentListPanel.removeAssigneeTitle=移除该负责人
AssignmentListPanel.deleteAssigneeConfirm=确定要移除负责人'${assignee}'吗？
NewPullRequestPage.target=目标
NewPullRequestPage.source=源
NewPullRequestPage.selectAnother=请选择不同的分支
NewPullRequestPage.mergeDesc.1=目标分支
NewPullRequestPage.mergeDesc.2=已经更新了源分支
NewPullRequestPage.mergeDesc.3=的所有提交。
NewPullRequestPage.mergeDesc.4=请尝试
NewPullRequestPage.mergeDesc.5=交换目标和源
NewPullRequestPage.mergeDesc.6=进行比较。
NewPullRequestPage.unrelatedMsg=目标分支和源分支的历史记录毫无关联
NewPullRequestPage.needSelectBranch=请选择分支来创建拉取请求
NewPullRequestPage.inputTitle.placeholder=请在这里输入标题
NewPullRequestPage.inputTitle.label=标题
NewPullRequestPage.mergeStrategy=合并策略
NewPullRequestPage.reviewers=评审人
NewPullRequestPage.assignees=负责人
NewPullRequestPage.assignToMe=分配给我
NewPullRequestPage.mergedBy=拉取请求只能在获得所有评审人批准后才能合并。
NewPullRequestPage.assigneePermission=负责人具有代码写入权限，并将负责合并该请求
NewPullRequestPage.sendPullRequest=发送拉取请求
NewPullRequestPage.sendWarn=目标分支或源分支刚刚有新的提交，请重新检查。
NewPullRequestPage.title=标题
NewPullRequestPage.description.error=描述过长
NewPullRequestPage.prMergePreview=合并预览
NewPullRequestPage.mergeWithoutConflicts=没有冲突，可以合并
NewPullRequestPage.mergeWithConflicts=本次合并有冲突。不过，您仍然可以创建拉取请求
NewPullRequestPage.mergeCaculating=正在计算合并预览...
NewPullRequestPage.createPrWithHtml=<span class='text-nowrap'>创建拉取请求</span>
Create\ Merge\ Commit=总是创建合并提交
Create\ Merge\ Commit\ If\ Necessary=快进合并或创建合并提交
Squash\ Source\ Branch\ Commits=压缩后合并(Squash)
Rebase\ Source\ Branch\ Commits=变基(Rebase)
MergeStrategy.createMergeCommitDesc=将源分支的所有提交添加到目标分支，并创建一个合并提交，即使目标分支可以快进合并（fast-forward)，系统也会创建一个新的合并提交。这种方式保留了完整的历史记录，并且明确记录了合并操作。
MergeStrategy.createMergeCommitIfNecessaryDesc=仅在目标分支无法快进合并源分支时才创建合并提交。
MergeStrategy.squashDesc=将源分支的所有提交压缩为目标分支中的一个提交。也就是说所有源分支上的提交（无论有多少个）都会被压缩成一个单一的提交记录，该提交会被添加到目标分支，而源分支的提交历史不会被保留。这种方式有助于清理提交历史，尤其是在开发过程中有很多小的提交或修复时。通过“压缩”，最终的历史看起来更简洁。
MergeStrategy.rebaseDesc=将源分支的所有提交变基到目标分支。也就是说源分支的提交会被重新应用在目标分支的最新提交之后，仿佛它们是从目标分支开始的。这种操作会“重写”源分支的提交历史，创建一个新的提交记录，从而将源分支的更改应用到目标分支上。变基操作使得提交历史更加线性，避免了合并提交和分叉，使得系统的提交历史看起来更清晰。适用于那些希望保持干净、直线的历史记录的情况。
UnreviewedChangesPanel.label1=自您上次评审后，已有
UnreviewedChangesPanel.label2=新的更改
UnreviewedChangesPanel.label3=。
ConflictResolveInstructionPanel.label1=在项目的工作目录中
ConflictResolveInstructionPanel.label2=检出源分支并与目标分支合并
ConflictResolveInstructionPanel.getFetch=git fetch origin
ConflictResolveInstructionPanel.getMerge=git merge 
ConflictResolveInstructionPanel.origin=origin/
ConflictResolveInstructionPanel.gitPull=git pull 
ConflictResolveInstructionPanel.gitPullRebase=git pull --rebase 
ConflictResolveInstructionPanel.gitRebase=git rebase origin/
ConflictResolveInstructionPanel.gitCheckout=git checkout 
ConflictResolveInstructionPanel.gitPushOrigin=git push origin 
ConflictResolveInstructionPanel.gitPushOriginF=git push -f origin 
ConflictResolveInstructionPanel.resolveMergeConflicts=解决合并冲突，测试更改后，将提交推送到服务器
ConflictResolveInstructionPanel.resolveRebaseConflicts=解决变基冲突，测试更改后，强制推送提交到服务器
ConflictResolveInstructionPanel.fromWorking=在项目的工作目录中 
ConflictResolveInstructionPanel.chekcout=检出源分支并变基到目标分支
PullRequestDetailPage.moreInfo=更多信息
PullRequestDetailPage.checkout=检出到本地
PullRequestDetailPage.discarded=该拉取请求已被丢弃
PullRequestDetailPage.fastForward=目标分支已快进(fast-foward)到源分支
PullRequestDetailPage.merged=提交已合并到目标分支
PullRequestDetailPage.merged2=提交已在该拉取请求之外合并到目标分支
PullRequestDetailPage.squashed=提交已被压缩(squash)为目标分支中的单个提交
PullRequestDetailPage.rebased=提交已变基到目标分支上
PullRequestDetailPage.sourceBranchIsOutdated=源分支已失效
PullRequestDetailPage.sourceBranchIsOutdated.showChanges=显示更改
PullRequestDetailPage.conflicts=存在合并冲突。
PullRequestDetailPage.reviewRequiredForThisChange=此次更改需要审核，请提交拉取请求
PullRequestDetailPage.needToBeVerifiedBySomeJobs=此次更改需要通过某些任务验证，请提交拉取请求
PullRequestDetailPage.noValidSignature=目标分支的最新提交没有有效签名
PullRequestDetailPage.noGpgSigningKeySpecified=需要提交签名，但未指定 GPG 签名密钥
PullRequestDetailPage.errorValidatingCommitMessageOf=验证提交信息 '${errorName}' 时出错：${errorMsg}
PullRequestDetailPage.mergeBranchInto=将分支 '${target}' 合并到分支 '${source}'
PullRequestDetailPage.mergeBranchIntoWithProject=将项目 '${projectPath}' 的分支 '${target}' 合并到分支 '${source}'
PullRequestDetailPage.pleaseFollow=请按照
PullRequestDetailPage.thisInstruction=这些提示
PullRequestDetailPage.toResolve=来解决冲突
PullRequestDetailPage.prCanNotBeMerged=目前还无法合并此请求，因为最新提交需要有效签名
PullRequestDetailPage.prCanNotBeMerged2=目前还无法合并此请求，因为
PullRequestDetailPage.prCanNotBeMerged3=目前还无法合并此请求，因为
PullRequestDetailPage.prCanNotBeMerged4=目前还无法合并此请求，因为
PullRequestDetailPage.someRequiredBuilds=某些所需的构建
PullRequestDetailPage.notSuccess=尚未成功
PullRequestDetailPage.notFinished=尚未完成
PullRequestDetailPage.runThisJob=运行此构建任务
PullRequestDetailPage.prCanOnlymergedBy=只有具有代码写入权限的用户才能合并此拉取请求
PullRequestDetailPage.pendingReview=待评审
PullRequestDetailPage.actions.merge=合并
PullRequestDetailPage.actions.discard=丢弃
PullRequestDetailPage.actions.approve=批准
PullRequestDetailPage.actions.rc=请求修改
PullRequestDetailPage.actions.reopen=重新开启
PullRequestDetailPage.actions.updateSourceBranch=更新源分支
PullRequestDetailPage.actions.delete=删除源分支
PullRequestDetailPage.actions.restore=恢复源分支
PullRequestDetailPage.submitter=提交人
PullRequestDetailPage.change=更改
PullRequestDetailPage.hiddenJobs=由于权限策略，有些构建任务被隐藏
PullRequestDetailPage.jobsMarkedPrefix=标记为
PullRequestDetailPage.jobsMarkedSuffix=的构建任务需运行成功
PullRequestDetailPage.assigneesToExpected=期望由谁合并此拉取请求
PullRequestDetailPage.mergeStrategy=合并策略
PullRequestDetailPage.sync=同步
PullRequestDetailPage.syncDesc=如果拉取请求状态与基础仓库不同步，您可以在此手动同步它们
PullRequestDetailPage.checkoutPrHead=检出拉取请求的最新提交，以便查看、调试或修改PR原始代码
PullRequestDetailPage.gitFetchOrigin=git fetch origin 
PullRequestDetailPage.gitCheckout=git checkout FETCH_HEAD 
PullRequestDetailPage.prMergePreview=检出拉取请求的合并预览，以便预览合并结果，检查冲突和问题
PullRequestDetailPage.error.commitMessagePrefix=非法的提交消息:
PullRequestDetailPage.error.autoMergeCommitMessagePrefix=非法的自动合并提交消息
PullRequestDetailPage.buildRequired.succOnMerge=要合并提交，构建任务必须是执行成功的: 
PullRequestDetailPage.buildRequired.succ=构建任务必须是运行成功的: 
PullRequestDetailPage.tabs.activities=动态
PullRequestDetailPage.changeTargetBranchConfirm=你确定要将目标分支更改为'${branch}'吗？
PullRequestDetailPage.unknown=未知
PullRequestDetailPage.prSyncSubmitted=同步拉取请求已接受
PullRequestDetailPage.prDeleteSuccess=拉取请求#${number}删除成功
PullRequestDetailPage.prDeleteConfirm=你确定要删除这个拉取请求吗？
PullRequestDetailPage.ops.merge=合并
PullRequestDetailPage.ops.mergeModalTitle=将目标分支合并到源分支
PullRequestDetailPage.ops.updateSuccess=源分支更新成功
PullRequestDetailPage.ops.updateFail=目标分支或源分支已更新，请重试
PullRequestDetailPage.ops.rebase=变基
PullRequestDetailPage.ops.rebaseConfirm=您正在将源分支变基到目标分支之上
PullRequestDetailPage.ops.approved=已批准
PullRequestDetailPage.ops.requestedForChanges=已请求更改
PullRequestDetailPage.ops.deletedSourceBranch=已删除源分支
PullRequestDetailPage.ops.restoredSourceBranch=已恢复源分支
PullRequestDetailPage.canNotPerform=无法执行该操作
PullRequestDetailPage.autoMergeLabel=自动合并
PullRequestDetailPage.autoMergeEnableDesc1=一旦准备就绪，拉取请求将使用预设的
PullRequestDetailPage.autoMergeEnableDesc2=提交消息
PullRequestDetailPage.autoMergeEnableDesc3=自动合并。添加新提交、更改合并策略或切换目标分支时，此选项将被禁用。
PullRequestDetailPage.autoMergeEnableDescRebase=一旦准备就绪，拉取请求将自动合并。添加新提交、更改合并策略或切换目标分支时，此选项将被禁用。
PullRequestDetailPage.presetCommitMessage=预设的提交消息
PullRequestDetailPage.presetCommitMessageUpdated=预设的提交消息已更新
PullRequestDetailPage.autoMergeOn=打开
PullRequestDetailPage.autoMergeOff=关闭
PullRequestDetailPage.autoMergeDisableDesc=启用此选项后，一旦准备就绪(所有评审人都批准了，所有必要的任务都通过了等等)，将自动合并拉取请求。
MergeConfirmPanel.commitMessage.mergesPullRequestPrefix=合并(pull request #%s)
MergeConfirmPanel.confirmDescription.rebase=源分支的提交将被变基到目标分支
MergeConfirmPanel.confirmDescription.fastForward=源分支的提交将被快进合并到目标分支
CommitMessageBean.commitMessage.name=提交消息
Reference.type.pr=拉取请求
Reference.type.build=构建
Reference.type.issue=问题
EntityReferencePanel.title=引用
EntityReferencePanel.help=您可以通过下面的文字在markdown或提交消息中引用该${type}。
EntityReferencePanel.helpProjectPath=如果引用来自当前项目，则项目路径可以省略
EntityWatchesPanel.andMore=和更多
EntityWatchesPanel.watchersLabel=关注者(${count})
OperationConfirmPanel.newCommitsWarn=在该拉取请求中有<a href='${url}'>新的提交</a>了
EntityNavPanel.entityName.pullRequest=拉取请求
EntityNavPanel.entityCountLabel=第${offset}个${entityName}，共${total}个
EntityNavPanel.prevTitle=前一个${entityName}
EntityNavPanel.nextTitle=后一个${entityName}
EntityNavPanel.noMore=没有${entityName}了
PullRequestChangePanel.changesSince=自该操作执行后的变化
PullRequestUpdatedPanel.tooManyCommits=提交过多，仅显示最近的${count}行
PullRequestUpdatedPanel.thisCommitIsRebased=该提交已经被变基
PullRequestActivitiesPage.changesSinceThisComment=自上次访问后的变化
PullRequestActivitiesPage.toggleComments=隐藏/显示评论
PullRequestActivitiesPage.toggleCommits=隐藏/显示提交
PullRequestActivitiesPage.toggleChangeHistory=隐藏/显示变更历史
BlobTextDiffPanel.showMoreLines=显示更多行
BlobTextDiffPanel.skippedLines=${linesNum}行被折叠
BlobTextDiffPanel.spoofingRisk=检测到该行存在 Unicode 同形字符混淆风险
ProjectIssueListPage.copyTitleAndNumber=复制问题编号和标题
ProjectIssueListPage.unPin=取消置顶此问题
EstimatedTimeEditBean.name=编辑预估时间
EstimatedTimeEditBean.estimatedTime.desc=仅为<b class='text-warning'>此问题</b>指定预估时间，不包括'%s'
EstimatedTimeEditBean.estimatedTime.placeholder=请输入预估时间
IssueProgressPanel.estimatedTitle=预估/花费时间。点击查看详情
IssueProgressPanel.stopWork=停止工作
QueriedIssuesProgressPanel.estimatedTime=总预估时间：
QueriedIssuesProgressPanel.spentTime=总花费时间：
QueriedIssuesProgressPanel.noValidQuery=查询错误，无法显示进度
TimingDetailPanel.h6=预估时间
TimingDetailPanel.total=总计：
TimingDetailPanel.own=自身：
TimingDetailPanel.aggregated=汇总自
TimingDetailPanel.spentTime=花费时间
TimingDetailPanel.logWork=记录工作
TimingDetailPanel.startWork=开始工作
TimingDetailPanel.estimatedTimeNoAggr=预估时间：
TimingDetailPanel.spentTimeNoAggr=花费时间：
IssueListPanel.queryHint=查询或排序问题
IssueListPanel.createNewIssue=创建新问题
IssueListPanel.createIssueSuccess=创建成功
IssueListPanel.fieldAndLinks=字段与关联
IssueListPanel.timing=时间进度
IssueListPanel.tips=默认情况下，父项目和子项目的问题都会列出。使用查询 <code>&quot;Project&quot; is current</code> 仅显示属于此项目的问题
IssueListPanel.pin=置顶此问题
IssueListPanel.votes=投票
IssueListPanel.expandIssueDetail=展开问题详情
CreateEstimatedTimeEditBean.estimatedTime.name=预估时间
CreateEstimatedTimeEditBean.estimatedTime.desc=选填，指定预估时间。
IssueListPanel.selectIssueToSync=请选择要同步预估/花费时间的问题
IssueListPanel.requestedSuccess=同步预估/花费时间的请求已发送
IssueListPanel.selectIssueToEdit=请选择要编辑的问题
IssueListPanel.selectIssueToMove=请选择要移动的问题
IssueListPanel.selectIssueToCopy=请选择要复制的问题
IssueListPanel.selectIssueToDelete=请选择要删除的问题
IssueListPanel.noIssuesToSync=没有要同步预估/花费时间的问题
IssueListPanel.noIssuesToEdit=没有要编辑的问题
IssueListPanel.noIssuesToMove=没有要移动的问题
IssueListPanel.noIssuesToCopy=没有要复制的问题
IssueListPanel.noIssuesToDelete=没有要删除的问题
IssueListPanel.noIssuesToWatchUnwatch=没有要关注/取消关注的问题
IssueListPanel.noIssuesToRead=没有要标记为已读的问题
IssueListPanel.watchStatusChanged=关注状态已更改
IssueListPanel.issuesMoved=问题已移动
IssueListPanel.issuesMoveConfirm=请在下方输入 <code>yes</code> 以将选定的问题移动到项目 '${targetProject}'
IssueListPanel.allQueriedIssuesMoveConfirm=请在下方输入 <code>yes</code> 以将所有查询到的问题移动到项目 '${targetProject}'
IssueListPanel.issuesCopied=问题已复制
IssueListPanel.issuesCopyConfirm=请在下方输入 <code>yes</code> 以将选定的问题复制到项目 '${targetProject}'
IssueListPanel.allQueriedIssuesCopyConfirm=在下方输入 <code>yes</code> 以将所有查询到的问题复制到项目 '${targetProject}'
IssueListPanel.issuesDeleteConfirm=请在下方输入 <code>yes</code> 以删除选定的问题
IssueListPanel.allQueriedIssuesDeleteConfirm=请在下方输入 <code>yes</code> 以删除所有查询到的问题
IssueListPanel.foundIssues=查到${count}个问题
IssueListPanel.exportAllQueriedTo=导出
IssueListPanel.noIssuesToExport=没有要导出的问题
IssueListPanel.header.number=编号
IssueListPanel.header.title=标题
IssueListPanel.header.iteration=迭代
IssueListPanel.header.state=状态
IssueListPanel.header.estimatedTime=预估时间
IssueListPanel.header.spentTime=耗费时间
FieldsAndLinksBean.fields.name=显示字段
FieldsAndLinksBean.fields.desc=指定要在问题列表中显示的字段
FieldsAndLinksBean.fields.placeholder=不显示任何字段
FieldsAndLinksBean.links.name=显示关联问题
FieldsAndLinksBean.links.desc=指定要在问题列表中显示的关联问题
FieldsAndLinksBean.links.placeholder=不显示任何关联问题
BatchEditPanel.desc=<b>注意：</b> 批量编辑问题不会使其他问题的状态转换，即使转换规则匹配。
BatchEditPanel.filesToChange=要更改的字段
BatchEditPanel.state=状态
BatchEditPanel.confidential=私密
BatchEditPanel.iteration=迭代
BatchEditPanel.sendNotifications=发送通知
BatchEditPanel.sendToWatchers=是否将此次更改的通知发送给问题关注者
BatchEditPanel.batchEditingLabel=批量编辑${issuesCount}个问题
BuiltInFieldsBean.state=状态
BuiltInFieldsBean.confidential=私密
BuiltInFieldsBean.iterations=迭代
PullRequestChangesPage.prevCommitTitle=前一个提交
PullRequestChangesPage.nextCommitTitle=后一个提交
PullRequestChangesPage.youAreReviewASubset=您正在查看的只是所有更改的一部分。
PullRequestChangesPage.showAllChanges=显示所有更改
PullRequestChangesPage.allChanges=所有更改
PullRequestChangesPage.changesSinceLastReview=自上次评审后的更改
PullRequestChangesPage.clickToSelectACommit=单击选择一个提交，或按住Shift键再单击以选择多个提交。
PullRequestChangesPage.base=基础
PullRequestChangesPage.head=最新
PullRequestActivitiesPage.loginToComment=请登录后再评论
IssueActivitiesPanel.toggleWorkLog=隐藏/显示工作日志
TransitionMenuLink.couldNotTransitLabel=<div class='px-3 py-2'><i>无权限或没有适用的状态转换规则</i></div>
IssueSidePanel.removeIssueFromIteration=将此问题从当前迭代中删除
IssueSidePanel.issueVotes=问题投票
IssueSidePanel.externalParticipants=外部参与者
IssueSidePanel.removeExternalParticipantsTitle=从问题中移除此外部参与者
IssueSidePanel.externalParticipantsDesc=外部参与者没有账户，通过电子邮件参与问题
IssuePrimaryPanel.unlink=解除关联
IssuePrimaryPanel.selectExisting=选择现有
IssuePrimaryPanel.createNew=创建新的
IssuePrimaryPanel.addLinkTitle=添加关联 ${linkName}
IssuePrimaryPanel.linkError.canNotLinktoSelf=不能关联自己
IssuePrimaryPanel.linkError.alreadyLinked=${specName}已经关联了一个问题。请先取消关联
IssuePrimaryPanel.linkError.alreadyLinked2=该问题已经关联过了
IssuePrimaryPanel.removeLinkConfirm=您确定要移除此关联吗？
IssuePrimaryPanel.addIssueLink=继续关联
IssueSidePanel.removeIssueFromIterationConfirm=您确定要将此问题从迭代'${iteration}'中移除吗？
IssueSidePanel.addToIterationPlaceholder=添加到迭代...
IssueSidePanel.loginToVote=先登录后投票
IssueSidePanel.unvote=取消投票
IssueSidePanel.vote=投票
IssueSidePanel.notAbleToparticipate=${name}将无法参与此问题，您要继续吗？
IterationCrumbPanel.noPermission=<div class='px-3 py-2'><i>无权限安排问题</i></div>
IterationCrumbPanel.goToIterationDetail=转到迭代详情
FieldValuesPanel.uneditReason.noPermission=无权限编辑字段
FieldValuesPanel.uneditReason.specNotFound=找不到字段的定义
FieldValuesPanel.uneditReason.needsReconciled=问题需要核对
FieldValuesPanel.dependentFieldsTitle=相关字段
FieldValuesPanel.notFoundWithI=<i>未找到</i>
IssueEditableTitlePanel.editTitle=编辑问题标题
IssueEditableTitlePanel.title=标题
IssueEditableTitlePanel.similarIssues=相似问题
CreateIssuePanel.title=新建问题
CreateIssuePanel.continueToAdd=继续创建
IssueStateBadge.stateTitle=状态
NewIssueEditor.switchdescriptionTemplate=有新的的问题描述模板，您要放弃当前内容并切换吗？
IssueChoice.chooseIssue.placeholder=选择问题...
IssueAddChoice.addIssue.placeholder=添加问题...
IssueAuthorizationsPanel.desc=指定除角色授予的用户外，能够访问此私密问题的额外用户。问题中提到的用户将自动被授权
IssueAuthorizationsPanel.unauthToThisUser=取消对此用户的授权
IssueAuthorizationsPanel.authUser.placeholder=授权用户...
IssueAuthorizationsPanel.authorizeSuccess=用户授权成功
IssueAuthorizationsPanel.authorizeUserSuccess=已取消用户'${name}'的授权
IssueAuthorizationsPanel.authorizeUserConfirm=您确定要取消用户'${name}'的授权吗？
IssueWorkPanel.logedWork=记录了工作
IssueChangeData.activities.removedComment=移除了评论于
IssueChangeData.activities.changedConfidential=更改了私密状态于
IssueChangeData.activities.changedDescription=更改了描述于
IssueChangeData.activities.changedFields=更改了字段于
IssueChangeData.activities.addedToIteration=加入了迭代'${iteration}'于
IssueChangeData.activities.changedIterations=更改了迭代于
IssueChangeData.activities.removedFromIteration=从迭代'${iteration}'中移除于
IssueChangeData.activities.addedToLink=添加了'${linkName}' '${linkedIssueNumber}'于
IssueChangeData.activities.removeedFromLink=移除了'${linkName}' '${linkedIssueNumber}'于
IssueChangeData.activities.changeOwnEstimatedTime=更改了个人预估时间于
IssueChangeData.activities.changeOwnSpentTime=更改了个人花费时间于
IssueChangeData.activities.moved=移动于
IssueChangeData.activities.referenceFromCodeComment=被其他代码评论引用于
IssueChangeData.activities.referenceFromOtherIssue=被其他问题引用于
IssueChangeData.activities.referenceFromPullRequest=被拉取请求引用于
IssueChangeData.activities.changedStateTo=更改状态为'${state}'于
IssueChangeData.activities.changedTitle=更改了标题于
IssueChangeData.activities.changedTotalEstimatedTime=更改了总预估时间于
IssueChangeData.activities.changeTotalSpentTime=更改了总花费时间于
IssueChangeData.activities.batchUpdated=批量编辑于
PullRequestChangeData.activities.approved=批准于
PullRequestChangeData.activities.enabledAutoMerge=打开了自动合并于
PullRequestChangeData.activities.disabledAutoMerge=关闭了自动合并于
PullRequestChangeData.activities.removedComment=移除了评论于
PullRequestChangeData.activities.changedDescription=更改了描述于
PullRequestChangeData.activities.discarded=丢弃于
PullRequestChangeData.activities.merged=合并于
PullRequestChangeData.activities.reopened=重新开启于
PullRequestChangeData.activities.requestedForChanges=请求更改于
PullRequestChangeData.activities.changedMergeStrategy=更改合并策略于
PullRequestChangeData.activities.referencedFromIssue=被其他问题引用于
PullRequestChangeData.activities.referencedFromOtherPullRequest=被其他拉取请求引用于
PullRequestChangeData.activities.deleteSourceBranch=删除了源分支于
PullRequestChangeData.activities.restoredSourceBranch=恢复了源分支于
PullRequestChangeData.activities.changedTargetBranch=更改了目标分支于
PullRequestChangeData.activities.changedTitle=更改了标题于
CodeCommentEvent.codeCommentCreated=添加了评论，
CodeCommentEvent.codeCommentDeleted=删除了评论，
CodeCommentEvent.codeCommentEdited=编辑了评论，
CodeCommentEvent.codeCommentReplyCreated=回复了评论，
CodeCommentEvent.codeCommentReplyDeleted=删除了回复，
CodeCommentEvent.codeCommentReplyEdited=编辑了回复，
CodeCommentEvent.codeCommentsDeleted=删除了评论，
CodeCommentEvent.codeCommentStatusResolved=解决了争议，
CodeCommentEvent.codeCommentStatusUnresolved=未解决争议，
CodeCommentEvent.touched=简单操作，
IssueEvent.issueCommentCreated=评论于
IssueEvent.issueCommentEdited=编辑评论于
IssueEvent.issueCommitAttached=提交附加于
IssueEvent.issueDeleted=删除于
IssueEvent.issuesDeleted=删除于
IssueEvent.issueOpened=开启于
IssueEvent.issueCopied=复制于
IssueEvent.issueImported=导入于
IssueEvent.issueMoved=移动于
IssueEvent.issueTouched=简单操作于
PackEvent.packPublished=发布于
PullRequestEvent.buildCommitUpdted=构建提交更新于
PullRequestEvent.checkFailed=检查失败于
PullRequestEvent.createCodeComment=添加了代码评论于
PullRequestEvent.repliedCodeComment=回复了代码评论于
PullRequestEvent.resolvedCodeComment=解决了代码评论的争议于
PullRequestEvent.unresolvedCodeComment=未解决解决代码评论的争议于
PullRequestEvent.createdComment=评论于
PullRequestEvent.editedComment=编辑了评论于
PullRequestEvent.pullRequestDeleted=删除了拉取请求于
PullRequestEvent.pullRequestsDeleted=删除了拉取请求于
PullRequestEvent.mergePrivewUpdated=合并了预览更新于
PullRequestEvent.pullRequestOpened=开启于
PullRequestEvent.pullRequestUpdated=添加了提交于
PullRequestEvent.assigned=指定了负责人于
PullRequestEvent.touched=简单操作于
PullRequestEvent.unassigned=取消了负责人于
PullRequestEvent.removedReviewer=移除了评审者于
PullRequestEvent.requestedReview=发送了评审请求于
PullRequestBuildEvent.activity.withVersion=构建 #%s (%s) 的状态为 %s
PullRequestBuildEvent.activity.withoutVersion=构建 #%s 的状态为 %s
ActivityDetail.prevValue=更改前
ActivityDetail.currentValue=当前
ActivityDetail.empty=空
NewIssueEditor.chooseIterations.pleaseChoose=请选择迭代...
BasePage.performNotAllow=您无权执行该操作
IssueDetailPage.activities=动态
IssueDetailPage.fixingCommits=修复提交
IssueDetailPage.fixingBuilds=修复构建
IssueDetailPage.authorizations=授权
IssueDetailPage.deleteSuccess=问题#${number}删除成功
IssueDetailPage.deleteConfirm=确定要删除该问题吗？
EntityNavPanel.entityName.issue=问题
IssueWorkPanel.deleteWorkConfirm=确定要删除吗?
IssuePrimaryPanel.onbehalfOf=源发起人 <b>${behalf}</b>
IssuePrimaryPanel.linkIssuesButton=关联其他问题
StateStatsBar.tipsTitle=${count}个状态为${state}的问题
StateStatsBar.noIssueInIteration=该迭代中没有一个问题
TransitionOptionPanel.stateTransitionTitle=问题状态转换(${from} -> ${to})
IncompatibilitiesPage.warning=自升级到该版本后，出现了不兼容的地方
IncompatibilitiesPage.warningTips=你也可以稍后通过点击帮助/不兼容的地方链接找到此页面
IncompatibilitiesPage.goback=返回
ResourceListPage.title=所有RESTful资源
ResourceListPage.notice=如果禁用了匿名访问，或者匿名用户对某项资源操作没有足够的权限，则需要通过 HTTP Basic Auth 头提供用户名和密码（或访问令牌）进行身份验证
ResourceListPage.resouceLabel=资源
ResourceListPage.endpointLabel=访问端点
ResourceDetailPage.operationLabel=操作
ResourceDetailPage.httpMethodLabel=HTTP方法
MethodDetailPage.placeholderLabel=占位符
MethodDetailPage.exampleLabel=示例
MethodDetailPage.queryParams=查询参数
MethodDetailPage.param=参数
MethodDetailPage.required=必填
MethodDetailPage.response=响应
MethodDetailPage.opsSuccess=操作成功
MethodDetailPage.opsFailed=操作失败
MethodDetailPage.statusCode=状态码
MethodDetailPage.responseBody=响应体
MethodDetailPage.responseErrorDesc=如果状态码不为200，就表明本次请求失败
MethodDetailPage.errorDetailConetentTypeDesc=注意错误详情的content type为text/plain
MethodDetailPage.curlExample=cURL示例
ProjectImportPage.dryRun=演练一下
UrlProjectImporter.title=指定从哪个URL进行导入
ImportServer.url.name=URL
ImportServer.url.desc=指定远程git仓库的URL。只支持http/https协议
ImportServer.ra.name=需要认证
ImportServer.project.name=项目
ImportServer.project.desc=指定要导入到DevGrip的哪个项目中
ImportServer.import.starting=正在从'%s'导入到'%s'...
ImportServer.import.cloneCode=正在克隆代码...
ImportServer.import.success=项目导入成功
ImportServer.import.projectAlreadyHasCode=已忽略，因为该项目已经有代码了
ImportServer.importError.targetAlreadyExists=导入目标已存在，你需要具备项目管理权限才能操作它。
ImportServer.importError.onlyHttpSupported=只支持http(s)协议
ProjectImportPage.topTitle=项目导入
GitHubProjectImporter.title.step1=GitHub认证
GitHubProjectImporter.title.step2=指定仓库
GitHubProjectImporter.title.step3=指定其他选项
GitHubImportServer.apiurl.name=GitHub API URL
GitHubImportServer.apiurl.desc=指定GitHub API url，例如 <tt>https://api.github.com</tt>
GitHubImportServer.gpat.name=GitHub个人访问令牌(Access Token)
GitHubImportServer.gpat.desc=生成GitHub个人访问令牌时，scope应为<b>repo</b>和<b>read:org</b>
GitHubImportServer.error.listingOrg=查询组织列表错误
GitHubImportServer.error.dupIssueFieldMapping=重复的问题字段映射(问题: %s, 字段: %s)
GitHubImportServer.issueImportedCount=成功导入了%s个问题
GitHubImportServer.error.rateLimit=已限流，请等待...
GitHubImportServer.error.httpFailedWithMsg=HTTP请求失败(url: %s, 状态码: %d, 错误信息: %s)
GitHubImportServer.error.notAGithubUrl=这可能不是GitHub API URL
GitHubImportServer.error.authFailed=认证失败
GitHubImportServer.error.connectFailed=连接API服务时出错
GitHubImportServer.starting=正在从'%s'导入到'%s'...
GitHubImportServer.error.httpFailed=Http请求失败(status: %s)
GitHubImportServer.import.projectAlreadyHasCode=忽略代码克隆，因为该项目已经有代码了
GitHubImportServer.import.milestones=正在导入里程碑数据...
GitHubImportServer.import.issues=正在导入问题数据...
GitHubImportServer.import.success=仓库导入成功
GitLabProjectImporter.title.step1=GitLab认证
GitLabProjectImporter.title.step2=指定仓库
GitLabProjectImporter.title.step3=指定其他导入选项
GitLabImportServer.apiurl.name=GitLab API URL
GitLabImportServer.apiurl.desc=指定GitLab API url ，例如 <tt>https://gitlab.example.com/api/v4</tt>
GitLabImportServer.gpat.name=GitLab个人访问令牌(Access Token)
GitLabImportServer.gpat.desc=GitLab个人访问令牌的scope应为 <b>read_api</b>、<b>read_user</b> 和 <b>read_repository</b>。请注意，仅列出指定访问令牌用户拥有的组/项目
GitLabImportServer.error.notAGitLabUrl=这可能不是GitLab api url
GitLabImportServer.starting=正在从'%s'导入到'%s'...
GitLabImportServer.import.projectAlreadyHasCode=忽略代码克隆，因为该项目已经有代码了
GitLabImportServer.import.milestones=正在导入里程碑数据...
GitLabImportServer.import.issues=正在导入问题数据...
GitLabImportServer.import.success=仓库导入成功
JiracloudProjectImporter.title.step1=JIRA cloud 认证
JiracloudImportServer.apiurl.name=您的JIRA cloud实例的API url
JiracloudImportServer.apiurl.desc=您的JIRA cloud实例的API url, 例如 <tt>https://your-domain.atlassian.net/rest/api/3</tt>
JiracloudImportServer.accountEmail.name=账户email
JiracloudImportServer.apiToken.name=Api Token
IssueImporter.import.success=问题导入成功
JiracloudImportServer.error.notAJiraCloudUrl=这可能不是Jira cloud api url
JiracloudImportServer.error.download=下载附件出错(url: %s, 错误信息: %s)
EmptyWithI=<i>空</i>
ImportOrganization.org.desc=请选择要从哪个Github组织下导入，如果为空，将从当前账户下的仓库导入
ImportOrganization.includeForks.name=包含Fork的仓库
ImportOrganization.includeForks.desc=是否包含Fork的仓库
ImportRepositories.parentDevgripProject.name=在DevGrip侧的父项目
ImportRepositories.importAll.name=导入所有仓库
ImportRepositories.includeForks.name=包含Fork的仓库
ImportRepositories.includeForks.desc=是否导入GitHub的Fork仓库
ImportRepositories.parentDevgripProject.desc=您可以选择指定一个DevGrip项目作为导入仓库的父项目。如果为空，则作为根项目导入
ImportRepositories.repoToImport.name=要导入的GitHub仓库
ImportRepository.repo.name=GitHub仓库
ImportRepository.repo.desc=选择要导入哪个仓库
IssueImportOption.closedIssueState.name=已关闭问题的状态
IssueImportOption.closedIssueState.desc=指定问题状态用于GitHub的已关闭问题。<br><b>注意：</b>如果没有合适的选项您可以自定义DevGrip问题状态
IssueImportOption.assigneesIssueField.name=负责人字段
IssueImportOption.assigneesIssueField.desc=指定一个多值用户字段以存储负责人信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义DevGrip问题字段
IssueImportOption.issueLabelMappings.name=问题标签映射
IssueImportOption.issueLabelMappings.desc=指定如何将GitHub问题分类标签映射到DevGrip自定义字段。<br><b>注意：</b>如果没有合适的选项，您可以自定义DevGrip问题字段
IssueLabelMapping.githubIssueLabel.name=GitHub问题分类标签
IssueLabelMapping.devgripIssueField.name=DevGrip问题字段
IssueLabelMapping.devgripIssueField.desc=指定一个枚举类型的自定义字段
ProjectImportOption.publicRole.name=默认角色
ProjectImportOption.publicRole.desc=如果指定，所有从GitHub导入的公开仓库将使用该角色作为默认角色。私有仓库不受影响
ProjectImportOption.importIssues.name=导入问题
IssueImporter.title.step2=选择仓库
IssueImporter.title.chooseProject=选择项目
IssueImporter.title.step3=指定导入选项
IssueImporter.import.starting=正在从仓库 %s 导入问题...
ImportResult.andMore=和更多
ImportResult.note=<br><br><b>注意:</b><ul>
ImportResult.nonExistentIterations=不存在迭代
ImportResult.notMaps=%s问题标签无法映射到DevGrip自定义字段
ImportResult.accountCanNotBeMaps=%s账户没有公开电子邮件无法映射到DevGrip帐户
ImportResult.attachmentsAreNotImported=<li> 由于GitHub的限制，无法导入问题描述和评论中的附件
ImportGroup.groupId.name=GitLab 组
ImportGroup.groupId.desc=指定要导入的组。如果为空，将从当前账户下导入项目
ImportProject.project.name=GitLab 项目
ImportProject.project.desc=选择要导入的项目
ImportProjects.parentProject.name=父DevGrip项目
ImportProjects.parentProject.desc=可以选择指定一个DevGrip项目作为导入项目的父项目。如果为空，则作为根项目导入
ImportProjects.all.name=导入所有项目
ImportProjects.includeForks.name=包含Fork
ImportProjects.includeForks.desc=是否导入GitLab的Fork项目
ImportProjects.gitlabProjects.name=要导入的GitLab项目
ImportResult.tooLargeAttachments=附件过大
ImportResult.downloadFailed=下载附件失败
GitLabIssueImportOption.closedState.desc=指定用于GitLab已关闭问题的状态。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题状态
GitLabIssueImportOption.assigneesIssueField.desc=指定一个多值用户字段以存储指派人员信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
GitLabIssueImportOption.dueDateIssueField.name=到期日期字段
GitLabIssueImportOption.dueDateIssueField.desc=可以选择指定一个日期字段以存储到期日期信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
GitLabIssueImportOption.estimatedTimeField.name=预估时间字段
GitLabIssueImportOption.estimatedTimeField.desc=可以选择指定一个工作周期字段以存储预估时间信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
GitLabIssueImportOption.spentTimeField.name=已用时间字段
GitLabIssueImportOption.spentTimeField.desc=可以选择指定一个工作周期字段以存储已用时间信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
GitLabIssueImportOption.issueLabelMappings.desc=指定如何将 GitLab 问题标签映射到 DevGrip 自定义字段。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
GitLabIssueLabelMapping.issueLabel.name=GitLab问题标签
GitLabIssueLabelMapping.devgripIssueField.name=DevGrip问题字段
GitLabIssueLabelMapping.devgripIssueField.desc=指定一个枚举类型的自定义字段
GitLabProjectImportOption.publicRole.desc=如果指定，从 GitLab 导入的所有公开和内部项目将使用该角色作为默认角色。私有项目不受影响
JiraCloudImportOption.assigneeIssueField.name=负责人字段
JiraCloudImportOption.assigneeIssueField.desc=指定一个用户字段以存储负责人信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportOption.dueDateField.name=到期日期字段
JiraCloudImportOption.dueDateField.desc=可以选择指定一个日期字段以存储到期日期信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportOption.timeSpentField.name=已用时间字段
JiraCloudImportOption.timeSpentField.desc=可以选择指定一个工作周期字段以存储已用时间信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportOption.estimatedField.name=预估时间字段
JiraCloudImportOption.estimatedField.desc=可以选择指定一个工作周期字段以存储时间预估信息。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportOption.issueStatusMapping.name=问题状态映射
JiraCloudImportOption.issueStatusMapping.desc=指定如何将 JIRA 问题状态映射到 DevGrip 自定义字段。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题状态
JiraCloudImportOption.issueTypeMapping.name=问题类型映射
JiraCloudImportOption.issueTypeMapping.desc=指定如何将 JIRA 问题类型映射到 DevGrip 自定义字段。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportOption.issuePriorityMapping.name=问题优先级映射
JiraCloudImportOption.issuePriorityMapping.desc=指定如何将 JIRA 问题优先级映射到 DevGrip 自定义字段。<br><b>注意：</b>如果没有合适的选项，您可以自定义 DevGrip 问题字段
JiraCloudImportProject.project.name=JIRA 项目
JiraCloudImportProject.project.desc=选择要从哪个JIRA项目中导入问题
JiraCloudImportProjects.parentProject.name=父DevGrip项目
JiraCloudImportProjects.parentProject.desc=可以选择指定一个DevGri 项目作为导入项目的父项目。如果为空，则作为根项目导入
JiraCloudImportProjects.projectToImport.name=要导入的 JIRA 项目
JiraCloudImportProjects.error.validate=无权限作为根项目导入，请指定父项目
JiraCloudImportResult.error.customFieldsNotImported=<li> JIRA 问题自定义字段未导入
JiraCloudImportResult.error.statusNotMapped=JIRA 问题状态未映射到 DevGrip 自定义字段
JiraCloudImportResult.error.typesNotMapped=JIRA 问题类型未映射到 DevGrip 自定义字段
JiraCloudImportResult.error.prioritiesNotMapped=JIRA 问题优先级未映射到 DevGrip 自定义字段
JiraCloudImportResult.error.accountsNotAvailable=JIRA 账户在 DevGrip 中不可用（通过全名(full name)匹配）
IssuePriorityMapping.issuePriority.name=JIRA 问题优先级
IssuePriorityMapping.devgripIssueField.name=DevGrip 问题字段
IssuePriorityMapping.devgripIssueField.desc=指定一个枚举类型的自定义字段
IssueStatusMapping.issuePriority.name=JIRA 问题状态
IssueStatusMapping.issueState.name=DevGrip 问题状态
IssueTypeMapping.issueType.name=JIRA 问题类型
IssueTypeMapping.issueField.name=DevGrip 问题字段
IssueTypeMapping.issueField.desc=指定一个枚举类型的自定义字段
GiteaIssueImporter.stepAuth.title=Gitea 认证
GiteaIssueImporter.stepChooseRepo.title=选择仓库
GiteaIssueImporter.stepSpecifyImportOption.title=指定导入选项
GiteaIssueImporter.log.importing=正在从仓库 %s 导入问题 ...
GiteaImportServer.apiUrl.name=Gitea API URL
GiteaImportServer.apiUrl.desc=指定 Gitea API URL，例如 <tt>https://gitea.example.com/api/v1</tt>
GiteaImportServer.gpat.name=Gitea 个人访问令牌
GiteaImportServer.error.issueFieldDup=重复的问题字段映射（问题: %s, 字段: %s）
GiteaImportServer.error.fieldNotFound=找不到字段: %s
GiteaImportServer.logInfo.importedIssues=成功导入 %d 个问题
GiteaImportServer.logInfo.importingFrom=正在从 '%s' 导入到 '%s'...
GiteaImportServer.error.targetAlreadyExists=导入目标已存在。您需要具有项目管理权限才能继续操作
GiteaImportServer.logInfo.cloneCode=正在克隆代码...
GiteaImportServer.logWarn.skipCloneCode=跳过代码克隆，因为项目已包含代码
GiteaImportServer.logInfo.importingMilestones=正在导入里程碑...
GiteaImportServer.logInfo.importingIssues=正在导入问题...
GiteaImportServer.importedSuccess=仓库导入成功
GiteaImportServer.error.authFailed=身份认证失败
GiteaImportServer.error.connectingFailed=连接 API 服务时出错
GiteaProjectImporter.stepAuth.title=认证 Gitea
GiteaProjectImporter.stepSpecifyRepo.title=指定仓库
GiteaProjectImporter.stepSpecifyImportOption.title=指定导入选项
GiteaImportOrganization.org.name=Gitea 组织机构
GiteaImportOrganization.org.desc=选择要从中导入的组织机构。如果为空，将从当前账户下的仓库导入
GiteaImportOrganization.includeForks.name=包括分叉项目
GiteaImportOrganization.includeForks.desc=是否包括分叉的仓库
GiteaImportRepositories.parent.name=父级 DevGrip 项目
GiteaImportRepositories.parent.desc=选填，指定一个 DevGrip 项目作为导入仓库的父级。如果为空，将作为根项目导入
GiteaImportRepositories.importAll.name=导入所有仓库
GiteaImportRepositories.includeForks.name=包括分叉项目
GiteaImportRepositories.includeForks.desc=是否导入分叉的 Gitea 仓库
GiteaImportRepositories.reposToImport.name=要导入的 Gitea 仓库
GiteaImportRepository.repo.name=Gitea 仓库
GiteaImportRepository.repo.desc=选择要导入的仓库
GiteaImportResult.andMore=及更多
GiteaImportResult.notice=<br><br><b>注意：</b><ul>
GiteaImportResult.noExistsIterations=不存在的迭代
GiteaImportResult.fieldNotMapped=Gitea 问题标签未映射到 DevGrip 自定义字段
GiteaImportResult.accountNotMapped=Gitea 登录用户未提供邮箱或邮箱无法映射到 DevGrip 账户
GiteaImportResult.attachmentsAndCommentsAreNotImported=<li> 问题描述和评论中的附件无法导入，因为 Gitea 当前不提供附件 API
GiteaImportResult.issueDependenciesAreNotImported=<li> 问题依赖关联无法导入，因为 Gitea 不提供公开的 API 来访问这些信息
GiteaIssueImportOption.closedIssueState.name=已关闭问题状态
GiteaIssueImportOption.closedIssueState.desc=指定用于 Gitea 已关闭问题的状态。<br><b>注意：</b> 如果这里没有适当的选项，您可以自定义 DevGrip 的问题状态
GiteaIssueImportOption.assigneesIssueField.name=分配者问题字段
GiteaIssueImportOption.assigneesIssueField.desc=指定一个多值用户字段以存储分配者信息。<b>注意：</b> 如果这里没有适当的选项，您可以自定义 DevGrip 的问题字段
GiteaIssueImportOption.dueDateIssueField.name=截止日期问题字段
GiteaIssueImportOption.dueDateIssueField.desc=可选地指定一个日期字段以存储截止日期信息。<br><b>注意：</b> 如果这里没有适当的选项，您可以自定义 DevGrip 的问题字段
GiteaIssueImportOption.issueLabelMappings.name=截止日期问题字段
GiteaIssueImportOption.issueLabelMappings.desc=指定如何将 Gitea 的问题标签映射到 DevGrip 的自定义字段。<br><b>注意：</b> 如果这里没有适当的选项，您可以自定义 DevGrip 的问题字段
GiteaIssueLabelMapping.giteaIssueLabel.name=Gitea 问题标签
GiteaIssueLabelMapping.myIssueField.name=DevGrip 问题字段
GiteaIssueLabelMapping.myIssueField.desc=指定一个枚举类型的自定义字段
GiteaProjectImportOption.publicRole.name=公开角色
GiteaProjectImportOption.publicRole.desc=如果指定，则从 Gitea 导入的所有公共仓库将使用此角色作为默认角色。私有仓库不受影响
GiteaProjectImportOption.importIssues.name=导入问题
NewIssuePage.topTile=<span class='text-nowrap'>创建问题</span>
IssueBoardsPage.board=看板
IssueBoardsPage.backlogTitle=显示还未被安排迭代的问题
IssueBoardsPage.iteration=迭代
IssueBoardsPage.backlog=待办
IssueBoardsPage.noIssueBoard=没有问题看板
AddNew=新建
IssueBoardsPage.firstBoardTips=第一个看板将作为你的默认看板
IssueBoardsPage.defaultBadge=默认
IssueBoardsPage.addNewBoard=新建看板
IssueBoardsPage.useDefaultBoard=使用默认看板
IssueBoardsPage.burndownChart=燃尽图
IssueBoardsPage.inherited=继承的
IssueBoardsPage.showClosed=显示已关闭的迭代
IssueBoardsPage.hideClosed=隐藏已关闭的迭代
IssueBoardsPage.unscheduledIssues=未安排迭代的问题
IssueBoardsPage.allIssues=所有问题
IssueBoardsPage.createIteration=创建迭代
IssueBoardsPage.close=关闭
IssueBoardsPage.reopen=重新开启
IssueBoardsPage.useDefaultConfirm=这将丢弃所有项目的看板，是否继续？
IssueBoardsPage.deleteBoardConfirm=确定要删除此看板吗？
IssueBoardsPage.unscheduled=<i>未安排迭代的问题</i>
IssueBoardsPage.iterationDue=迭代已到期
IssueBoardsPage.iterationCloseSuccess=迭代'${name}'关闭成功
IssueBoardsPage.iterationReopenSuccess=迭代'${name}'重新开启成功
IssueBoardsPage.iterationDeleteSuccess=迭代'${name}'删除成功
IssueBoardsPage.editIteration=编辑迭代
IssueBoardsPage.deleteIterationConfirm=确定要删除此迭代吗？
IssueBoardsPage.createIterationModalTitle=创建迭代
IssueBoardsPage.backlogFilter.placeholder=筛选待办问题
IssueBoardsPage.filter.placeholder=筛选问题
IssueBoardsPage.pageTitle=问题看板
IssueBoardsPage.errorParseQuery=解析%s查询时出错：
IssueBoardsPage.malformedQuery=格式错误的%s查询
IssueBoardsPage.errorParseBaseQuery=解析%s基础查询时出错：
IssueBoardsPage.malformedBaseQuery=格式错误的%s基础查询
IssueBoardsPage.queryMessageWithBacklog=待办
NewBoardPanel.newBoard=新看板
NewBoardPanel.error.nameAlreadyUsed=该名称已被使用
NewBoardPanel.createSuccess=新问题看板创建成功
CardDetailPanel.recentCommitsFixingTheIssue=此页面列出了最近修复此问题的提交
BoardColumnPanel.addNewBoardTitle=向此列添加新问题卡片
BoardColumnPanel.addAllCards=将所有问题卡片添加到指定迭代
BoardColumnPanel.showTotalSpentTime=显示总估算/花费时间
BoardColumnPanel.showIssues=以列表形式显示问题
BoardColumnPanel.noValueWithI=<i>无</i>
BoardCardPanel.showIssueDetails=点击查看问题详情
AddToIterationBean.name=将问题添加到迭代
AddToIterationBean.iteration.desc=请选择将问题安排到哪个迭代
AddToIterationBean.iteration.name=迭代
AddToIterationBean.sendNotification.desc=是否为此次更改向问题关注者发送通知
AddToIterationBean.sendNotification.name=发送通知
AddToIterationBean.removeFromCurrentIteration.name=从当前迭代移除
IterationEditPage.saveSuccess=迭代保存成功
IterationDetailPage.iteration=迭代
IterationDetailPage.burndown=燃尽图
NewIterationPage.createSuccess=新迭代创建成功
IterationBurndownPage.showStatesBy=请选择展示状态的维度
IterationListPanel.open=已开启
IterationListPanel.close=已关闭
IterationListPanel.sort=排序
IterationListPanel.dueDate=到期日期
IterationListPanel.issueStats=问题统计
IterationDateLabel.noStartOrNoDue=<i>无开始/到期日期</i>
IterationDateLabel.startDateSpan=<span title='开始日期'>
IterationDateLabel.dueDateSpan=<span title='到期日期'>
IterationBurndownPanel.error.daystooLong=迭代跨度太长，无法显示燃尽图
IterationBurndownPanel.error.startDateShouldBeforeDueDate=迭代的开始日期应早于到期日期
IterationBurndownPanel.error.startOrDueNotSpicified=需要指定迭代的开始和到期日期才能显示燃尽图
IterationBurndownPanel.error.avoidDup=为避免重复，此处显示的预估/剩余时间不包括从 '${name}' 汇总的数据
IterationActinosPanel.reopenTitle=重新开启此迭代
IterationActinosPanel.closeTitle=关闭此迭代
IterationActinosPanel.editTitle=编辑此迭代
IterationActinosPanel.deleteTitle=删除此迭代
IterationActinosPanel.reopenSuccess=迭代'${name}'重新开启成功
IterationActinosPanel.closeSuccess=迭代'${name}'关闭成功
IterationActinosPanel.deleteSuccess=迭代'${name}'删除成功
IterationActinosPanel.deleteConfirm=您确定要删除迭代'${name}'吗？
IterationSort.closestDueDate=最近到期日期
IterationSort.furthestDueDate=最远到期日期
IterationSort.name=名称
IterationSort.nameReversely=名称倒序
IterationEditBean.startDate.name=开始日期
IterationEditBean.dueDate.name=到期日期
IterationEditBean.error.namePrefixMustWith=名称的开头必须为:
IterationEditBean.error.nameAlreadyUsed=该名称已经被其他迭代使用
BurndownIndicators.issueCount=问题数量
BurndownIndicators.remainTime=剩余时间
BurndownIndicators.estimatedTime=预估时间
IterationStatusName.open=已开启
IterationStatusName.closed=已关闭
IterationBurndownPanel.guideLine=参考线
AvatarEditPage.title=当前图标
AvatarEditPage.toptitle=编辑头像
UserAuthorizationsPage.tips=当授权一个用户时，该用户将在所有子项目中具有相同角色
GroupAuthorizationsPage.tips=当授权一个用户组时，该用户组将在所有子项目中具有相同角色
UserAuthorizationBean.username.name=用户
UserAuthorizationsPage.submitError.unableToApplyChanges=无法应用此次更改，因为此次更改将导致您将无法管理该项目
UserAuthorizationsPage.submitError.dupAuth=发现重复的授权：
UserAuthorizationsPage.submitSuccess=用户授权已更新
UserAuthorizationsPage.topTitle=用户授权
GroupAuthorizationBean.groupname.name=用户组
GroupAuthorizationsPage.submitSuccess=用户组授权已更新
BranchProtectionsPage.tips=定义分支保护规则。如果父项目也定义了分支保护规则，那么当前项目的规则具有更高的优先级。对于指定的分支和用户，第一条匹配的规则将生效
BranchProtectionsPage.editRule=编辑规则
BranchProtectionsPage.addRule=添加规则
GroupAuthorizationsPage.topTitle=用户组授权
BranchProtectionPanel.deleteConfirm=您确定要删除此规则吗？
BranchProtectionPanel.editThisRuleTitle=编辑此规则
BranchProtectionPanel.deleteThisRuleTitle=删除此规则
BranchProtection.branches.name=分支
BranchProtection.branches.desc=指定要保护的分支，多个分支以空格分隔。使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的分支将被排除。
BranchProtection.users.name=适用于哪些用户
BranchProtection.users.desc=规则仅在修改分支(推送代码、合并、删除分支)的用户满足此处指定的条件时才会生效
BranchProtection.preventForcePush.name=禁止强制推送
BranchProtection.preventForcePush.desc=勾选此项以禁止强制推送
BranchProtection.preventBranchDeletion.name=禁止删除
BranchProtection.preventBranchDeletion.desc=勾选此项以禁止删除分支
BranchProtection.preventBranchCreation.name=禁止创建
BranchProtection.preventBranchCreation.desc=勾选此项以禁止创建分支
BranchProtection.requiredValidSign.name=提交签名校验
BranchProtection.requiredValidSign.desc=勾选此项后，分支的最新提交记录（头部提交）必须包含有效的签名
BranchProtection.enforceConventionalCommits.name=强制规范化提交
BranchProtection.enforceConventionalCommits.desc=勾选此项则要求遵循<a href='https://www.conventionalcommits.org' target='_blank'>规范化提交</a>。注意，这仅适用于非合并提交。
BranchProtection.commitTypes.name=提交类型
BranchProtection.commitTypes.placeholder=任意类型
BranchProtection.commitTypes.desc=选填，指定规范化提交的有效类型（按回车键添加值）。留空允许任意类型
BranchProtection.commitScopes.name=提交范围
BranchProtection.commitScopes.placeholder=任意范围
BranchProtection.commitScopes.desc=选填，指定规范化提交的有效范围（按回车键添加值）。留空允许任意范围
BranchProtection.checkCommitFooter.name=检查提交消息页脚
BranchProtection.commitFooterPattern.name=验证提交消息页脚的正则表达式
BranchProtection.commitFooterPattern.desc=用于验证提交消息页脚的<a href='https://docs.oracle.com/javase/8/docs/api/java/util/regex/Pattern.html'>Java正则表达式</a>
BranchProtection.commitTypesForFooterCheck.name=用于校验页脚的提交类型
BranchProtection.commitTypesForFooterCheck.desc=可选地指定适用于提交消息页脚检查的提交类型（按回车键添加值）。留空表示所有类型
BranchProtection.maxLength.name=提交消息最大长度
BranchProtection.maxLength.placeholder=无长度限制
BranchProtection.requiredReviewers.name=需要哪些人来评审
BranchProtection.requiredReviewers.placeholder=无
BranchProtection.requiredReviewers.desc=选填，指定更改特定分支时所需的评审人员
BranchProtection.requiredBuilds.name=必要的构建
BranchProtection.requiredBuilds.placeholder=没有要求
BranchProtection.requiredBuilds.desc=选填，选择必要的构建，在合并或推送代码前，确保所有构建任务（如编译、单元测试、集成测试等）都成功运行。防止将存在缺陷或未通过测试的代码提交到重要分支。
BranchProtection.fileProtections.name=文件保护
BranchProtection.fileProtections.desc=选填，指定路径保护规则
BranchProtection.requireStrictPrBuilds.name=严格的拉取请求构建
BranchProtection.requireStrictPrBuilds.desc=当拉取请求的目标分支有新提交时，将重新计算拉取请求的合并提交。此选项决定是否接受在之前的合并提交上运行的拉取请求构建。如果启用，您需要在新的合并提交上重新运行所需的构建。此设置仅在指定了必需的构建时生效
FileProtection.paths.name=路径
FileProtection.paths.desc=指定要保护的文件路径，多个用空格分隔。使用'**'，'*' 或 '?' 进行 <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配/a>。以'-'开头的将会被排除。
FileProtection.reviewers.name=评审人
FileProtection.reviewers.desc=若指定的路径或文件发生更改，您可以指定必要的评审人。请注意，用户提交更改时，自动被视为该更改的评审人。这意味着提交者不需要额外的步骤来评审他们自己的更改。
FileProtection.validateError=必须指定评审人或必要的构建任务
TagProtectionsPage.tips=定义标签保护规则。如果父项目也定义了标签保护规则，那么当前项目的规则具有更高的优先级。对于指定的标签和用户，第一条匹配的规则将生效。
BooleanEditSupport.value.true=是
BooleanEditSupport.value.false=否
UserMatchEditSupport.value.anyone=任何人
TagProtection.tags.name=标签
TagProtection.tags.desc=指定要保护的标签，多个以空格分隔。使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的标签将被排除。
TagProtection.users.desc=规则仅在操作分支(推送代码、合并、删除分支)的用户满足此处指定的条件时才会生效
TagProtection.preventUpdate.name=禁止更新
TagProtection.preventUpdate.desc=勾选此选项以阻止禁止更新
TagProtection.preventBranchDeletion.desc=勾选此选项以禁止删除标签
TagProtection.preventBranchCreation.desc=勾选此选项以禁止创建标签
GitPackConfigPage.tips=如果您的Git仓库非常大，您可能需要调整此处的选项以减少内存使用
GitPackConfig.windowMem.name=窗口内存
GitPackConfig.windowMem.desc=选填，为该仓库指定git配置项 <code>pack.windowMemory</code> 的值
GitPackConfig.packSizeLimit.name=包大小限制
GitPackConfig.packSizeLimit.desc=选填，为该仓库指定git配置项 <code>pack.packSizeLimit</code> 的值
GitPackConfig.threads.name=线程
GitPackConfig.threads.desc=选填，为该仓库指定git配置项 <code>pack.threads</code> 的值
GitPackConfig.window.name=窗口
GitPackConfig.window.desc=选填，为该仓库指定git配置项 <code>pack.window</code> 的值
GitPackConfigPage.updateSuccess=Git Pack设置已更新
PullRequestSettingPage.updateSuccess=拉取请求设置已更新
ProjectPullRequestSetting.defaultMergeStrategy.name=默认合并策略
ProjectPullRequestSetting.defaultMergeStrategy.rootPlaceholder=总是创建合并提交
ProjectPullRequestSetting.defaultMergeStrategy.desc=为拉取请求指定默认的合并策略
ProjectPullRequestSetting.defaultAssignee.name=默认合并负责人
ProjectPullRequestSetting.defaultAssignee.rootPlaceholder=无
ProjectPullRequestSetting.defaultAssignee.desc=为拉取请求指定默认的合并负责人，注意：只能选择具备该项目代码写(code write)权限的用户
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.name=合并后是否删除源分支
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.rootPlaceholder=否
ProjectPullRequestSetting.deleteSourceBranchAfterMerge.desc=启用此选项，拉取请求被自动合并后，源分支会被自动删除。当然前提是合并拉取请求的负责人具有删除的权限
ContributedProjectSettingPage.saveSuccess=设置保存成功
CodeAnalysisSettingPage.submitSuccess=代码分析设置已更新
CodeAnalysisSetting.files.name=要分析的文件
InheritFromParent=继承于父项目
CodeAnalysisSetting.files.rootPlaceholder=所有文件
CodeAnalysisSetting.files.desc=系统会分析仓库文件用于支持代码搜索、代码行统计和代码贡献统计。这个设置用于指定哪些文件会被分析，并且期望以空格分隔，具体如何指定路径请参考<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径匹配</a>。以'-'开头的路径会被排除，例如：<code>-**/doc/**</code>会排除路径中包含doc的所有文件。<b>注意: </b> 更改此设置仅影响新的提交。若要将此次更改应用到历史提交中，请停止服务器并删除<a href='https://docs.devgrip.net/concepts#project-storage' target='_blank'>项目存储路径</a>下的<code>index</code>和<code>info/commit</code>文件夹。重新启动服务器时，仓库将被重新分析。
ServiceDeskSettingsPage.submitError=该邮箱地址已被使用
ServiceDeskSettingsPage.submitSuccess=服务台设置已更新
JobSecretsPage.tips=<svg class='icon mr-2'><use xlink:href='%s'/></svg> 定义任务密钥，然后在构建中使用。主要作用：统一管理构建过程中需要使用的ssh密码，ftp密码，包仓库或镜像库密码，然后在构建中使用<code>@serets:your_secret_name@</code>来引用，这样就不必把敏感数据直接定义在构建的脚本中，提高了安全性。<br>不同密钥可以定义<b>相同名称</b>。如果名称相同, 将使用第一个被授权的同名密钥（优先在当前项目中查找，然后在父项目中查找）。请注意，密钥值如果包含换行符或少于<b>%d</b>个字符，将不会在构建日志中被隐藏。
HideArchived=隐藏已归档的
ShowArchived=显示已归档的
JobSecretsPage.deleteConfirm=确定要删除密钥'${name}'吗?
JobSecretsPage.deleteSuccess=密钥'${name}'删除成功
JobSecretsPage.archived=已归档
JobSecret.authorization.name=授权
JobSecret.authorization.placeholder=任何任务
JobSecret.authorization.desc=选填，指定允许访问此密钥的分支/用户/用户组。如果留空，任何构建任务都可以访问此密钥，包括通过外部拉取请求触发的构建任务。
JobSecret.archived.name=是否归档
JobSecret.archived.desc=如果某个密钥不再被当前构建使用，但仍需要保留以便重启旧的构建，就可以将其标记为已归档。已归档的密钥默认不会显示。
JobSecretEditPanel.editJobSecret=编辑任务密钥
SecretPropertyEditor.show=显示密钥
SecretPropertyEditor.hide=隐藏密钥
JobPropertiesPage.tips=定义属性，然后在构建中使用。主要作用：统一管理构建过程中需要的属性值、变量值，如镜像版本，镜像地址等，然后在构建中使用<code>@property:your_property_name@</code>来引用，减少重复定义，增加可维护性。<br><b>注意：</b>属性可以被子项目继承，也可以被子项目的同名属性覆盖。
JobPropertiesPage.saveSuccess=任务属性保存成功
JobProperty.archived.desc=如果某个属性不再被当前构建使用，但仍需要保留以便重启旧的构建，就可以将其标记为已归档。已归档的属性默认不会显示。
BuildPreservationPage.tips=定义构建保留的规则。主要作用：<ul><li>清理旧的构建，防止构建系统的存储空间被耗尽。</li><li>而某些关键构建（例如发布版本或用于调试）需要长期保留，以便以后能随时使用。</li></ul><b>注意:</b>只要此处或父项目中定义的一条规则保留了某个构建，该构建就会被一直保留。如果此处和父项目中都未定义保留规则，则所有构建都会被保留。<br>举个例子:<ul><li>条件：Branch = main && Status = Successful, 表示只保留 main 分支上成功的构建。</li><li>数量：Count = 5,表示只保留最近的 5 个符合条件的构建。</li></ul>
BuildPreservationPage.submitSuccess=构建保留规则保存成功
BuildPreservation.condition.name=条件
BuildPreservation.condition.desc=指定被保留的构建必须满足的条件
BuildPreservation.count.desc=要保留的构建数量
BuildPreservation.count.name=数量
BuildPreservation.count.placeholder=无限制
DefaultFixedIssueFiltersPage.tips=对于每个构建，系统会自动计算自上次构建以来已关闭的问题列表。该设置提供了一个默认查询，用于进一步筛选/排序问题列表。对于特定的构建任务，系统将优先使用第一个匹配的规则。
DefaultFixedIssueFiltersPage.submitSuccess=默认的已关闭问题筛选器保存成功
DefaultFixedIssueFilter.jobNames.name=哪些构建任务
DefaultFixedIssueFilter.jobNames.desc=指定哪些构建任务，中间以空格分隔。使用 '*' 或 '?' 进行通配符匹配。以'-'开头的会被排除
DefaultFixedIssueFilter.issueQuery.name=哪些问题
DefaultFixedIssueFilter.issueQuery.desc=指定一个默认查询，用于筛选/排序被指定构建任务关闭的问题
CacheManagementPage.preserveDays=保留天数
CacheManagementPage.uploadCaches=已上传的缓存
CacheManagementPage.columnKey=缓存键
CacheManagementPage.columnSize=大小
CacheManagementPage.columnLastAccessed=最后访问
CacheManagementPage.deleteSuccess=缓存'${key}'删除成功
CacheManagementPage.fileMissing=<i class='text-danger'>文件缺失或已失效</i>
CacheManagementPage.topTitle=构建任务缓存管理
CacheSettingBean.preserveDays.desc=如果在这设置的天数内缓存没有被访问，则缓存将被删除以节省空间
WebHooksPage.tips=对于此处和父项目中定义的 Web Hooks，当订阅的事件发生时，DevGrip以Post方式，JSON格式将事件数据发送到指定的URL。<br><b>请注意</b>：请求头中的<code>X-DevGrip-Signature</code>的值应与下面定义的密钥一致，否则请丢弃该请求。
WebHooksPage.submitSuccess=Web Hooks保存成功
WebHook.postUrl.name=Post URL
WebHook.postUrl.desc=接收 Webhook POST 请求的服务器端点URL
WebHook.eventTypes.name=事件类型
WebHook.secret.name=密钥
WebHook.secret.desc=用于保证发送事件数据的POST请求来自DevGrip。当您设置密钥后，您将在 Webhook POST 请求中接收到<code>X-DevGrip-Signature</code>请求头，如果请求头的值与这定义的密钥值应相同，则证明了请求是由DevGrip发送的，否则，您应该丢弃该请求。
BuildListPanel.queryOrOrder.placeholder=查询/排序构建
BuildListPanel.runJobTitle=运行构建任务
BuildListPanel.error.buildAlreadyFinished=构建#${number}已经完成
BuildListPanel.error.buildNotFinishedYet=构建#${number}还没有完成
BuildListPanel.cancelSuccess=取消请求已提交
BuildListPanel.cancelConfirm=在下方输入<code>yes</code>以取消所选构建
BuildListPanel.cancelAllQueriedConfirm=在下方输入<code>yes</code>以取消所有查询到的构建
BuildListPanel.selectBuildsCancelTitle=请选择要取消的构建
BuildListPanel.allQueriedBuildsCancelTitle=没有可取消的构建
BuildListPanel.reRunSuccess=重新运行请求已提交
BuildListPanel.resubmitManually=手动地执行重新提交
BuildListPanel.reRunConfirm=在下方输入<code>yes</code>以重新运行所选构建
BuildListPanel.allQueriedReRunConfirm=在下方输入<code>yes</code>以重新运行所有查询到的构建
BuildListPanel.selectBuildsReRunTitle=请选择要重新运行的构建
BuildListPanel.allQueriedBuildsReRunTitle=没有可重新运行的构建
BuildListPanel.deleteConfirm=在下方输入<code>yes</code>以删除所选构建
BuildListPanel.allQueriedDeleteConfirm=在下方输入<code>yes</code>以删除所有查询到的构建
BuildListPanel.selectBuildsDeleteTitle=请选择要删除的构建
BuildListPanel.allQueriedBuildsDeleteTitle=没有可删除的构建
BuildListPanel.countBuilds=查到${count}个构建
BuildListPanel.columnBuild=构建
BuildListPanel.columnDuration=持续时间
BuildListPanel.columnOnBehalfOf=构建时的分支/标签
BuildListPanel.columnLastUpdate=最后更新
BuildListPanel.noraWithI=<i>不详</i>
BuildListPanel.labelBranch=分支 ${branch}
BuildListPanel.labelTag=git标签 ${tag}
BuildListPanel.labelPr=拉取请求 #${pr}
BuildListPanel.labelTod=命令行工具
BuildLogPanel.buildLog.tooManyLogs=日志过多，仅显示最后${num}条
BuildLogPanel.buildLog.noLogs=无日志
BuildLogPanel.buildLog.total=总共查到${total}条日志
BuildLogPanel.buildLog.loadedCount=已加载${total}条日志
BuildLogPanel.buildLog.loadingLogs=正在加载构建日志...
BuildLogPanel.buildLog.allLogsloaded=全部${total}条日志都已加载
BuildLogPanel.buildLog.loadMore=加载更多
BuildLogPanel.buildLog.executionPaused=脚本执行已暂停
BuildLogPanel.buildLog.resume=恢复
MiniBuildListPanel.noBuilds=无
BuildMultiChoice.chooseBuilds=选择构建...
BuildMultiChoice.chooseBuild=选择构建...
BuildMultiChoice.chooseBranches=选择分支...
BuildMultiChoice.chooseBranch=选择分支...
BranchPicker.chooseWithI=<i>选择</i>
InvalidBuildPage.h2=该构建的提交已丢失
InvalidBuildPage.h2Tips=当项目指向错误的git仓库，或提交已被垃圾回收时，可能会发生此情况。
InvalidBuildPage.missingCommit=已丢失的提交
InvalidBuildPage.jobName=任务名称
InvalidBuildPage.deleteBuild=删除构建
InvalidBuildPage.builds=构建
InvalidBuildPage.deleteSuccess=构建 #${build} 已删除
InvalidBuildPage.entityNotFound=无法在项目 %s 中找到构建 #%s
InvalidBuildPage.deleteConfirm=您确定要删除此构建吗？
DescriptionBean.name=构建说明
BuildDetailPage.reRunTitle=重新运行此构建
BuildDetailPage.cancelTitle=取消此构建
BuildDetailPage.editTitle=编辑构建说明
BuildDetailPage.openTerminalTitle=在当前运行阶段打开终端
BuildDetailPage.promotionsTitle=继续运行下一级流水线任务
BuildDetailPage.buildSpecNotFound=此构建的提交中找不到构建配置文件
BuildDetailPage.job=构建任务
BuildDetailPage.notFound=未找到。
BuildDetailPage.importErrors=很可能构建配置文件中存在导入错误
BuildDetailPage.buildSpec=构建配置文件
BuildDetailPage.rebuildConfirm=您确定要重新构建吗？
BuildDetailPage.cancelConfirm=您确定要取消此构建吗？
BuildDetailPage.interactiveWebShell=交互式web终端，可用于访问正在执行的任务上下文、调试构建任务等作用。该功能是企业版功能。您可以<a href='https://devgrip.net/pricing' target='_blank'>免费试用</a>30天
BuildDetailPage.columnLog=日志
BuildDetailPage.columnPipeline=流水线
BuildDetailPage.columnArtifacts=构建产物
BuildDetailPage.columnFixedIssues=已关闭问题
BuildDetailPage.columnChanges=变更
BuildDetailPage.pageTitle.build=构建
BuildLogPage.downloadFullLog=下载全部日志
ArtifactUploadPanel.relativeDir=选填，指定相对目录来存放上传的文件
ArtifactUploadPanel.error.dirNotAllowed=目录中不允许包含'..'
BuildArtifactsPage.noArtifacts=没有已发布的构建产物
BuildArtifactsPage.columnlastModified=最后修改
BuildArtifactsPage.deleteFileConfirm=确定要删除该文件吗？
BuildArtifactsPage.deleteDirConfirm=确定要删除该目录吗？
FixedIssuesPage.tips=无法计算已关闭问题，很可能该项目下同样分支、同样任务还没有构建成功过。
BuildReportTabOptionPanel.viewStatTitle=查看统计数据
JobInfoButton.runJobTitle=运行该任务
RunJobLink.submitManually=手动提交
RunJobLink.rebuildManually=手动进行重新构建
RunJobLink.noRefsBuildOnBehalf=没有可用于构建的分支/标签
JobMultiChoice.chooseJobs=请选择任务...
JobSingleChoice.chooseJob=请选择任务...
BuildOptionContentPanel.h5=指定构建选项
BuildOptionContentPanel.buildOnBehalf=构建时的分支/标签
BuildOptionContentPanel.tips=对于每个选定的分支/标签，将生成一个单独的构建，并将分支/标签设置为相应的值。
BuildOptionContentPanel.errorValid=请至少选择一个分支或标签
JobRunSelector.searchJob=搜索任务
JobRunSelector.selectJob=请选择任务
JobRunSelector.noJobsFound=查不到构建任务
EntityNavPanel.entityName.build=构建
###在中文语境下，要的就是这种中间什么都没有的分隔符###
HumanDuration.delimiter=
HumanDuration.na=不详
HumanDuration.noTime=时间过短忽略不计
HumanDuration.year=%d年
HumanDuration.month=%d月
HumanDuration.week=%d周
HumanDuration.day=%d天
HumanDuration.hour=%d小时
HumanDuration.minute=%d分钟
HumanDuration.second=%d秒
GpgSignatureVerifier.error.notVerified=该GPG密钥的邮箱还未经过验证
GpgSignatureVerifier.error.invalid=无效的GPG签名
GpgSignatureVerifier.error.unknownKey=使用未知的GPG密钥签名（密钥 ID: %s）
GpgSignatureVerifier.error.withoutNecessaryData=该GPG签名缺少必要的数据
GpgSignatureVerifier.error.couldNotVerifying=验证GPG签名时出错
RunTaskBehavior.inProgress=正在处理...
DefaultJobManager.joblog.autoDiscovering=找不到您配置的任务执行器, 系统将会自动寻找...
DefaultJobManager.joblog.discoveredExecutor=寻找到的任务执行器类型为: %s
DefaultJobManager.joblog.finished=构建任务运行完成
DefaultJobManager.joblog.timeout=构建任务运行超时
DefaultJobManager.joblog.error=构建任务运行错误
DefaultJobManager.joblog.lockingGroup=正在锁定顺序运行组...
DefaultJobManager.joblog.retryAfter=构建任务稍后会进行重试...
DefaultJobManager.joblog.dependenciesRequireSuccessful=有些依赖的任务失败了
BuildSpecBlobViewPanel.jobs=构建任务
BuildSpecBlobViewPanel.services=服务
BuildSpecBlobViewPanel.stepTemplates=步骤模板
BuildSpecBlobViewPanel.properties=属性
BuildSpecBlobViewPanel.imports=导入
BuildSpecBlobViewPanel.errorParseBuildSpec=解析构建配置文件时出错
BuildSpecBlobViewPanel.this=该
BuildSpecBlobViewPanel.importFrom=是从别处导入而来的
BuildSpecBlobViewPanel.propertyImportFrom=该属性是从${path}导入的
BuildSpecBlobViewPanel.noProperties=未定义任何属性
BuildSpecBlobViewPanel.noJobs=未定义任何构建任务
BuildSpecBlobViewPanel.noServices=未定义任何服务
BuildSpecBlobViewPanel.noStepTemplates=未定义任何步骤模板
BuildSpecBlobViewPanel.noImports=未定义任何导入
BuildSpecBlobViewPanel.noBuildSpec=未找到构建配置文件
BuildSpecBlobViewPanel.errorBuildSpecWithProperty=验证构建配置文件时出错（位置：%s，错误信息：%s）
BuildSpecBlobViewPanel.errorBuildSpec=验证构建配置文件时出错：%s
BuildSpecEditPanel.suggestions=快速生成
BuildSpecEditPanel.noNameWithI=<i>无名称</i>
BuildSpec.malformed=构建配置文件格式错误
Job.name.desc=指定任务的名称
Job.jobExecutor.name=任务执行器
Job.jobExecutor.desc=选填，为此构建任务指定合适的执行器。留空则使用首个适用的执行器（如果未定义执行器，则系统自动寻找执行器）
Job.autoDiscoveredExecutor=自动寻找执行器
Job.firstApplicableExecutor=首个适用的执行器
Job.steps.name=步骤
Job.steps.desc=任务步骤将在同一节点上按顺序执行，共享同一个构建<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>
Job.paramSpec.name=参数配置
Job.paramSpec.group=参数与触发器
Job.paramSpec.desc=选填，配置任务所需的参数
Job.triggers.name=触发器
Job.triggers.desc=使用触发器可以在特定条件下自动运行构建任务
Job.jobDependence.name=任务依赖
Job.jobDependence.group=依赖与服务
Job.jobDependence.desc=决定了运行不同任务时的顺序与并发性。您还可以指定从上游任务中获取的构建产物
Job.projectDependence.name=项目依赖
Job.projectDependence.desc=使用项目依赖从其他项目中获取构建产物
Job.requiredService.name=必要服务
Job.requiredService.placeholder=无必要服务
Job.requiredService.desc=选填，指定此任务所需的必要服务。<b class='text-warning'>注意：</b>服务仅支持Docker相关的执行器，即 <code>本地Docker执行器</code>, <code>远程Docker执行器</code>,  <code>Kubernetes执行器</code>
Job.sequentialGroup.name=顺序执行组
Job.sequentialGroup.group=更多设置
Job.sequentialGroup.desc=具有相同顺序执行组和执行器的任务将按顺序执行。例如，您可以将此属性指定为<tt>@project_path@:prod</tt>，用于由相同执行器执行并部署到当前项目的生产环境的任务，以避免部署冲突
Job.retryCondition.name=重试条件
Job.retryCondition.desc=指定在失败时重试构建的条件
Job.maxRetries.name=最大重试次数
Job.maxRetries.desc=放弃执行前的最大重试次数
Job.retryDelay.name=重试延迟
Job.retryDelay.desc=首次重试的延迟时间（以秒为单位）。后续重试的时间将基于此值按指数回退的方式进行计算
Job.timeout.name=超时
Job.timeout.desc=指定超时时间（以秒为单位），从任务提交时开始计时。
Job.postBuildActions.name=构建完成的后续操作
PostBuildAction.name=构建完成后续操作
PostBuildAction.condition.name=条件
PostBuildAction.condition.desc=指定当前构建必须满足什么条件才能执行该操作
RunJobAction.name=运行构建任务
SendNotificationAction.name=发送通知
SendNotificationAction.receivers.name=接收者
CreateIssueAction.name=创建问题
CreateIssueAction.project.placeholder=当前项目
CreateIssueAction.project.desc=选填，指定创建问题的项目。留空将在当前项目中创建
CreateIssueAction.accessTokenSecret.desc=指定一个任务密钥，用作在上述项目中创建问题（如果该项目不可公开访问）。注意：这个任务密钥的值是具备在上述项目创建问题的用户的访问令牌。
CreateIssueAction.accessTokenSecret.name=访问令牌密钥
CreateIssueAction.issueTitle.name=标题
CreateIssueAction.issueTitle.group=问题详情
CreateIssueAction.issueTitle.desc=指定问题的标题
CreateIssueAction.issueDescription.desc=选填，指定问题的说明
CreateIssueAction.confidential.name=私密
CreateIssueAction.confidential.desc=是否将问题设为私密
PostBuildActionListEditPanel.addNewTitle=添加新的后续操作
PostBuildActionListViewPanel.actionTypeTitle=构建完成后续操作(类型: ${actionType})
NotFoundWithI=<i>查不到</i>
ImportListEditPanel.tips=从其他项目导入构建配置（任务、服务、步骤模板和属性）。导入的配置将被视为本地所定义的。本地定义的配置会覆盖具有相同名称的导入配置。
ImportListEditPanel.addNewImport=添加新的导入
JobDependencyEditPanel.error.alreadyDefined=对此任务依赖重复了
JobDependencyEditPanel.jobDependency=任务依赖
JobDependencyListEditPanel.columnRequireSucc=是否要求任务运行成功
JobDependencyListEditPanel.columnParamsCount=参数数量
JobDependencyListEditPanel.addNew=添加新的任务依赖
ProjectDependency.projectDependency=项目依赖
ProjectDependencyListEditPanel.addNew=添加新的项目依赖
ProjectDependencyListEditPanel.columnBuild=构建
JobTriggerEditPanel.jobTrigger=触发器
JobTriggerListEditPanel.addNew=添加新的触发器
JobTriggerListViewPanel.triggerType=触发器(类型: ${triggerType})
ParamSpecEditPanel.error.nameAlreadyUsed=该名称已被使用
ParamSpecEditPanel.parameterDefinition=参数定义
ParamSpecListEditPanel.addNew=添加新的参数
ParamSpecListViewPanel.paramType=参数(类型: ${paramType})
StepListEditPanel.addNew=添加新的步骤
StepListEditPanel.moreOps=更多操作
VariableInterpolator.helpText=<b>提示:</b>输入<tt>@</tt>选择插入<a href='https://docs.devgrip.net/appendix/job-variables' target='_blank' tabindex='-1'>系统内置变量</a>。如果要输入字符<tt>@</tt>，请输入<tt>@@</tt>代替。
VariableInterpolator.helpTextDot=。
BuildSidePanel.countBuilds=${count}个构建
HttpCredential.accessTokenSecret.name=访问令牌密钥
HttpCredential.accessTokenSecret.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>作为访问令牌。也就是：该任务密钥的值是某个用户的访问令牌。
HttpCredential.error.notFound=未找到密钥(%s)
SshCredential.keySecret.name=密钥密文
SshCredential.keySecret.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>作为SSH私钥。也就是：该任务密钥的值是SSH的私钥。
DefaultCredential.name=默认
CreateIssueAction.description.value=创建问题
CreateIssueAction.error.validating=验证问题字段时出错：
RunJobAction.description.value=运行任务'%s'
RunJobAction.submitReason=任务'%s'的构建完成后续操作
RunJobAction.error.validating=校验任务参数时出错（任务: %s，错误信息: %s）
RunJobAction.error.jobNotFound=未找到任务 (%s)
SendNotificationAction.description.value=发送通知给%s
LastFinishedBuild.name=最后完成的构建任务
LastFinishedBuild.jobname.placeholder=请选择构建...
LastFinishedBuild.refName.name=git引用
LastFinishedBuild.refName.placeholder=任意引用
LastFinishedBuild.refName.desc=选填，指定上述构建任务在git上的引用(分支/标签/远程分支等)，例如：<i>refs/heads/main</i>。使用 * 进行通配符匹配
LastFinishedBuild.descriptionWithRef=构建任务'%s'在git引用'%s'上最后完成
LastFinishedBuild.description=构建任务'%s'最后完成
ProjectDependency.projectPath.desc=指定要从哪些项目中获取构建产物
ProjectDependency.artifacts.name=要获取的构建产物
ProjectDependency.artifacts.desc=指定要从<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>获取哪些构建产物。仅可获取通过产物发布步骤发布的产物。
ProjectDependency.destProject.name=目标路径
ProjectDependency.destProject.placeholder=任务工作空间
ProjectDependency.destProject.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的一个相对路径，用于放置获取到的构建产物。留空则使用任务工作空间本身
ProjectDependency.accessTokenSecret.name=访问令牌密钥
ProjectDependency.accessTokenSecret.placeholder=匿名访问
ProjectDependency.accessTokenSecret.desc=指定一个任务密钥，用作从上述项目获取构建产物的访问令牌。如果未指定，将以匿名方式访问项目构建产物
SpecifiedBuild.name=通过构建编号指定
SpecifiedBuild.buildNumber.placeholder=构建编号
BlobViewPanel.linesLabel=共${count}行
JobDependency.requireSuccessful.name=要求任务运行成功
JobDependency.requireSuccessful.desc=是否要求依赖的任务运行成功
JobDependency.artifacts.desc=选填，指定从依赖中取回到<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的产物。只有通过产物发布步骤发布的产物才能被取回。留空表示不取回任何产物
JobDependency.artifacts.name=要取回的产物
JobDependency.artifacts.placeholder=不取回
JobDependency.destPath.desc=选填，指定一个相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径以放置取回的产物。留空表示直接取回到任务工作空间
JobDependency.destPath.name=目标路径
JobDependency.destPath.placeholder=任务工作空间
Job.error.dupDp=重复依赖（%s）
Job.error.dupParam=重复的参数（%s）
Job.error.malformedRc=重试条件格式错误
Job.error.validating=校验任务参数时出错（参数位置：#%s，错误信息：%s）
JobTrigger.project.desc=选填，指定适用于此触发器的项目，中间以空格隔开。此选项对于防止任务在分叉的项目中被触发非常有用。使用 '**'、'*' 或 '?' 进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。留空匹配所有项目
ExcludeParamCombos=排除参数组合
BranchUpdateTrigger.bu.name=分支更新后
BranchUpdateTrigger.bu.desc=代码提交时运行任务。<b class='text-info'>注意：</b>此触发器会忽略包含以下信息的提交消息：<code>[skip ci]</code>、<code>[ci skip]</code>、<code>[no ci]</code>、<code>[skip job]</code>、<code>[job skip]</code> 或 <code>[no job]</code>
BranchUpdateTrigger.branches.desc=（可选）指定要检查的那些分支，中间以空格分隔的。使用 '**' 或 '*' 或 '?' 进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。留空匹配所有分支
BranchUpdateTrigger.paths.name=哪些文件
BranchUpdateTrigger.paths.placeholder=任何文件
BranchUpdateTrigger.paths.desc=（可选）指定要检查的那些文件，中间以空格分隔。使用 '**'、'*' 或 '?' 进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。留空匹配所有文件
BranchUpdateTrigger.reason=分支'%s'已更新
BranchUpdateTrigger.desc1=当更新分支'%s'并涉及文件'%s'时
BranchUpdateTrigger.desc2=当更新分支'%s'时
BranchUpdateTrigger.desc3=当涉及文件'%s'时
BranchUpdateTrigger.desc4=当更新分支时
DependencyFinishedTrigger.name=依赖的任务完成后
DependencyFinishedTrigger.reason=依赖的任务'%s'执行完成
DependencyFinishedTrigger.desc=当依赖的任务完成时
IssueInStateTrigger.name=问题的状态为
IssueInStateTrigger.desc=任务将在默认分支的最新提交上运行
IssueInStateTrigger.state.name=状态
Any_Issue=任何问题
JobTrigger.descInProject=在项目'%s'中
IssueInStateTrigger.reason=问题状态为'%s'
IssueInStateTrigger.desc1=当问题处于状态'%s'并匹配：%s
IssueInStateTrigger.desc2=当问题处于状态'%s'
PullRequestDiscardTrigger.name=丢弃拉取请求后
PullRequestDiscardTrigger.desc=任务将在目标分支的最新提交上运行
PullRequestDiscardTrigger.reason=拉取请求已丢弃
PullRequestDiscardTrigger.action=丢弃
PullRequestMergeTrigger.name=合并拉取请求后
PullRequestMergeTrigger.desc=任务将在目标分支和源分支的合并提交上运行
PullRequestMergeTrigger.reason=拉取请求已合并
PullRequestMergeTrigger.action=合并
PullRequestUpdateTrigger.name=开启或更新拉取请求后
PullRequestUpdateTrigger.desc=任务将在目标分支和源分支的合并提交上运行。<br><b class='text-info'>注意：</b>除非分支保护规则要求，否则此触发器会忽略包含以下信息的提交消息：<code>[skip ci]</code>、<code>[ci skip]</code>、<code>[no ci]</code>、<code>[skip job]</code>、<code>[job skip]</code> 或 <code>[no job]</code>
PullRequestUpdateTrigger.reason=拉取请求已开启/更新
PullRequestUpdateTrigger.action=开启/更新
PullRequestTrigger.desc1=当%s拉取请求的目标分支为'%s'并涉及文件'%s'
PullRequestTrigger.desc2=当%s拉取请求的目标分支为'%s'
PullRequestTrigger.desc3=当%s拉取请求涉及文件'%s'
PullRequestTrigger.desc4=当%s拉取请求
ScheduleTrigger.name=Cron调度
ScheduleTrigger.CronExp.name=Cron表达式
ScheduleTrigger.CronExp.desc=指定一个<a target='_blank' href='http://www.quartz-scheduler.org/documentation/quartz-2.3.0/tutorials/crontrigger.html#format'>Cron表达式</a>来自动触发任务。<b class='text-info'>注意：</b>为了节省资源，Cron表达式中的秒将被忽略，最小调度间隔为一分钟
ScheduleTrigger.branches.placeholder=默认分支
ScheduleTrigger.branches.desc=（可选）指定适用于此触发器的分支，中间以空格分隔。使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径通配符匹配</a>。以'-'开头的将会被排除。留空表示默认分支
ScheduleTrigger.reason=已调度
ScheduleTrigger.desc1=分支'%s'调度于 %s
ScheduleTrigger.desc2=默认分支调度于 %s
TagCreateTrigger.name=标签创建后
TagCreateTrigger.tags.placeholder=任何标签
TagCreateTrigger.tags.desc=（可选）指定要检查的git标签，中间以空格分隔。使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径通配符匹配</a>。以'-'开头的将会被排除。留空以匹配所有git标签
TagCreateTrigger.branches.name=所在分支
TagCreateTrigger.branches.placeholder=任何分支
TagCreateTrigger.branches.desc=此触发器仅在指定分支的提交历史记录中包含这个标签时才适用，多个分支以空格分隔。使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径通配符匹配</a>。以'-'开头的将会被排除。留空以匹配所有分支
TagCreateTrigger.reason=标签'%s'已创建
TagCreateTrigger.desc1=当在分支'%s'上创建标签'%s'时
TagCreateTrigger.desc2=当创建标签'%s'时
TagCreateTrigger.desc3=当在分支'%s'上创建标签时
TagCreateTrigger.desc4=当创建标签时
ParamIgnoreValue.name=忽略此参数
ParamSpecifiedValue.name=使用指定值或任务密钥
ParamSpecifiedValue.dn=使用指定值
ParamSpecifiedValue.sdn=使用指定任务密钥
ParamSpecifiedValues.name=使用指定值或任务密钥
ParamSpecifiedValues.dn=使用指定值
ParamSpecifiedValues.sdn=使用指定任务密钥
ParamScriptingValue.name=通过脚本计算值或密钥
ParamScriptingValue.dn=通过脚本计算值
ParamScriptingValue.sdn=通过脚本计算密钥
ParamScriptingValues.name=通过脚本计算值或任务密钥
ParamScriptingValues.dn=通过脚本计算值
ParamScriptingValues.sdn=通过脚本计算任务密钥
ParamPassthroughValue.name=使用指定参数/密钥的值
ParamPassthroughValue.dn=使用指定参数的值
ParamUtils.validating.atLeastOneValueNeed=请至少指定一个值
ParamUtils.validating.DupValueNotAllow=不允许重复值
ParamUtils.validating.paramValuesError=参数值校验错误(参数: %s, 错误信息: %s)
ParamUtils.validating.paramValueError=参数值校验错误(参数: %s, 值: %s, 错误信息: %s)
ParamUtils.validating.secretNotFound=找不到密钥
ParamUtils.validating.missingJobParameter=构建任务缺少参数(%s)
ParamUtils.validating.unknownJobParameter=未知的构建任务参数(%s)
ParamUtils.validating.dupParam=重复的参数(%s)
ParamUtils.secret.note=<div style='margin-top: 12px;'><b>注意:</b>若您的构建密钥少于%d个字符时，在构建日志中将不会脱敏</div>
ParamMatrixEditPanel.jobTips=您可以额外添加多个的值来运行任务，这样该任务会针对所有参数的不同值组合逐一运行
ParamMatrixEditPanel.stepTips=您可以额外添加多个值来运行任务步骤，这样该步骤会针对所有参数的不同值组合逐一运行
ParamSpec.description.desc=选填，请描述该参数，支持HTML标签。
ParamSpec.allowMultiple.desc=是否允许为该参数指定多个值
ParamSpec.allowMultiple.name=允许多个值
ParamSpec.sc.name=满足一定条件才显示
ParamSpec.sc.desc=如果该字段是否显示取决于其他字段，则启用
ParamSpec.sc.placeholder=始终显示
ParamSpec.amv.name=允许空值
ParamSpec.amv.desc=该参数是否接受空值
ParamSpec.defaultProviderValue.name=默认值
ParamSpec.defaultProviderValue.placeholder=无默认值
ParamSpec.ChoiceProvider.name=可用选项
IntegerParam.min.name=最小值
IntegerParam.min.desc=选填，指定允许的最小值。
IntegerParam.max.name=最大值
IntegerParam.max.desc=选填，指定允许的最大值。
TextParam.multiLine.name=多行
TextParam.pattern.name=校验该参数的正则表达式
TextParam.pattern.desc=选填，为文本输入指定一个<a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>正则表达式</a>校验输入值是否有效。
Project.serviceDeskEmailAddress.name=服务台邮件地址
Project.serviceDeskEmailAddress.placeholder=默认
Project.serviceDeskEmailAddress.desc=指定一个与系统邮箱地址共享同一收件箱的邮箱地址（在邮件设置定义中）。发送到此邮箱地址的邮件将被创建为该项目中的问题。默认值的格式为：<tt>&lt;系统邮箱地址的名称部分&gt;+&lt;项目路径&gt;@&lt;系统邮箱地址的域名部分&gt;</tt>
PassthroughValue.error=找不到参数: %s
BuildSpec.validating.errorImported=验证导入的 %s 时出错（%s: %s，错误信息: %s）
BuildSpec.validating.errorImportedWithLocation=验证导入的 %s 时出错（%s: %s，位置: %s，错误信息: %s）
BuildSpec.validating.errorDupJobName=重复的任务名称（%s）
BuildSpec.validating.errorDupServiceName=重复的服务名称（%s）
BuildSpec.validating.errorDupTemplateName=重复的步骤模板名称（%s）
BuildSpec.validating.errorDupPropertyName=重复的属性名称（%s）
BuildSpec.validating.errorDupImportProject=重复导入（项目: %s，修订版本: %s）
BuildSpec.validating.errorImportedStepTemplate=验证导入的步骤模板时出错（步骤模板: %s，错误信息: %s）
BuildSpec.validating.errorImportedJob=验证导入的任务时出错（任务: %s，错误信息: %s）
BuildSpec.validating.errorImportedJobWithLocationAndStepTemplate=验证导入的任务时出错（任务: %s，位置: steps[%d].templateName，错误信息: %s）
BuildSpec.validating.errorPostBuildAction=验证构建后续操作时出错（索引: %d，错误信息: %s）
BuildSpec.validating.errorUndefinedService=未定义的服务（%s）
BuildSpec.validating.errorCircularTemplateUsages=模板循环使用（%s）
BuildSpec.validating.errorStepTemplateParameters=验证步骤模板参数时出错（%s）
BuildSpec.validating.errorStepTemplateNotFound=未找到步骤模板（%s）
BuildSpec.validating.errorCircularDep=循环依赖（%s）
BuildSpec.validating.errorDepJobNotFound=未找到依赖任务（%s）
BuildSpec.validating.errorDepJobParam=验证依赖任务参数时出错（依赖任务: %s，错误信息: %s）
BuildSpec.elementTypeName.job=任务
BuildSpec.elementTypeName.service=服务
BuildSpec.elementTypeName.stepTemplate=步骤模板
BuildSpec.elementTypeName.property=属性
Service.name.desc=指定服务名称，此名称将用作访问服务的主机名
Service.image.name=镜像
Service.image.desc=指定服务的 Docker 镜像
Service.args.name=参数
Service.args.desc=选填，指定运行上述镜像的参数
Service.envs.name=环境变量
Service.envs.desc=选填，指定该服务的环境变量
Service.rcc.name=就绪检查命令
Service.rcc.desc=指定用于检查服务就绪状态的命令。此命令将在 Windows 镜像上由 cmd.exe 执行，在 Linux 镜像上由 shell 执行。此命令将被重复执行，直到返回0，表示服务已就绪
Service.runAs.name=以哪个用户运行
Service.runAs.group=更多设置
Service.runAs.placeholder=root
Service.runAs.desc=选填，以uid:gid形式指定容器以何种身份运行。<b class='text-warning'>注意：</b> 如果容器运行时是rootless的或使用用户命名空间重新映射(user namespace remapping)，此设置应留空
Service.rl.desc=选填，指定镜像库的登录信息用以覆盖任务执行器中定义的。对于内置的镜像库，请使用 <code>@server_url@</code> 作为镜像库 URL，<code>@job_token@</code> 作为用户名，使用访问令牌密钥作为密码
StepRegistryLogin.un.desc=指定镜像库的用户名
Import.project.desc=指定从哪个项目中导入构建配置文件
Import.revision.desc=指定上述项目中的修订版本（分支、标签或提交）以导入构建配置文件
Import.revision.name=修订版本
Import.secret.name=访问令牌密钥
Import.secret.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，用于在上述项目代码不可公开访问时作为访问令牌导入构建配置文件
Import.error.unableToImport=无法导入构建配置文件（导入项目: %s，导入版本: %s）：%s
Import.error.noCodeReadPermission=需要代码读取权限以导入构建配置文件（导入项目: %s，导入版本: %s）
Import.error.malformedBuildSpec=构建配置文件格式错误（导入项目: %s，导入版本: %s）
Import.error.buildSpecNotDefined=未定义的构建配置文件（导入项目: %s，导入版本: %s）
Import.error.circularBuildSpecImports=构建配置文件循环导入（%s）
Import.error.importedBuildSpec=验证导入的构建配置文件时出错（导入项目: %s，导入版本: %s，位置: %s，信息: %s）
Import.error.failedToValidate=构建配置导入验证失败。请检查服务器日志了解详情
Import.error.unableToFindProject=无法找到要导入构建配置文件的项目: %s
Import.error.unableToFindCommit=无法从提交中导入构建配置（导入项目: %s，导入版本: %s）
DefaultInterpreter.name=默认（Linux 上为 Shell，Windows 上为 Batch）
DefaultInterpreter.commands.name=命令
DefaultInterpreter.commands.desc=指定在<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>下执行的 shell 命令（在 Linux/Unix 上）或批处理命令（在 Windows 上）
PowerShellInterpretor.commands.desc=指定在<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>下执行的 PowerShell 命令。<br><b class='text-warning'>注意：</b> 系统检查脚本的退出代码以确定该步骤是否成功。由于 PowerShell 即使脚本出现错误也总是以 0 退出，因此应在脚本中处理错误并使用非零代码退出，或者在脚本开头添加行 <code>$ErrorActionPreference = &quot;Stop&quot;</code><br>
PowerShellInterpretor.powershell.name=可执行文件
PowerShellInterpretor.powershell.desc=指定要使用的 PowerShell 可执行文件
ShellInterpreter.name=自定义 Linux Shell
ShellInterpreter.shell.desc=指定要使用的 Shell
ShellInterpreter.commands.desc=指定在<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>下执行的 Shell 命令
StepGroup.securityAndCompliance=安全与合规
StepGroup.publish=发布
StepGroup.utilities=实用工具
StepGroup.repoSync=代码库同步
StepGroup.dependencyManagement=依赖管理
StepGroup.dockerImage=Docker 镜像
BuildImageStep.name=构建镜像
BuildImageStep.desc=使用 Docker Buildx 构建 Docker 镜像。此步骤仅能由本地Docker执行器 或 远程Docker执行器 执行，并使用这些执行器中指定的 Buildx 构建器完成工作。若要使用 Kubernetes执行器 构建镜像，请改用 构建镜像(Kaniko)
BuildImageStep.buildPath.name=构建路径
BuildImageStep.buildPath.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的构建路径。留空则使用任务工作空间本身
BuildImageStep.dockerfile.name=Dockerfile
BuildImageStep.dockerfile.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的 Dockerfile 文件路径。留空则使用构建路径中名为 <tt>Dockerfile</tt> 的文件
BuildImageStep.output.name=输出策略
BuildImageStep.platforms.name=平台
BuildImageStep.platforms.placeholder=当前平台
BuildImageStep.platforms.desc=选填，指定要构建出哪些平台的产物，多个中间以 <span class='text-info'>,</span> 分隔，例如 <tt>linux/amd64,linux/arm64</tt>。留空则构建为运行任务的节点的平台
BuildImageStep.moreOptions.name=更多选项
BuildImageStep.moreOptions.desc=选填，指定 Buildx 构建命令的其他选项
RegistryOutput.name=推送到容器镜像库
RegistryOutput.tags.name=镜像标签
RegistryOutput.tags.desc=指定要推送的镜像标签，例如 your-registry-server:5000/your-org/repo:latest。如果要推送到内置镜像库，请直接使用格式 @server@/<project path>/<repo name>:<tag name>。多个标签应以空格分隔
OCIOutput.name=导出OCI布局结构
OCIOutput.destPath.name=OCI布局结构目录
OCIOutput.destPath.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的路径以存储OCI布局结构
Step.condition.name=条件
Step.condition.desc=指定此步骤运行的条件。“均已成功” 表示之前所有必要步骤均已成功完成。
Step.optional.name=可选
Step.optional.desc=如果该步骤是可选的，它的失败不会导致构建失败，后续步骤判断成功时也会忽略它。
BuildImageWithKanikoStep.name=构建镜像（Kaniko）
BuildImageWithKanikoStep.desc=使用 Kaniko 构建 Docker 镜像。此步骤需由本地Docker执行器 、 远程Docker执行器 或 Kubernetes执行器 执行
BuildImageWithKanikoStep.buildContext.name=构建上下文
BuildImageWithKanikoStep.buildContext.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的构建上下文路径。留空则使用任务工作空间本身。除非通过选项 <code>--dockerfile</code> 指定其他位置，否则在构建上下文目录中应存在名为 <code>Dockerfile</code> 的文件
CertificatesToTrust=受信任证书
MoreSettings=更多设置
CertificatesToTrustPlaceholder=Base64 编码的 PEM 格式，以 -----BEGIN CERTIFICATE----- 开始并以 -----END CERTIFICATE----- 结束
CertificatesToTrustDesc=如果您的 Docker 镜像库使用自签名证书，请在此指定信任的证书
MoreOptionsName=更多选项
MoreOptionsDesc=选填，指定 Kaniko 的<a href='https://github.com/GoogleContainerTools/kaniko?tab=readme-ov-file#additional-flags' target='_blank'>其他选项</a>
RegistryOutput.destinations.name=目标位置
RegistryOutput.destinations.desc=指定目标，例如 your-registry-server:5000/your-org/repo:latest。如果要推送到内置镜像库，请直接使用格式 @server@/<project path>/<repo name>:<tag name>。多个目标应以空格分隔。
CommandStep.name=执行命令
CommandStep.runInContainer.name=在容器中运行
CommandStep.runInContainer.desc=是否在容器中运行此步骤
CommandStep.image.name=镜像
CommandStep.image.desc=指定容器的镜像以便在其中执行命令
CommandStep.interpreter.name=解释器
CommandStep.runAs.name=以哪个用户身份运行
CommandStep.runAs.placeholder=root
CommandStep.runAs.desc=选填，指定以 uid:gid 身份运行容器。<b class='text-warning'>注意：</b> 如果容器运行时为无根(rootless)模式或使用用户命名空间映射(user namespace remapping)，此设置应留空
CommandStep.rl.name=镜像库登录
CommandStep.rl.desc=选填，指定镜像库的登录信息用以覆盖任务执行器中定义的。对于内置的镜像库，请使用 <code>@server_url@</code> 作为镜像库 URL，<code>@job_token@</code> 作为用户名，使用访问令牌密钥作为密码
CommandStep.env.name=环境变量
CommandStep.env.desc=选填，为此步骤指定环境变量
CommandStep.enableTty.name=启用 TTY 模式
CommandStep.enableTty.desc=许多命令在 TTY 模式下以 ANSI 颜色打印输出从而快速识别问题。然而，一些命令在此模式下可能会等待用户输入导致构建挂起，这些通常可以通过为命令添加额外选项来解决此问题
StepTemplate.steps.name=步骤
StepTemplate.steps.desc=步骤将在同一节点上按顺序执行，共享同一个<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>
StepTemplate.param.name=参数规范
StepTemplate.param.desc=选填，定义步骤模板的参数规范
UseStepTemplate.name=使用步骤模板
UseStepTemplate.desc=运行指定的步骤模板
UseStepTemplate.templateName.name=模版名称
UseStepTemplate.error=未找到步骤模板：%s
SyncRepository.remoteurl.name=远程 URL
SyncRepository.remoteurl.desc=指定远程 Git 仓库的 URL。仅支持 http/https 协议
SyncRepository.username.name=用户名
SyncRepository.username.desc=选填，指定访问远程仓库的用户名
SyncRepository.password.name=远程仓库的密码或访问令牌
SyncRepository.password.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，用作访问远程仓库的密码或访问令牌
SyncRepository.certificate.desc=如果您的远程仓库使用了自签名证书，请指定信任的证书
SyncRepository.proxy.name=代理地址
SyncRepository.proxy.placeholder=无
SyncRepository.proxy.desc=选填，配置访问远程仓库的代理。代理格式应为 &lt;代理主机&gt;:&lt;代理端口&gt;
SyncRepository.force.name=强制
SyncRepository.force.desc=当引用更新而无法执行快进时，是否使用强制选项来覆盖原有更改
PullRepository.name=从远程拉取
PullRepository.desc=此步骤从远程拉取指定的git引用。
PullRepository.targetProject.name=目标项目
PullRepository.targetProject.placeholder=当前项目
PullRepository.targetProject.desc=选择要同步到哪个项目。留空以同步到当前项目
PullRepository.token.name=目标项目的访问令牌
PullRepository.token.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值为具有上述项目管理权限的访问令牌。注意，如果同步到当前或子项目且提交可从默认分支访问，则无需访问令牌
PullRepository.refs.name=引用
PullRepository.refs.desc=指定要从远程拉取的引用，中间以空格分隔。'*' 可用于引用名称中的通配符匹配。例如：refs/heads/dev 拉取dev分支，refs/tags/v1.0 拉取v1.0标签。<br> <b class='text-danger'>注意：</b>通过此步骤更新分支/标签时，将忽略分支/标签保护规则
PullRepository.lfs.name=拉取 LFS 文件
PullRepository.lfs.desc=如果启用此选项，则服务器上需要安装 git lfs 命令（即使此步骤在其他节点上运行）
PullRepository.error.targetProjectNotFound=未找到目标项目：%s
PullRepository.error.notAuthorized=此构建未被授权同步到项目：%s
PushRepository.name=推送到远程
PushRepository.desc=此步骤将当前提交推送到远程的相同引用
CheckoutStep.name=检出代码
CheckoutStep.cloneCredenital.desc=默认情况下，代码通过自动生成的凭据克隆，该凭据仅具有当前项目的读取权限。如果任务需要<a href='https://docs.devgrip.net/tutorials/cicd/commit-and-push' target='_blank'>将代码推送到服务器</a>，则需要在此处提供具有适当权限的自定义凭据
CheckoutStep.cloneCredenital.name=克隆凭据
CheckoutStep.lfs.name=检出 LFS 文件
CheckoutStep.lfs.desc=选中此项则同时检出 Git LFS 文件
CheckoutStep.submodules.name=检出git子模块
CheckoutStep.submodules.desc=是否检出子模块(Git Submodules)。请参考<a href='https://docs.devgrip.net/tutorials/cicd/clone-submodules' target='_blank'>教程</a>了解如何设置克隆凭据检出子模块
CheckoutStep.depth.name=克隆深度
CheckoutStep.depth.desc=选填，指定浅克隆的深度。对于大规模的项目，仓库历史可能非常庞大，浅克隆可以节省存储空间和带宽。例如<code>git clone --depth 1 <repository_url></code>
CheckoutStep.checkoutPath.name=检出路径
CheckoutStep.checkoutPath.placeholder=任务工作空间
CheckoutStep.checkoutPath.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的相对路径存放克隆的代码。留空以使用任务工作空间本身
CloseIterationStep.name=关闭迭代
CloseIterationStep.iteration.name=迭代名称
CloseIterationStep.iteration.desc=指定迭代的名称
AccessTokenSecret=访问令牌密钥
CloseIterationStep.ats.desc=对于默认分支不可访问的构建提交，应指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值为具有管理问题权限的访问令牌
CloseIterationStep.error.noAuth=此构建未被授权关闭迭代 '%s'
CloseIterationStep.error.unableFind=无法找到要关闭的迭代 '%s'。已忽略。
CreateBranchStep.name=创建分支
CreateBranchStep.branch.name=分支名称
CreateBranchStep.branch.desc=指定分支的名称
CreateBranchStep.revision.name=修订版本
CreateBranchStep.revision.placeholder=构建提交
CreateBranchStep.revision.desc=选填，指定用于创建分支的修订版本。留空以从构建提交中创建
CreateBranchStep.ats.desc=对于默认分支不可访问的构建提交，应指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值为具有创建分支权限的访问令牌
CreateBranchStep.error.invalidName=无效的分支名称：%s
CreateBranchStep.error.nameAlreadyExists=分支 %s 已存在
CreateBranchStep.error.noAuth=此构建未被授权创建分支 '%s'
GenerateChecksumStep.name=生成文件校验和
GenerateChecksumStep.desc=此步骤只能由Docker相关执行器执行
GenerateChecksumStep.files.name=文件
GenerateChecksumStep.files.desc=指定要创建 md5 校验和的文件。多个文件应以空格分隔。接受<a href='https://www.linuxjournal.com/content/globstar-new-bash-globbing-option' target='_blank'>Globstar</a>模式。非绝对路径的文件被认为是相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的
GenerateChecksumStep.targetFile.name=目标文件
GenerateChecksumStep.targetFile.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的文件以写入校验和
CreateTagStep.name=创建标签
CreateTagStep.tag.name=标签名称
CreateTagStep.tag.desc=指定标签的名称
CreateTagStep.msg.name=标签消息
CreateTagStep.msg.desc=选填，指定标签的消息
CreateTagStep.ats.desc=对于默认分支不可访问的构建提交，应指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值为具有创建标签权限的访问令牌
CreateTagStep.error.invalidName=无效的标签名称：%s
CreateTagStep.error.noAuth=此构建未被授权创建标签 '%s'
PruneBuilderCacheStep.name=清理Docker构建器缓存
PruneBuilderCacheStep.desc=清理 Docker buildx 构建器的镜像缓存。Docker 构建器缓存是在构建 Docker 镜像时生成的中间层、临时文件和其他缓存数据，用于加速后续构建操作。然而，随着时间的推移，缓存可能会占用大量磁盘空间，因此需要通过清理操作释放存储资源。此步骤调用 docker builder prune 命令来清理 本地Docker执行器 或 远程Docker执行器中指定的buildx构建器的缓存
PruneBuilderCacheStep.options.desc=选填，为 docker builder prune 命令指定其他选项，例如：<code>--all</code> <code>--force</code>
PruneBuilderCacheStep.options.name=选项
PublishArtifactStep.name=构建产物
PublishArtifactStep.fromDir.name=来自哪个目录
PublishArtifactStep.fromDir.placeholder=任务工作空间
PublishArtifactStep.fromDir.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径以发布构建产物。留空以使用任务工作空间本身
PublishArtifactStep.artifacts.name=产物
PublishArtifactStep.artifacts.desc=指定上述目录下要发布的文件。使用 * 或 ? 进行模式匹配
PublishReportStep.report.name=报告名称
PublishReportStep.report.desc=指定要在构建详情页面显示的报告名称
PublishReportStep.filePatterns.name=文件模式
PublishReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的文件以发布。使用 * 或 ? 进行模式匹配
PublishSiteStep.name=站点
PublishSiteStep.desc=此步骤可以发布指定的文件作为项目网站通过互联网访问。项目网站的地址为 <code>http://&lt;server base URL&gt;/path/to/project/~site</code>
PublishSiteStep.project.placeholder=当前项目
PublishSiteStep.project.desc=选填，指定发布站点文件的项目。留空以发布到当前项目
PublishSiteStep.fromDir.name=来自哪个目录
PublishSiteStep.fromDir.placeholder=任务工作空间
PublishSiteStep.fromDir.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径以发布站点文件。留空以使用任务工作空间本身
PublishSiteStep.artifacts.name=构建产物
PublishSiteStep.artifacts.desc=指定上述目录下要发布的文件。使用 * 或 ? 进行模式匹配。<b>注意：</b>应包含<code>index.html</code>作为站点起始页面
PublishSiteStep.error.unableFindProject=无法找到项目：%s
PublishSiteStep.log.publishSuccess=站点发布成功，访问路径为 %s/%s/~site
PublishSiteStep.error.publishProhibited=当前任务执行器禁止发布站点
PullImageStep.name=拉取镜像
PullImageStep.desc=通过crane以OCI布局拉取Docker镜像。此步骤需要由本地Docker执行器、远程Docker执行器或Kubernetes执行器 执行
PullImageStep.srcImage.name=源Docker镜像
PullImageStep.srcImage.desc=指定要拉取的镜像标签，例如 your-registry-server:5000/your-org/repo:latest。如果要从内置镜像库拉取，请直接使用格式 @server@/<project path>/<repo name>:<tag name>。
PullImageStep.platform.name=平台
PullImageStep.platform.placeholder=镜像中的所有平台
PullImageStep.platform.desc=选填，指定要拉取的平台，例如<tt>linux/amd64</tt>。留空则拉取镜像中的所有平台
PullImageStep.moreOptions.desc=选填，指定<a href='https://github.com/google/go-containerregistry/blob/main/cmd/crane/doc/crane_pull.md' target='_blank'>crane的附加选项</a>
PullImageStep.moreOptions.name=更多选项
PullImageStep.error.notAllowed=不允许在推送镜像步骤中使用回环地址作为源Docker镜像，请改用IP地址或主机名
PushImageStep.name=推送镜像
PushImageStep.desc=通过crane从OCI布局推送Docker镜像。此步骤需要由本地Docker执行器、远程Docker执行器或Kubernetes执行器 执行
PushImageStep.srcPath.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的OCI布局目录路径作为推送来源
PushImageStep.destImage.name=目标Docker镜像
PushImageStep.destImage.desc=指定要推送到的镜像标签，例如 your-registry-server:5000/your-org/repo:latest。如果要推送到内置镜像库，请直接使用格式 @server@/<project path>/<repo name>:<tag name>。
PushImageStep.moreOptions.name=更多选项
PushImageStep.moreOptions.desc=选填，指定<a href='https://github.com/google/go-containerregistry/blob/main/cmd/crane/doc/crane_push.md' target='_blank'>crane的附加选项</a>
pushImageStep.error.notAllowed=不允许在推送镜像步骤中使用回环地址作为目标Docker镜像，请改用IP地址或主机名
RunContainerStep.name=运行Docker容器
RunContainerStep.desc=运行指定的Docker容器。<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>会挂载到容器中，其路径存放在环境变量<code>DEVGRIP_WORKSPACE</code>中。<b class='text-warning'>注意：</b>此步骤只能由本地Docker执行器或远程Docker执行器 执行
RunContainerStep.image.name=镜像
RunContainerStep.image.desc=指定要运行的容器镜像
RunContainerStep.args.name=参数
RunContainerStep.args.desc=选填，指定容器参数，用空格分隔。包含空格的单个参数应加引号。<b class='text-warning'>注意：</b>不要将此参数与在执行器设置中指定的容器选项混淆
RunContainerStep.wd.name=工作目录
RunContainerStep.wd.placeholder=容器默认目录
RunContainerStep.wd.desc=选填，指定容器的工作目录。留空则使用容器的默认工作目录
RunContainerStep.env.desc=选填，为容器指定环境变量
RunContainerStep.vm.name=卷挂载
RunContainerStep.vm.desc=选填，将任务工作空间下的目录或文件挂载到容器中
RunImagetoolsStep.name=运行 Buildx Image Tools
RunImagetoolsStep.desc=使用指定参数运行 Docker Buildx imagetools 命令。此步骤只能由本地Docker执行器 或 远程Docker执行器 执行
RunImagetoolsStep.args.name=参数
RunImagetoolsStep.args.desc=指定imagetools的参数。例如<code>create -t myorg/myrepo:1.0.0 myorg/myrepo@&lt;arm64 manifest digest&gt; myorg/myrepo@&lt;amd64 manifest digest&gt;</code>
SCPCommandStep.name=通过SCP复制文件
SCPCommandStep.desc=此步骤只能由Docker相关的执行器执行。运行在<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>下
SCPCommandStep.pks.name=私钥密钥
SCPCommandStep.pks.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，作为SSH身份验证的私钥。<b class='text-warning'>警告:</b> 不支持设置了密码的私钥
SCPCommandStep.source.name=源文件或目录
SCPCommandStep.source.desc=指定SCP命令的<code>source</code>参数，可以是本地路径或远程路径，例如：本地路径：<code>app.tar.gz</code>
SCPCommandStep.target.name=目标文件或目录
SCPCommandStep.target.desc=指定SCP命令的<code>target</code>参数，可以是本地路径或远程路径，例如：远程路径：<code>user@@host:/app</code>。<b class='text-info'>注意:</b> 请确保远程主机上scp命令已安装
SCPCommandStep.options.name=选项
SCPCommandStep.options.desc=选填，指定scp命令的选项。多个选项需要用空格分隔
SetBuildDescriptionStep.name=设置构建说明
SetBuildDescriptionStep.buildDesc.name=构建说明
SetBuildVersionStep.name=设置构建版本
SetBuildVersionStep.buildVersion.name=构建版本
SetupCacheStep.name=设置缓存
SetupCacheStep.desc=设置缓存以加快构建任务的执行。查看<a href='https://docs.devgrip.net/tutorials/cicd/job-cache' target='_blank'>教程</a>了解如何使用任务缓存
SetupCacheStep.key.name=缓存键
SetupCacheStep.key.desc=此键用于确定在项目层级中是否存在缓存命中。按顺序从当前项目搜索到根项目，如果找到与此处定义相同的缓存键，将视为命中缓存。
SetupCacheStep.loadKeys.name=二级缓存键
SetupCacheStep.loadKeys.desc=二级缓存同样可以加速项目构建，想象一下，项目中只有少数依赖更新了，其他依赖都没有变，这时若指定了二级缓存，只需要下载少数依赖。如果通过上述缓存键未命中缓存，系统会依次循环匹配这里定义的二级缓存键，直到在项目层级中找到匹配的缓存，如果多个缓存匹配，只返回最新的缓存。怎么算匹配呢？二级缓存键是上述缓存键的前缀，则视为匹配。例如：二级缓存为node_modules，而缓存键为node_modules_xxx 这种就视为匹配，<b>特别建议将二级缓存键和缓存键设置相同的前缀</b>。
SetupCacheStep.cachePaths.name=缓存路径
SetupCacheStep.cachePaths.desc=对于Docker相关的任务执行器，此路径位于容器内，并接受绝对路径和相对路径（相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>）。对于非docker相关的任务执行器，仅接受相对路径。
SetupCacheStep.uploadStrategy.name=上传策略
SetupCacheStep.uploadStrategy.desc=指定构建成功后缓存的上传策略。<var>如果未命中则上传</var>表示在通过缓存键未找到缓存时上传，而<var>文件被更改则上传</var>表示如果缓存路径中的某些文件发生更改，则上传
SetupCacheStep.cde.name=更改检测排除项
SetupCacheStep.cde.desc=选填，指定相对于缓存路径的文件，用于在检测缓存更改时忽略它们。使用‘**’，‘*’或‘?’进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。多个文件应以空格分隔，包含空格的单个文件应加引号
SetupCacheStep.uploadToProject.name=上传到项目
SetupCacheStep.uploadToProject.placeholder=当前项目
SetupCacheStep.uploadToProject.desc=如果需要上传缓存，此属性可以指定上传的目标项目。留空表示当前项目
SetupCacheStep.uploadAccessTokenSecret.name=访问令牌密钥
SetupCacheStep.uploadAccessTokenSecret.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值是具有上述项目的上传缓存权限的访问令牌。注意，如果上传到当前项目或子项目，且构建提交可从默认分支访问(即该提交在默认分支的历史记录中)，则不需要此属性
SSHCommandStep.name=通过SSH执行命令
SSHCommandStep.desc=此步骤只能由Docker相关的执行器执行
SSHCommandStep.remoteMachine.name=远程机器
SSHCommandStep.remoteMachine.desc=通过SSH运行命令的远程机器的主机名或IP地址
SSHCommandStep.username.name=用户名
SSHCommandStep.username.desc=指定上述机器的SSH身份验证用户名
SSHCommandStep.pks.name=私钥密钥
SSHCommandStep.pks.desc=指定一个<a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，作为上述用户的SSH身份验证私钥。<b class='text-warning'>警告:</b> 不支持设置了密码的私钥
SSHCommandStep.options.name=选项
SSHCommandStep.options.desc=选填，指定ssh命令的选项。多个选项需要用空格分隔
SSHCommandStep.commands.name=命令
SSHCommandStep.commands.desc=指定要在远程机器上执行的命令。<b class='text-warning'>注意：</b>用户环境不会在执行这些命令时被加载，如有必要，请在命令中显式设置
VolumeMount.sourcePath.name=源路径
VolumeMount.sourcePath.placeholder=任务工作空间
VolumeMount.sourcePath.desc=指定相对于任务工作空间的路径，作为挂载源。留空表示挂载任务工作空间本身
VolumeMount.targetPath.name=目标路径
VolumeMount.targetPath.desc=指定容器内的路径，作为挂载目标
PublishCheckstyleReportStep.name=Checkstyle 报告
File_Patterns=文件模式
PublishCheckstyleReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的 checkstyle result XML 文件，例如，<tt>target/checkstyle-result.xml</tt>。有关生成结果 XML 文件的详细信息，请参考文档 <a href='https://checkstyle.org/'>checkstyle</a>。使用 * 或 ? 进行模式匹配
PublishCheckstyleReportStep.tabWidth.name=缩进(tab)宽度
PublishCheckstyleReportStep.tabWidth.desc=指定缩进(tab)宽度，用于计算报告中出问题的代码具体位置的行号和列号。
PublishESLintReportStep.name=ESLint 报告
PublishESLintReportStep.filePatterns.desc=指定在<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>中的 checkstyle 格式的 ESLint 报告文件。此文件可以通过 ESLint 选项 <tt>'-f checkstyle'</tt> 和 <tt>'-o'</tt> 生成。使用 * 或 ? 进行模式匹配
PublishClippyReportStep.name=Clippy 报告
PublishClippyReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的 <a href='https://github.com/rust-lang/rust-clippy'>rust clippy</a> JSON 格式的文件。此文件可以通过 clippy 的 JSON 输出选项生成，例如 <code>cargo clippy --message-format json>check-result.json</code>。使用 * 或 ? 进行模式匹配
ProblemReportPage.filterTargets=筛选目标
ProblemReportPage.filterButton=筛选
ProblemReportPage.viewSource=查看源码
ProblemReportPage.notice=报告格式已更改。您可以重新运行此构建以生成新格式的报告
ProblemReportPage.hint1=包含空格或以短横线开头的目标需要加引号
ProblemReportPage.hint2=使用 '**'、'*' 或 '?' 进行路径<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'口头的将被排除
ProblemReportPage.tooManyProblems=问题过多，仅显示前${max}项
ProblemReportPage.line=行: 
ProblemReportPage.filterMalformed=筛选格式错误
ProblemStatsPage.title=代码问题统计
BuildMetricStatsPage.filterButton=筛选
BuildMetricStatsPage.filter=筛选...
BuildMetricStatsPage.malformedQuery=查询格式错误
CodeContribsPage.commits=提交
CodeContribsPage.additions=新增
CodeContribsPage.deletions=删除
CodeContribsPage.title=代码贡献统计
CodeContribsPage.noteLabel=对 ${branch} 分支的贡献，不包括合并提交
GitRole.author=作者
GitRole.committer=提交者
PersonCardPanel.systemAccount=<i>系统账号</i>
PersonCardPanel.noAccount=<i>无账号</i>
UserCardPanel.unknownAccount=<i>未知账号</>
CodeStatsPage.contributions=贡献
CodeStatsPage.sourceLines=源码行数
CodeStatsPage.title=代码统计
SourceLinesPage.title=代码行数统计
SourceLinesPage.slocTitle=${defaultBranch} 分支的代码总行数
SourceLinesPage.noDefaultBranchTitle=没有默认分支
PublishProblemReportStep.ft.name=失败阈值
PublishProblemReportStep.ft.desc=如果存在达到或超过指定级别的缺陷，则构建失败。注意，只有在构建未因其他步骤失败时，此选项才会生效。
PublishProblemReportStep.error=%s：发现严重性达到或超过 %s 级别的问题
PublishProblemReportStep.passed=代码已通过检测（检查报告已被忽略）
Publish_Log_UnableFindBlob=无法找到文件路径：%s
PublishJestCoverageReportStep.name=Jest 覆盖率报告
PublishJestCoverageReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 Jest 覆盖率报告文件（clover 格式），例如 <tt>coverage/clover.xml</tt>。此文件可通过 Jest 选项 <tt>'--coverage'</tt> 生成。使用 * 或 ? 进行模式匹配。
PublishCloverReportStep.name=Clover 覆盖率报告
PublishCloverReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 clover 覆盖率报告文件(XML格式)，例如 <tt>target/site/clover/clover.xml</tt>。有关如何生成 Clover XML 文件，请参阅<a href='https://openclover.org/documentation'>OpenClover 文档</a>。使用 * 或 ? 进行模式匹配。
Publish_Log_Processing=正在处理 %s 报告 '%s'...
Publish_Log_Ignored=忽略了 %s 报告 '%s'，因为它不是有效的 XML
PublishCoberturaReportStep.name=Cobertura 覆盖率报告
PublishCoberturaReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 Cobertura 覆盖率报告文件(XML格式)，例如 <tt>target/site/cobertura/coverage.xml</tt>。使用 * 或 ? 进行模式匹配。
CoverageStatsPage.title=覆盖率统计
CoverageStatsPage.hint1=包含空格或以短横线开头的名称需要加引号
CoverageStatsPage.overall=总体
CoverageStatsPage.files=文件
CoverageStatsPage.groups=组
CoverageStatsPage.filterFiles=筛选文件...
CoverageStatsPage.filterGroups=筛选组...
CoverageOrderBy.default=默认
CoverageOrderBy.leastBranchCoverage=最低分支覆盖率
CoverageOrderBy.mostBranchCoverage=最高分支覆盖率
CoverageOrderBy.leastLineCoverage=最低行覆盖率
CoverageOrderBy.mostLineCoverage=最高行覆盖率
CoveragePanel.lines=行
CoveragePanel.branches=分支
PublishCPDReportStep.name=CPD 报告
PublishCPDReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 CPD 结果 XML 文件，例如 <tt>target/cpd.xml</tt>。使用 * 或 ? 进行模式匹配。
PublishCPDReportStep.dupMsg=与 '%s' 重复，位于 <a href='%s'>第 %s - %s 行</a>
PublishCppcheckReportStep.name=Cppcheck 报告
PublishCppcheckReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 Cppcheck XML 结果文件。此文件可以通过 Cppcheck 的 XML 输出选项生成，例如 <code>cppcheck src --xml 2>check-result.xml</code>。使用 * 或 ? 进行模式匹配。
PublishGTestReportStep.name=Google Test 报告
PublishGTestReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 GoogleTest XML 结果文件。可以通过运行测试时设置环境变量 <tt>GTEST_OUTPUT</tt> 生成此报告，例如 <code>export GTEST_OUTPUT=&quot;xml:gtest-result.xml&quot;</code>。使用 * 或 ? 进行模式匹配。
PublishHtmlReportStep.name=Html 报告
PublishHtmlReportStep.startPage.name=起始页
PublishHtmlReportStep.startPage.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的报告起始页面，例如：api/index.html
PublishHtmlReportStep.startPageNotFound=未找到 HTML 报告起始页面：%s
PublishHtmlReportStep.noAuth=当前任务执行器禁止发布 HTML 报告
PublishJacocoReportStep.name=JaCoCo 覆盖率报告
PublishJacocoReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 JaCoCo 覆盖率 XML 报告文件，例如 <tt>target/site/jacoco/jacoco.xml</tt>。使用 * 或 ? 进行模式匹配。
PublishJestReportStep.name=Jest Test 报告
PublishJestReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 JSON 格式的 Jest 测试结果文件。此文件可以通过 Jest 选项 <tt>'--json'</tt> 和 <tt>'--outputFile'</tt> 生成。使用 * 或 ? 进行模式匹配。
PublishJUnitReportStep.name=JUnit 报告
PublishJUnitReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 XML 格式的 JUnit 测试结果文件，例如 <tt>target/surefire-reports/TEST-*.xml</tt>。使用 * 或 ? 进行模式匹配。
PublishMarkdownReportStep.name=Markdown 报告
PublishMarkdownReportStep.startPage.name=起始页
PublishMarkdownReportStep.startPage.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的报告起始页面，例如：<tt>manual/index.md</tt>
PublishMarkdownReportStep.pageNotFound=未找到 Markdown 报告起始页面：%s
PublishPullRequestMarkdownReportStep.name=拉取请求 Markdown 报告
PublishPullRequestMarkdownReportStep.desc=如果构建由拉取请求触发，则此报告将显示在拉取请求概览页面中。
PublishPullRequestMarkdownReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 Markdown 文件进行发布。
PublishMypyReportStep.name=Mypy 报告
PublishMypyReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 mypy 输出文件。此文件可通过重定向 mypy output<b>不带 '--pretty' 选项</b>生成，例如 <code>mypy --exclude=.git --exclude=.venv . > mypy-output</code>。使用 * 或 ? 进行模式匹配。
PublishPMDReportStep.name=PMD 报告
PublishPMDReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 PMD 结果 XML 文件，例如 <tt>target/pmd.xml</tt>。使用 * 或 ? 进行模式匹配。
PublishPylintReportStep.name=Pylint 报告
PublishPylintReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 pylint JSON 结果文件。此文件可以通过 pylint 的 JSON 输出格式选项生成，例如 <code>--exit-zero --output-format=json:pylint-result.json</code>。注意，pylint 命令不会因代码质量违规而失败，因为此步骤会根据配置的阈值使构建失败。使用 * 或 ? 进行模式匹配。
PublishRoslynatorReportStep.name=Roslynator 报告
PublishRoslynatorReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 XML 格式的 Roslynator 诊断输出文件。此文件可以通过<i>-o</i>选项生成。使用 * 或 ? 进行模式匹配。
PublishRuffReportStep.name=Ruff 报告
PublishRuffReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 Ruff JSON 结果文件。此文件可通过 ruff 的 JSON 输出格式选项生成，例如 <code>--exit-zero --output-format json --output-file ruff-result.json</code>。注意，ruff 命令不会因代码质量违规而失败，因为此步骤会根据配置的阈值使构建失败。使用 * 或 ? 进行模式匹配。
PublishSpotBugsReportStep.name=SpotBugs 报告
PublishSpotBugsReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 SpotBugs 结果 XML 文件，例如 <tt>target/spotbugsXml.xml</tt>。使用 * 或 ? 进行模式匹配。
PublishTRXReportStep.name=TRX 报告（.NET 单元测试）
PublishTRXReportStep.filePatterns.desc=指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作区</a>的 .NET TRX 测试结果文件，例如 <tt>TestResults/*.trx</tt>。使用 * 或 ? 进行模式匹配。
UnitTestCasesPage.filterByTestSuite=按测试套件筛选
UnitTestCasesPage.longestDurationFirst=按最长持续时间排序
UnitTestCasesPage.malformedTestSuiteFilter=测试套件筛选条件格式错误
UnitTestCasesPage.malformedNameFilter=名称筛选条件格式错误
UnitTestCasesPage.hint1=路径包含空格或以短横线开头需要加引号
UnitTestCasesPage.hint2=使用 '**'、'*' 或 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>路径通配符匹配</a>。使用 '-' 前缀来排除。
UnitTestReportPage.testCases=测试用例
UnitTestReportPage.testSuites=测试套件
UnitTestStatsPage.title=单元测试统计
UnitTestSuitesPage.showTestCasesTitle=显示此测试套件的测试用例
CallbackUrlPanel.callbackTips=将 DevGrip 注册到 OpenID 提供方时，需要此回调 URL。
PackSidePanel.publishBy=构建
PackSidePanel.publishAt=发布时间
PackSidePanel.totalSize=大小
PackSidePanel.fixedIssueSince=自上次提交后已关闭的问题...
PackSidePanel.changeSince=自上次提交后的变更...
PackListPanel.queryPlaceholder=查询/排序包和镜像
PackListPanel.howToPub=怎么发布
PackListPanel.followBelowToPublish=请按照下面的说明将包和镜像发布到项目里
PackListPanel.deleteSelectedConfirm=输入 <code>yes</code> 删除已选的包和镜像
PackListPanel.deleteSelectedTitle=请选择要删除的包和镜像
PackListPanel.deleteAllQueriedConfirm=输入 <code>yes</code> 删除所有查询到的包和镜像
PackListPanel.deleteAllQueriedTitle=查不到要删除的包和镜像
PackListPanel.countPackages=查到${count}个包和镜像
PackListPanel.colPackage=包和镜像
PackListPanel.colType=类型
PackListPanel.colLastPublished=最后发布时间
PackListPanel.colTotalSize=大小
PackListPanel.pleaseSwitchToPackakgesPage=请切换到特定项目的包和镜像页面查看说明。
OrderEditPanel.removeTitle=移除
OrderEditPanel.descending=降序
MavenHelpPanel.pomHelp=1. 在项目的 pom.xml 中使用以下仓库
MavenHelpPanel.settingsHelp=2. 如果你想从命令行进行部署，请将以下内容添加到 <code>$HOME/.m2/settings.xml</code>
MavenHelpPanel.commandHelp=3. 对于构建任务，使用自定义的 settings.xml 更为方便和灵活，您可以在命令步骤中通过以下代码：
MavenPackPanel.pomTips=1. 要使用此包，请在项目的 pom.xml 中添加以下内容
MavenPackPanel.settingsTips=2. 如果你想从命令行编译项目，也请将以下内容添加到 <code>$HOME/.m2/settings.xml</code>
MavenPackPanel.commandTips=3. 对于构建任务，使用自定义的 settings.xml 更为方便和灵活，您可以在命令步骤中通过以下代码：
MavenPackPanel.metadataNotFound=未找到插件元数据
MavenPackPanel.publishedFile=已发布
MavenPackPanel.size=大小
ContainerHelpPanel.login=登录内置的Docker镜像库
ContainerHelpPanel.loginPermission=登录用户需要对以下项目拥有包与镜像的写入权限
ContainerHelpPanel.pushImage=将镜像推送到指定项目下的镜像仓库
ContainerHelpPanel.pushImageViaCicd=您还可以通过在构建任务中添加一个构建Docker镜像步骤并配置内建镜像库的登录信息(访问令牌密钥)来完成。注意：该密钥必须具备镜像库写权限。
ContainerPackPanel.digest=摘要
ContainerPackPanel.pullCommand=拉取命令
ContainerPackPanel.osArch=操作系统/架构
ContainerPackPanel.archPullCommand=架构拉取命令
ContainerPackPanel.imageSize=镜像大小
ContainerPackPanel.imageManifest=镜像清单
ContainerPackPanel.imageLabels=镜像标签
ContainerPackPanel.layerCache=这是一个层缓存(Layer Cache)。要使用缓存，请在 docker buildx 命令中添加以下选项
ContainerPackPanel.manifest=清单
InsecureRegistryNotePanel.warning=警告:
InsecureRegistryNotePanel.viaHttpProtocol=您正通过 http 协议访问此服务器，请配置您的 Docker 守护进程或 buildx 构建器以使用不安全的docker镜像库，具体请参考<a href="https://docs.devgrip.net/tutorials/cicd/insecure-docker-registry" target="_blank">教程</a>
GemHelpPanel.addASource=编辑 <code>$HOME/.gem/credentials</code> 添加 gem源
GemHelpPanel.runCommand=确保访问令牌具有项目包和镜像写入权限。创建文件后，还需运行命令 <code>chmod 0600 $HOME/.gem/credentials</code>
GemHelpPanel.pushGem=将 gem 推送到上述gem源中
GemHelpPanel.pushGemForCicd=对于构建任务，在命令步骤中运行以下命令添加gem源
GemPackPanel.runBelow=运行以下命令安装此 gem
GemPackPanel.permission=确保该账户具有项目的包和镜像读取权限
GemPackPanel.pushGemForCicd=对于构建任务，按以下方式添加此 gem 到 Gemfile 中
GemPackPanel.resolveDependency=然后通过命令步骤解析依赖关系
GemPackPanel.gemInfo=Gem 信息
NpmHelpPanel.configureScope=配置仓库的使用范围
NpmHelpPanel.configureAuthToken=配置仓库的身份验证令牌
NpmHelpPanel.permission=确保访问令牌具有项目的包和镜像写入权限
NpmHelpPanel.publish=发布，在项目根目录下执行
NpmHelpPanel.publishViaCommand=对于构建任务，通过命令步骤运行以下命令来发布npm包
NpmPackPanel.runBelow=运行以下命令以安装此包
NpmPackPanel.permission=确保访问令牌具有项目的包和镜像读取权限
NpmPackPanel.runBelowForCicd=对于构建任务，通过命令步骤运行以下命令
NpmPackPanel.readme=readme
NugetHelpPanel.addSource=添加源
NugetHelpPanel.permission=确保用户具有项目的包和镜像的写入权限
NugetHelpPanel.push=将包推送到上述源
NugetHelpPanel.cicd=对于构建任务，通过命令步骤运行以下命令添加源
NugetPackPanel.runBelow=运行以下命令以使用此包
NugetPackPanel.permission=确保该账户具有项目的包和镜像读取权限
NugetPackPanel.cicd=对于构建任务，通过命令步骤运行以下命令添加源
NugetPackPanel.nuSpec=NuSpec 文件
PypiHelpPanel.editToAdd=编辑 <code>$HOME/.pypirc</code> 按以下方式添加包仓库
PypiHelpPanel.permission=确保用户具有项目的包和镜像的写入权限
PypiHelpPanel.thenUpload=使用 twine 将包上传到仓库
PypiHelpPanel.cicd=对于构建任务，通过命令步骤运行以下命令添加包仓库
PypiPackPanel.runBelow=运行以下命令安装此包
PypiPackPanel.permission=确保账户具有项目的包和镜像读取权限
PypiPackPanel.cicd=对于构建任务，将此包添加到 requirements.txt 中，并通过命令步骤运行以下命令安装此包
PypiPackPanel.metaInfo=元数据
HelmHelpPanel.pushToRepo=推送chart到仓库中
HelmHelpPanel.writePermission=确保该账号具有项目的包与镜像写权限
HelmHelpPanel.cicdToPush=对于构建任务，通过命令步骤运行以下命令将chart推送到仓库中
HelmPackPanel.runBelow=运行以下命令来使用该chart
HelmPackPanel.readPermission=确保该账号具有项目的包与镜像读权限
HelmPackPanel.cicdToInstall=对于构建任务，通过命令步骤运行以下命令来安装chart
HelmPackPanel.metadata=元数据
ColorPicker.btnSave=保存
ColorPicker.btnClear=清除
BlobTextDiffPanel.invalidSelection=无效的选择，点击查看详情
BlobTextDiffPanel.unableToCommentHere=无法在这评论
BlobTextDiffPanel.permanentLink=为选中内容生成固定链接并应用到浏览器地址栏
BlobTextDiffPanel.copySelectedText=复制选中的内容到剪贴板
BlobTextDiffPanel.addCommentOnThisSelection=在选中的内容上添加评论
BlobTextDiffPanel.loginToComment=先登录再评论
BlobTextDiffPanel.showCommentOfMarkedTextTitle=点击显示标记文本的评论
BlobTextDiffPanel.coveredByTestsTitle=已被测试覆盖
BlobTextDiffPanel.notCoveredByAnyTestTitle=未被任何测试覆盖
BlobTextDiffPanel.partiallyCoveredBySomeTestsTitle=部分被某些测试覆盖
BlobTextDiffPanel.loading=正在加载...
BlobTextDiffPanel.thereAreUnsavedChanges=有未保存的更改，确定要丢弃并继续吗？
MarkdownEditor.commandsHint=按'cmd-/'或'ctrl-/'键显示命令
MarkdownEditor.outdated=评论过的代码已失效
MarkdownEditor.suggestOnMac=修改建议(cmd-g)
MarkdownEditor.suggestOnWin=修改建议(ctrl-g)
MarkdownEditor.suggestionIsOutdated=该建议已失效，可能是由于代码变更或拉取请求已关闭
MarkdownEditor.removeFromBatch=从批量建议中移除
MarkdownEditor.commitSuggestion=提交该建议
MarkdownEditor.addToBatchToCommit=添加到批量建议中，稍后与其他修改建议一起提交
MarkdownEditor.loading=正在加载...
MarkdownEditor.issueTooltip.noIssueFound=问题不存在或无权访问
MarkdownEditor.prTooltip.noPrFound=拉取请求不存在或无权访问
MarkdownEditor.buildTooltip.noBuildFound=构建不存在或无权访问
MarkdownEditor.commitTooltip.commitBuildFound=提交不存在或无权访问
CodeProcessor.suggestedCodeDiv=<div class='pb-2 mb-2 head font-size-xs mx-n2 px-2'>修改建议</div>
CodeProcessor.suggestedCode=<p><i>修改建议</i></p>
SuggestionBatchApplyBean.name=批量提交修改建议
SuggestionBatchApplyBean.defaultCommitMessage=应用代码评论中的修改建议
SuggestionApplyBean.name=提交修改建议
SuggestionApplyBean.defaultCommitMessage=应用代码评论中的修改建议
SuggestionApplyBean.branch.desc=指定提交修改建议的分支
SuggestionApplyBean.branch.name=分支
SuggestionApplyBean.commitMessage.name=提交说明
SuggestionApplyModalPanel.codeCommentNote=修改建议已应用
SuggestionApplyModalPanel.branchWasUpdate=刚刚有其他人更新了该分支，请重试
SuggestionApplyModalPanel.applyDisallowed=分支保护规则不允许应用修改建议
NewRolePage.createSuccess=角色创建成功
RoleProjectOwner=项目管理者
RoleCodeReader=代码读取者
RoleCodeWriter=代码写入者
RoleIssueReporter=问题报告者
RoleIssueManager=问题管理者
RolePackageReader=包与镜像读取者
RolePackageWriter=包与镜像写入者
SuggestionUtils.filesWithExt=扩展名是'%s'的文件
SuggestionUtils.anExampleBranch=示例
SuggestionUtils.anExampleBuild=使用项目路径的示例
SuggestionUtils.anExampleBuildWithProjectKey=使用项目简写的示例
SuggestionUtils.anExamplePullRequest=使用项目路径的示例
SuggestionUtils.anExamplePullRequestWithProjectKey=使用项目简写的示例
SuggestionUtils.anExampleIssue=使用项目路径的示例
SuggestionUtils.anExampleIssueWithProjectKey=使用项目简写的示例
SuggestionUtils.anExampleRevision=示例
SuggestionUtils.anExampleTag=示例
SourceFormatPanel.indentType.spaces=空格
SourceFormatPanel.indentType.tabs=缩进
SourceFormatPanel.lineWrapMode.soft=软换行
SourceFormatPanel.lineWrapMode.no=不换行
SourceFormatPanel.indentType.label=缩进类型
SourceFormatPanel.tabSize.label=缩进大小
SourceFormatPanel.lineWrapMode.label=换行模式
BuildTerminalPage.pageTilte=web终端
TimesheetsPage.noTimesheets=您还没有创建过工时统计表
TimesheetsPage.addNewTimesheet=创建工时统计表
TimesheetsPage.default=默认
TimesheetsPage.inherited=继承
TimesheetsPage.firstWillBeDefault=第一个创建的工时统计表将作为默认的工时统计表
TimesheetsPage.export=导出
TimesheetsPage.canNotBeSorted=继承的工时统计表无法排序
TimesheetsPage.deleteConfirm=您确定要删除工时统计表吗？
TimesheetsPage.editTimesheet=编辑工时统计表
TimesheetsPage.addTimesheet=创建工时统计表
TimesheetPanel.totalTime=总时间
TimesheetPanel.user=用户
TimesheetPanel.spent=已花费
TimesheetPanel.issue=问题
TimesheetSetting.filterIssues.name=筛选问题
TimesheetSetting.filterIssues.placeholder=所有问题
TimesheetSetting.showWorksOf.name=统计维度
TimesheetSetting.timeRange.name=时间范围
TimesheetSetting.groupBy.name=分组
TimesheetSetting.groupBy.placeholder=不分组
DateRangeNavigator.prev=往前
DateRangeNavigator.next=往后
DateRangeNavigator.thisWeek=本周
DateRangeNavigator.thisMonth=本月
DateRangeNavigator.toAvoidDuplication=为避免重复，这里显示的花费时间不包括从 ‘${aggregationLink}’ 汇总的数据。
BuildDurationStatsPage.help=该页面展示了这段时间内构建任务排队和运行的平均时长
BuildDurationStatsPanel.runningLabel=运行时间
BuildDurationStatsPanel.pendingLabel=排队时间
BuildFrequencyStatsPage.help=该页面展示了这段时间内已完成的构建数量
BuildFrequencyStatsPage.frequencyLabel=完成次数
BuildListStatsPanel.stats=统计
BuildListStatsPanel.statsDesc=构建统计是企业版功能，您可以<a href='https://devgrip.net/pricing' target='_blank'>免费试用</a>30 天
ProjectDashboardPage.dashboard=仪表台
BuildStatsPage.buildStatsTitle=构建统计
Stats.durationsLabel=时长
Stats.frequenciesLabel=频次
EntityStatsPage.filterTitle=查询
EntityStatsPage.monthDisplay=近${num}个月
IssueStateTrendStatsPage.help=该页面展示各个状态下的任务数量随时间的变化，帮助团队快速识别瓶颈，优化任务分配、改善工作流程。
IssueListStatsPanel.statsDesc=问题统计是企业版功能，您可以<a href='https://devgrip.net/pricing' target='_blank'>免费试用</a>30 天
IssueStatsPage.prStatsTitle=问题统计
IssueStatsPage.stateTrend=累积流图
DisplayMonthsEditBean.name=展示几个月
OsvVulnerScannerStep.name=OSV 漏洞扫描器
OsvVulnerScannerStep.desc=此步骤运行 OSV 扫描器扫描<a href='https://google.github.io/osv-scanner/supported-languages-and-lockfiles/' target='_blank'>各种编程语言和依赖项</a>中的漏洞。该步骤只支持 Docker 相关的执行器。
OsvVulnerScannerStep.configPath.name=配置文件
OsvVulnerScannerStep.configPath.placeholder=无配置文件
OsvVulnerScannerStep.configPath.desc=选填，在<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>中指定 OSV 扫描器的<a href='https://google.github.io/osv-scanner/configuration/' target='_blank'>配置文件</a>。您可以通过此文件忽略特定漏洞。
OsvScanStep.scanPaths.name=扫描路径
OsvScanStep.scanPaths.placeholder=任务工作空间
OsvScanStep.scanPaths.desc=选填，指定一个相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径以扫描依赖项漏洞。可以指定多个路径，用空格分隔。留空将使用任务工作空间本身。
OsvScanStep.recursive.name=递归
OsvScanStep.recursive.desc=是否在上述路径中递归扫描
OsvScanStep.ft.name=失败阈值
OsvScanStep.ft.desc=如果存在指定严重性级别或更严重的漏洞，则构建失败
OsvScanStep.reportName.name=报告名称
OsvScanStep.reportName.desc=指定将在构建详情页面显示的报告名称
NoSubscriptionLog=该步骤需要企业版订阅。访问 https://devgrip.net/pricing 获取 30 天试用许可证
OsvLicenseScannerStep.name=OSV 许可证扫描器
OsvLicenseScannerStep.desc=此步骤运行 OSV 扫描器以扫描各种<a href='https://deps.dev/' target='_blank'>依赖项</a>中使用的违规许可证。该步骤只支持 Docker 相关的执行器。
OsvLicenseScannerStep.al.name=允许的许可证
OsvLicenseScannerStep.al.desc=指定允许的<a href='https://spdx.org/licenses/' target='_blank'>SPDX 许可证标识符</a>，<span class='text-warning'>中间用逗号分隔</span>
FSScannerStep.name=Trivy 文件系统扫描器
FSScannerStep.desc=该步骤运行 Trivy 文件系统扫描器以扫描各种<a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>各种编程语言和依赖项</a>。该步骤只支持 Docker 相关的执行器，建议在依赖项被解析（如 npm install）后运行。与 OSV 扫描器相比，其设置稍复杂，但结果更精确。
DescWithNoSubscription=<br><br><b class='text-danger'>注意：</b> 该步骤需要企业订阅。<a href='https://devgrip.net/pricing' target='_blank'>您可以免费试用</a> 30 天
FSScannerStep.scanPath.name=扫描路径
FSScannerStep.scanPath.placeholder=任务工作空间
FSScannerStep.scanPath.desc=选填，指定一个相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径进行扫描。留空将使用任务工作空间本身。
ImageScannerStep.name=Trivy 容器镜像扫描器
ImageScannerStep.desc=该步骤运行 Trivy 容器镜像扫描器以查找指定镜像中的问题。对于漏洞，它会检查各种编程语言的<a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>分发文件</a>，如：java的jar文件，ruby的gem等。该步骤只支持 Docker 相关的执行器。
ImageScannerStep.scanPath.name=OCI 布局目录
ImageScannerStep.scanPath.desc=指定要扫描的镜像的 OCI 布局目录。此目录可以通过构建镜像步骤或拉取镜像步骤生成。该目录应为一个相对于<a href='https://docs.devgrip.net/concepts#job-workspace' target='_blank'>任务工作空间</a>的相对目录。
ImageScannerStep.platforms.name=平台
ImageScannerStep.platforms.placeholder=OCI 布局中的所有平台
ImageScannerStep.platforms.desc=选填，指定平台进行扫描，多个用<span class='text-info'>逗号分隔</span>，例如 <tt>linux/amd64,linux/arm64</tt>。留空以扫描 OCI 布局中的所有平台。
LicenseSetting.ignoreLic.name=忽略的许可证
LicenseSetting.ignoreLic.placeholder=无
LicenseSetting.ignoreLic.desc=选填，指定要忽略许可证，中间使用逗号分隔
RootFSScannerStep.name=Trivy Rootfs 扫描器
RootFSScannerStep.desc=此步骤运行 Trivy Rootfs 扫描器以扫描各种编程语言的<a href='https://aquasecurity.github.io/trivy/v0.50/docs/coverage/language/#supported-languages' target='_blank'>分发文件</a>。该步骤只支持 Docker 相关的执行器，建议项目测试、验证、预发等环境中运行。
RootFSScannerStep.scanPath.name=扫描路径
RootFSScannerStep.scanPath.placeholder=任务工作空间
RootFSScannerStep.scanPath.desc=选填，指定相对于<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>的路径进行扫描。留空将使用任务工作空间本身。
SecretSetting.configPath.name=机密配置文件
SecretSetting.configPath.placeholder=无机密配置
SecretSetting.configPath.desc=选填，指定<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>中用于 Trivy <a href='https://aquasecurity.github.io/trivy/v0.50/docs/scanner/secret/#configuration' target='_blank'>机密配置</a>的相对路径。
TrivyCacheStep.name=设置 Trivy 缓存
TrivyCacheStep.desc=该步骤设置 Trivy 数据库缓存以加速各种 Trivy 扫描步骤
TrivyCacheStep.key.name=缓存键
TrivyCacheStep.key.desc=此键用于在项目层次结构中下载和上传缓存（按从当前项目到根项目的顺序搜索）。
TrivyCacheStep.uploadProjectPath.name=上传项目路径
TrivyCacheStep.uploadProjectPath.placeholder=当前项目
TrivyCacheStep.uploadProjectPath.desc=如果需要上传缓存，此属性指定上传的目标项目。留空表示当前项目。
TrivyCacheStep.uploadAts.name=上传访问令牌密钥
TrivyCacheStep.uploadAts.desc=指定一个任务密钥，其值为具有上述项目上传缓存权限的访问令牌。注意，如果上传缓存到当前或子项目且构建提交可从默认分支访问，则此属性不是必需的。
TrivyScanStep.dv.name=漏洞检测
TrivyScanStep.ds.name=机密检测
TrivyScanStep.dl.name=许可证检测
TrivyScanStep.skipDirs.name=要跳过的目录
TrivyScanStep.skipDirs.placeholder=无
TrivyScanStep.skipDirs.desc=选填，指定扫描路径中要跳过的目录或一组文件或目录的匹配模式，如logs/*.log **/node_modules/**。多个以空格分隔。
TrivyScanStep.ignoreFile.name=忽略文件
TrivyScanStep.ignoreFile.placeholder=无
TrivyScanStep.ignoreFile.desc=选填，指定<a href='https://docs.devgrip.net/concepts#job-workspace'>任务工作空间</a>中的相对路径，作为 Trivy <a href='https://aquasecurity.github.io/trivy/v0.50/docs/configuration/filtering/#by-finding-ids' target='_blank'>忽略文件</a>。
TrivyScanStep.ft.name=失败阈值
TrivyScanStep.ft.desc=如果存在指定严重性级别或更严重的漏洞，则构建失败。
TrivyScanStep.reportName.name=报告名称
TrivyScanStep.reportName.desc=指定将在构建详情页面显示的报告名称。
RenovateStep.name=通过 Renovate 更新依赖
RenovateStep.desc=通过拉取请求，运行 <a href='https://github.com/renovatebot/renovate' target='_blank'>renovate CLI</a> 来达到更新依赖的目的
RenovateStep.projects.name=项目
RenovateStep.projects.placeholder=当前项目
RenovateStep.projects.desc=指定要更新依赖的项目。留空则默认使用当前项目
RenovateStep.includeChildProjects.name=包含子项目
RenovateStep.includeChildProjects.desc=是否同时包含上述项目的子项目
RenovateStep.ats.name=访问令牌密钥
RenovateStep.ats.desc=指定 <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，其值为具有对上述项目代码写入权限的访问令牌。提交、问题和拉取请求也将以访问令牌所有者的名义创建
RenovateStep.ics.name=问题关闭状态
RenovateStep.ics.desc=指定哪些状态可以被Renovate视为关闭状态。此外，当 Renovate 关闭问题时，DevGrip 会将问题转换为此处指定的第一个状态
RenovateStep.issueFields.name=问题字段
RenovateStep.issueFields.desc=指定 Renovate 创建的各种问题时可使用的字段
RenovateStep.dms.name=合并策略
RenovateStep.dms.placeholder=默认合并策略
RenovateStep.dms.desc=选填，指定创建的拉取请求的合并策略。留空则使用各项目的默认策略
RenovateStep.rjsc.name=Renovate JavaScript 配置
RenovateStep.rjsc.desc=选填，指定由 Renovate CLI 使用的 JavaScript 配置
RenovateStep.ghats.name=GitHub 访问令牌密钥
RenovateStep.ghats.desc=选填，指定 <a href='https://docs.devgrip.net/tutorials/cicd/job-secrets' target='_blank'>任务密钥</a>，作为 GitHub 访问令牌。将用于检索托管在 GitHub 上的依赖项的发布说明，经过身份验证后，GitHub不再限制访问速率
RenovateStep.cliOpt.name=Renovate CLI 选项
RenovateStep.cliOpt.desc=选填，指定传递给 renovate CLI 的选项。多个选项应以空格分隔，包含空格的单个选项应加引号
RenovateStep.error.unableFindField=找不到问题字段: %s
RenovateStep.error.unableFindIssueState=找不到问题状态: %s
RenovateStep.error.noVerifiedEmail=没有经过验证的邮件: %s
RenovateStep.error.noCloseStates=请指定问题关闭的状态
RenovateStep.error.noIssueFields=请指定问题字段
RenovateCacheStep.name=设置 Renovate 缓存
RenovateCacheStep.desc=此步骤设置 Renovate 缓存。如果需要使用，请将其放在 Renovate 步骤之前
EntityQuery.error.operator=字段 '%s' 不适用于操作符 '%s'
EntitytQuery.error.fieldNotFound=无效的字段: %s
EntitytQuery.error.canNotOrderBy=无法根据该字段排序: %s
EntitytQuery.error.unableFindBuild=无法找到构建: %s
EntitytQuery.error.invalidField=无效的字段: %s
EntitytQuery.error.projectQueryNotSupport=这里不支持项目查询
EntitytQuery.error.jobQueryNotSupport=这里不支持任务查询
EntitytQuery.error.unableFindGroup=无法找到该用户组: %s
EntitytQuery.error.unableFindUser=无法找到该用户: %s
EntitytQuery.error.groupIsIncludeMultipeTimes=用户组 '%s' 被包含了多次
EntitytQuery.error.userIsIncludeMultipeTimes=用户 '%s' 被包含了多次
EntitytQuery.error.malformed=查询语法格式错误
DingtalkNotificationSetting.name=钉钉通知设置
DingtalkNotificationSetting.desc=设置钉钉通知。这些设置将会被子项目所继承，并且可以使用相同webhook url来覆盖父项目的设置。钉钉通知为了确保使用过程的安全性，提供三种保护措施，这些措施包括：设置自定义关键词、加签（使用签名加密）和 IP 地址（段），具体请参考<a href="https://open.dingtalk.com/document/robots/customize-robot-security-settings" target="_blank">这里</a>。其中设置ip地址段这种模式仅需在钉钉端设置，无需在DevGrip设置，而另外两种模式需要在钉钉侧和DevGrip侧共同设置才能生效。
DingtalkNotificationSetting.needSign.name=加签
DingtalkNotificationSetting.needSign.desc=是否使用加签的方式，如果你在钉钉侧配置了加签的方式，则此处应该启用。
DingtalkNotificationSetting.secret.name=密钥
DingtalkNotificationSetting.secret.desc=指定钉钉侧加签使用的密钥。该密钥是一串以“SEC”开头的字符串，显示在钉钉的机器人配置页面/安全设置/加签下。<b class='text-danger'>注意: </b><b>请确保这里配置的值和钉钉侧的值相同，否则通知可能无法正常发送</b>
DingtalkNotificationSetting.needKeyword.name=自定义关键词
DingtalkNotificationSetting.needKeyword.desc=是否使用自定义关键字的方式，如果你在钉钉侧配置了自定义关键词的方式，则此处应该启用。<b class='text-danger'>注意: </b><b>请确保这里配置的关键词也在钉钉侧配置了，否则通知可能无法正常发送</b>
DingtalkNotificationSetting.keyword.name=关键词
DingtalkNotificationSetting.keyword.desc=指定钉钉侧设置的自定义关键词。如果钉钉侧定义了多个关键词，你只需要选择一个配置在这里即可。
ClickForMoreDetails=点此查看更多详情
ChannelNotification.webhookUrl.name=Webhook Url 地址
ChannelNotification.webhookUrl.desc=请指定 webhook url 用以发送相关事件的通知
ChannelNotification.notifyIssueEvents.name=通知问题相关事件
ChannelNotification.applicableIssues.name=适用于哪些问题
ChannelNotification.notifyPrEvents.name=通知拉取请求相关事件
ChannelNotification.applicablePr.name=适用于哪些拉取请求
ChannelNotification.notifyBuildEvents.name=通知构建相关事件
ChannelNotification.applicableBuilds.name=适用于哪些构建
ChannelNotification.notifyCodePushEvents.name=通知代码推送相关事件
ChannelNotification.applicableCommits.name=适用于哪些提交
ChannelNotification.notifyCodeCommentEvents.name=通知代码评论相关事件
ChannelNotification.applicableCodeComments.name=适用于哪些代码评论
DiscordNotificationSetting.name=Discord 通知设置
DiscordNotificationSetting.desc=设置 discord 通知。这些设置将会被子项目所继承，并且可以使用相同webhook url来覆盖父项目的设置
NtfyNotificationSetting.name=Ntfy.sh 通知设置
NtfyNotificationSetting.desc=设置 ntfy.sh 通知。这些设置将会被子项目所继承，并且可以使用相同webhook url来覆盖父项目的设置
SlackNotificationSetting.name=Slack 通知设置
SlackNotificationSetting.desc=设置 slack 通知。这些设置将会被子项目所继承，并且可以使用相同webhook url来覆盖父项目的设置
WecomNotificationSetting.name=企业微信通知设置
WecomNotificationSetting.desc=设置企业微信通知。这些设置将会被子项目所继承，并且可以使用相同webhook url来覆盖父项目的设置
WecomNotificationSetting.imageNotSupport=不支持图片格式
ChannelNotificationManager.issueSummary=[问题 %s] (%s)
ChannelNotificationManager.prSummary=[拉取请求 %s] (%s)
ChannelNotificationManager.buildTitleWithVersion=[构建 %s] (%s: %s) %s
ChannelNotificationManager.buildTitleWithoutVersion=[构建 %s] (%s) %s
ChannelNotificationManager.packTitle=包或镜像 [%s %s] 已发布
ChannelNotificationManager.commitTitle=[提交 %s:%s] (%s) %s
ChannelNotificationManager.codeCommentTitle=[代码评论 %s:%s] %s %s
RefUpdated.activity.branch=分支 '%s' 已更新
RefUpdated.activity.tag=git标签 '%s' 已创建
RefUpdated.activity.ref=git引用 '%s' 已更新
IssueNotificationManager.issueYou=[问题 %s] (在字段 %s 指定了你) %s
IssueNotificationManager.mentionYou=[问题 %s] (提到你) %s
IssueNotificationManager.issueBcc=[问题 %s] (%s) %s
IssueNotificationManager.issueBccOpened=已开启
IssueNotificationManager.issueBccUpdated=已更新
IssueNotificationManager.issueBccMoved=已移动
IssueNotificationManager.prevProject=上一个项目: %s
IssueNotificationManager.currentProject=当前项目: %s
PullRequestNotificationManager.prBcc=[拉取请求 %s] (%s) %s
PullRequestNotificationManager.prAssigned=[拉取请求 %s] (已指派) %s
PullRequestNotificationManager.assignedToYou1=指派给你
PullRequestNotificationManager.assignedToYou2=指派给你
PullRequestNotificationManager.prReviewRequest=[拉取请求 %s] (请求评审) %s
PullRequestNotificationManager.requestedReviewFromYou1=请你评审
PullRequestNotificationManager.requestedReviewFromYou2=请你评审
PullRequestNotificationManager.prMentioned=[拉取请求 %s] (提到你) %s
PullRequestNotificationManager.bccOpened=已开启
PullRequestNotificationManager.bccUpdated=已更新
PackNotificationManager.published=包或镜像 [%s %s] 已发布
PackNotificationManager.publishedByUser=包或镜像已由 %s 发布
PackNotificationManager.publishedViaBuild=包或镜像已由构建 %s 发布
CommitNotificationManager.summary=提交作者 %s
CodeCommentNotificationManager.title=[代码评论 %s:%s] %s
BuildNotificationManager.subjectWithVersion=[构建 %s] (%s: %s) %s
BuildNotificationManager.subjectWithoutVersion=[构建 %s] (%s) %s
BuildNotificationManager.summaryBranch=构建%s [分支为 %s]
BuildNotificationManager.summaryTag=构建%s [git标签为 %s]
BuildNotificationManager.summaryPr=构建%s [拉取请求为 %s]
BuildNotificationManager.summaryRef=构建%s [git引用为 %s]
IssueWorkBean.name=记录工作
IssueWorkBean.addSpentTime.name=添加花费时间
IssueWorkBean.addSpentTime.desc=仅为<b class='text-warning'>此问题</b>添加花费时间，不计入 '%s'
IssueWorkBean.startAt.name=开始时间
IssueWorkBean.startAt.desc=此工作开始的时间
IssueWorkBean.note.name=备注
IssueWorkBean.note.desc=选填，请添加备注
PackDetailPage.entityName=包或镜像
PackDetailPage.deleteConfirm=确定要删除吗?
PackDetailPage.deleteSuccess=包或镜像 ${name} 删除成功
Stats.countAndStatsLabel=${count}个${status}
PackStats.countAndTypeLabel=${count}个 ${type}
ProjectSelector.defaultQuery=首选
VerticalBeanListPropertyEditor.mustNotBeNull=不能为空
DefaultIssueChangeManager.stateChangeAsIssueTransited=问题状态已更新，原因是问题状态从 %s 转换为 '%s'
DefaultIssueChangeManager.stateChangeAsBuildIsSuccessful=问题状态已更新，原因是该构建 %s 已成功
DefaultIssueChangeManager.stateChangeAsPullRequestIs=问题状态已更新，原因是该拉取请求 %s %s
DefaultIssueChangeManager.stateChangeAsCodeFixing=问题状态已更新，原因是该提交（%s）修复了问题
DisplayIssueFields.state=状态
DisplayIssueFields.iteration=迭代
DisplayGroupBy.project=项目
ReactionListPanel.displayAndMore=及其他${count}个
ReactionListPanel.addReactionTitle=使用表情回应
ReactionListPanel.thumbsUp=拇指向上，表示同意
ReactionListPanel.thumbsDown=拇指向下，表示反对
ReactionListPanel.smile=笑脸
ReactionListPanel.tada=庆祝，通常用于某个值得纪念的时刻，如迭代完成
ReactionListPanel.confused=困惑脸，表示不确定、不清楚、不知道
ReactionListPanel.heart=爱心，表示喜欢
ReactionListPanel.rocket=火箭，表示激动、兴奋、重大成就、重大突破
ReactionListPanel.eyes=眼睛，表示会持续关注
BasePage.eeContent=企业版
ModalPanel.closeConfirm=此次更改还未保存，确定要关闭吗？
DefaultUserInvitationManager.subject=[邀请]您已受邀使用DevGrip
PagingNavigator.first=第一页
PagingNavigator.previous=前一页
PagingNavigator.next=后一页
PagingNavigator.last=最后一页
PagingNavigation.page=第${page}页
IssueBatchUpdateData.state=状态
IssueBatchUpdateData.confidential=是否私密
IssueBatchUpdateData.iterations=迭代
LayoutPage.collapseOrExpand=折叠或展开侧边栏
Usage.inusagePrefix=%s正在被这些地方使用:
LinkSide.linkNotFound=无效的问题关联: %s
Usage.applicableJob=有适用的构建任务
BitbucketCloud.BitbucketProjectImporter.step1.title=Bitbucket Cloud 认证
BitbucketCloud.BitbucketProjectImporter.step2.title=指定仓库
BitbucketCloud.BitbucketProjectImporter.step3.title=指定导入选项
BitbucketCloud.ImportOption.publicRoleName.name=公共角色
BitbucketCloud.ImportOption.publicRoleName.desc=如果指定，从 Bitbucket 导入的所有公共仓库将使用此默认角色。私有仓库不受影响
BitbucketCloud.ImportRepositories.parent.name=父项目
BitbucketCloud.ImportRepositories.parent.desc=选填，指定一个 DevGrip 项目作为导入仓库的父项目。如果留空，将作为根项目导入
BitbucketCloud.ImportRepositories.all.name=导入所有仓库
BitbucketCloud.ImportRepositories.includeForks.name=包括分叉项目
BitbucketCloud.ImportRepositories.includeForks.desc=是否要导入分叉项目
BitbucketCloud.ImportRepositories.bitbucktRepoToImport.name=要导入的 Bitbucket 仓库
BitbucketCloud.ImportServer.userName.name=Bitbucket 账户名
BitbucketCloud.ImportServer.userName.desc=你的 Bitbucket 账户用户名(不是email)
BitbucketCloud.ImportServer.appPassword.name=Bitbucket 应用密码
BitbucketCloud.ImportServer.appPassword.desc=Bitbucket 应用密码应该使用 <b>account/read</b>, <b>repositories/read</b>, <b>issues:read</b> 这些权限生成
BitbucketCloud.importWorkspace.workspace.name=Bitbucket工作空间
BitbucketCloud.importWorkspace.workspace.desc=选择要导入的工作空间
BitbucketCloud.importWorkspace.includeForks.name=包含分支
BitbucketCloud.importWorkspace.includeForks.desc=是否包含分支仓库
UnitTestModule.title=单元测试
CoverageModule.title=覆盖率
UnitTestMetric.group.successRate=成功率
UnitTestMetric.group.totalNumber=总数量
UnitTestMetric.group.totalTestDuration=测试总时长
UnitTestMetric.name.testCase=测试用例
UnitTestMetric.name.testSuite=测试套件
UnitTestMetric.name.totalTestDuration=总时长
ProblemMetric.group.totalProbelms=总问题数
ProblemMetric.name.criticalSeverity=严重
ProblemMetric.name.highSeverity=高
ProblemMetric.name.mediumSeverity=中
ProblemMetric.name.lowSeverity=低
CoverageMetric.group.codeCoverage=代码覆盖率
CoverageMetric.name.branch=分支
CoverageMetric.name.line=行
Nodata.label=暂无数据
Commits.label=提交
GlobalWithI=<i>全局</i>
QueryWatchesPanel.deleteSelected=删除所选
QueryWatchesPanel.selectToDelete=请选择要删除的查询关注
QueryWatchesPanel.selectToDeleteConfirm=您确定要删除所选的查询关注吗？
QueryWatchesPanel.statusWatched=正在关注
QueryWatchesPanel.statusIgnore=已忽略
QueryWatchesPanel.suffixCommon=(公共的)
QueryWatchesPanel.suffixPersonal=(我的)
IssueQueryWatchesPanel.desc=问题查询关注仅影响新问题。要批量管理现有问题的关注状态，请在问题页面按关注状态筛选问题，然后采取相应的操作。
QueryWatchesPanel.watchStatus=关注状态
PrQueryWatchesPanel.desc=拉取请求查询关注仅影响新的拉取请求。要批量管理现有拉取请求的关注状态，请在拉取请求页面按关注状态筛选拉取请求，然后采取相应的操作。
QueryWatchesPanel.issue=问题
QueryWatchesPanel.build=构建
QueryWatchesPanel.pr=拉取请求
QueryWatchesPanel.pack=包与镜像
QueryWatchesPanel.commit=提交
ProjectPage.noSavedQueries=无快捷查询边栏
ProjectPage.keepOpen=点此使快捷查询边栏保持展开
ProjectPage.hideAuto=点此使快捷查询边栏自动隐藏
IssueImportPage.desc=您可以将问题列表导入当前项目中，请注意，为避免问题编号重复，只有在整个项目中没有任何问题时，问题编号才会被保留。
IssueImportPage.importingIssuesFrom=正在从${name}导入问题
QueryFilter=筛选条件
QueryFilter.desc=这些只是一些常见筛选的条件，如需更多筛选组合，请尝试在上方的搜索框中输入。
ProjectFilterPanel.rootProjects=根项目，即无父项目
ProjectFilterPanel.leafProjects=叶子项目，即无子项目
ProjectFilterPanel.childProjectsOf=子项目
ProjectFilterPanel.forksOf=分叉自
ProjectFilterPanel.owneredBy=拥有者
QueryFilter.label=分类标签
QueryFilter.activeSince=最后更新开始于
QueryFilter.activeUntil=最后更新截止于
PullRequestFilterPanel.assignedTo=负责人
QueryFilter.submittedBy=提交人
QueryFilter.status=状态
BuildFilterPanel.job=构建任务
BuildFilterPanel.ranOnAgent=任务代理
BuildFilterPanel.submittedAfter=提交时间晚于
BuildFilterPanel.submittedBefore=提交时间早于
IssueFilterPanel.state=状态
IssueFilterPanel.iteration=迭代
IssueFilterPanel.confidential=是否私密
PackFilterPanel.publishedAfter=发布日期晚于
PackFilterPanel.publishedBefore=发布日期早于
PackFilterPanel.publishedByProject=哪个项目发布
PackFilterPanel.publishedByUser=谁发布
AgentFilterPanel.os=操作系统
AgentFilterPanel.osArch=操作系统架构
AgentFilterPanel.paused=已暂停
AgentFilterPanel.hasRunningBuilds=正在执行构建
AgentFilterPanel.everUsedSince=自哪天后使用至今
AgentFilterPanel.notUsedSince=自哪天后一直未使用
CommitFilterPanel.branch=分支
CommitFilterPanel.tag=git标签
CommitFilterPanel.build=构建
CommitFilterPanel.touchedFile=有变更的文件
CommitFilterPanel.authoredBy=作者
CommitFilterPanel.committedBy=提交人
CommitFilterPanel.committedAfter=提交时间晚于
CommitFilterPanel.committedBefore=提交时间早于
CodeCommentFilterPanel.createdBy=创建人
Pack.containerImage.name=容器镜像
StatsGroup.defaultWeekFormatterPattern=第%02d周
InsertAdmonitionPanel.pageTitle=插入提示框
InsertAdmonitionPanel.admTitleLabel=标题
InsertAdmonitionPanel.admContentLabel=内容
InsertAdmonitionPanel.admTypeLabel=类型
InsertAdmonitionPanel.admContentCollapseLabel=允许内容折叠
InsertAdmonitionPanel.summary=摘要
InsertAdmonitionPanel.bug=缺陷
InsertAdmonitionPanel.caution=注意
InsertAdmonitionPanel.error=错误
InsertAdmonitionPanel.example=示例
InsertAdmonitionPanel.failure=失败
InsertAdmonitionPanel.question=问题
InsertAdmonitionPanel.help=帮助
InsertAdmonitionPanel.info=信息
InsertAdmonitionPanel.note=备注
InsertAdmonitionPanel.warning=警告
InsertAdmonitionPanel.success=成功
InsertAdmonitionPanel.done=完成
InsertAdmonitionPanel.tip=技巧
InsertAdmonitionPanel.important=重要
InsertAdmonitionPanel.error.titleTooLong=标题超过最大长度100个字符
InsertAdmonitionPanel.error.contentTooLong=内容超过最大长度3000个字符
TopbarOnlyLayoutPage.switchTheme=换个颜色，换个心情～
TopbarOnlyLayoutPage.caremelbrown=焦糖色
TopbarOnlyLayoutPage.teal=蓝绿色(默认)
TopbarOnlyLayoutPage.violet=紫罗兰
TopbarOnlyLayoutPage.indigo=靛蓝色
TopbarOnlyLayoutPage.crimson=胭脂红
TopbarOnlyLayoutPage.pastelpink=卡马龙粉
TopbarOnlyLayoutPage.oliveGreen=橄榄绿
TwoFactorAuthenticationStatusPanel.alert.alreadySetup=双因素认证已启用。
TwoFactorAuthenticationStatusPanel.alert.clickHere=点次重新设置，
TwoFactorAuthenticationStatusPanel.alert.reset=如果您同时丢失了TOTP验证器和访问代码，请重新设置。
TwoFactorAuthenticationStatusPanel.notConfigureAlert=下次登录时将提示设置双因素认证
TwoFactorAuthenticationSetupPanel.twoFactorIsEnforced=为提升账户安全性，系统强制启用双因素认证，请按以下流程设置：
TwoFactorAuthenticationSetupPanel.scan=使用TOTP验证器扫描下方二维码
TwoFactorAuthenticationSetupPanel.mobileApps=支持TOTP的移动应用（如Microsoft Authenticator, 腾讯身份验证器, 阿里云App, 1Password等）
TwoFactorAuthenticationSetupPanel.enterPasscode=输入TOTP验证器生成的动态密码以完成验证
TwoFactorAuthenticationSetupPanel.enterSecretKey=您还可以在您的身份验证应用程序中手动输入下方的安全码
TwoFactorAuthenticationSetupPanel.configureSuccess=双因素认证配置完成
TwoFactorAuthenticationSetupPanel.important=重要提示：
TwoFactorAuthenticationSetupPanel.please=请务必
TwoFactorAuthenticationSetupPanel.download=下载
TwoFactorAuthenticationSetupPanel.recovery=下方的恢复代码并妥善保管。若无法访问验证器应用，可以使用这些代码获取账户临时访问权限。该页面关闭后将<b>不再</b>显示
TwoFactorAuthenticationSetupPanel.error.passcodeEmpty=请输入动态密码
TwoFactorAuthenticationSetupPanel.error.passcodeError=动态密码错误
Passcode.placeholder=6位动态密码
RecoveryCode.placeholder=恢复代码
Passcode.name=动态密码
RecoveryCode.name=恢复代码
ThirdPartyLogin=第三方登录
ExternalIssueTransformers.issuePattern.name=问题模式
ExternalIssueTransformers.issuePattern.desc=指定一个 &lt;a href='http://docs.oracle.com/javase/6/docs/api/java/util/regex/Pattern.html'&gt;正则表达式&lt;/a&gt; 用于匹配问题引用。例如：&lt;br&gt; &lt;em&gt;(^|\\W)([A-Z][A-Z]+-\\d+)(?=\\W|$)&lt;/em&gt;
ExternalIssueTransformers.replaceWith.name=外部问题链接模版
ExternalIssueTransformers.replaceWith.desc=指定一个外部问题链接的模版，可使用如 $1 和 $2 这样的捕获组。例如：&lt;br&gt;&lt;em&gt;$1&amp;lt;a href='https://example.com/issues/$2'&amp;gt;$2&amp;lt;/a&amp;gt;&lt;/em&gt;
ExternalIssueTransformersPage.tips=当您正在使用外部问题跟踪系统时，可以在这里配置转换器，以便在提交信息、合并请求等位置将问题引用转换为外部链接。具体如何配置，这里有一些例子供您参考:
ExternalIssueTransformersPage.tips.th1=问题引用
ExternalIssueTransformersPage.tips.th2=原始链接(虚构的)
ExternalIssueTransformersPage.githubDesc=引用github上某个项目问题编号为5的问题
ExternalIssueTransformersPage.gitlabDesc=引用gitlab上某个项目问题编号为1的问题
ExternalIssueTransformersPage.jiraDesc=引用jira上问题编号为DG-1的问题
ExternalIssueTransformersPage.ytDesc=引用youtrack上问题编号为IJPL-111016的问题
ExternalIssueTransformersPage.planeDesc=引用plane上问题编号为DEVLA-16的问题
ExternalIssueTransformersPage.updateSuccess=保存成功
ExternalIssueTransformersPage.topbarTitle=外部问题链接
ShowMatchingAgents.title=显示匹配到的任务代理
AnyAgentWithI=<i>任意代理</i>
DefaultBaseProjectRoleManager.usageRoleName=角色'%s'
ChildLinkPanel.exceededLimit=项目数量过多，无法显示
PasswordRule.minLen.name=最小长度
PasswordRule.minLen.desc=密码的最小字符数
PasswordRule.containUppercase.name=必须包含大写字母
PasswordRule.containUppercase.desc=密码中必须至少包含一个大写字母
PasswordRule.containLowercase.name=必须包含小写字母
PasswordRule.containLowercase.desc=密码中必须至少包含一个小写字母
PasswordRule.containDigit.name=必须包含数字
PasswordRule.containDigit.desc=密码中必须至少包含一个数字
PasswordRule.containSpecial.name=必须包含特殊字符
PasswordRule.containSpecial.desc=密码中必须至少包含一个特殊字符，如 !@#$%^&*
PasswordRule.error.prefix=密码规则：\n
PasswordRule.error.minLen=最小长度: %s 个字符\n
PasswordRule.error.mustContainUppercase=必须至少包含一个大写字母\n
PasswordRule.error.mustContainLowercase=必须至少包含一个小写字母\n
PasswordRule.error.mustContainNumber=必须至少包含一个数字\n
PasswordRule.error.mustContainSpecial=必须至少包含一个特殊字符\n
Slogan=DevGrip，一个贯穿您整个软件开发生命周期的强大工具，为源代码管理、CI/CD、包管理和问题跟踪提供更加愉悦的使用体验。
MarkdownEditor.restoreFromUnsaved=以下内容是从刚才的未保存内容中恢复的。
MarkdownEditor.textFormat=标题
MarkdownEditor.Bold=加粗
MarkdownEditor.Italic=斜体
MarkdownEditor.Heading=一级标题
MarkdownEditor.Heading2=二级标题
MarkdownEditor.Heading3=三级标题
MarkdownEditor.Heading4=四级标题
MarkdownEditor.Heading5=五级标题
MarkdownEditor.Heading6=六级标题
MarkdownEditor.Link=链接
MarkdownEditor.Image=图片
MarkdownEditor.UnorderedList=无序列表
MarkdownEditor.OrderedList=有序列表
MarkdownEditor.TaskList=任务列表
MarkdownEditor.Code=代码
MarkdownEditor.Quote=引用
MarkdownEditor.ShowEmoj=显示表情符号
MarkdownEditor.Mention=@其他人
MarkdownEditor.Reference=引用问题、构建或拉取请求
MarkdownEditor.CodeSugg=代码建议
MarkdownEditor.ToggleFixedWidthFont=等宽字体
MarkdownEditor.FullScreen=全屏
MarkdownEditor.Help=帮助
MarkdownEditor.Preview=预览
MarkdownEditor.Split=markdown实时预览
MarkdownEditor.attachFile=你也可以将文件/图片拖放到输入框，或从剪贴板粘贴图片。
MarkdownEditor.githubMarkdown=支持GitHub风格的Markdown
MarkdownEditor.acceptedWith=，
MarkdownEditor.mermaidAndKatex=也支持mermaid和katex。
MarkdownEditor.refIssue=引用问题
MarkdownEditor.refPr=引用拉取请求
MarkdownEditor.refBuild=引用构建
MarkdownEditor.nothingPreview=没有内容
MarkdownViewer.pleaseTryAgain=有人更改了您正在编辑的内容，该内容已重新加载，请重试。
InsertUrlPanel.insert=插入
InsertUrlPanel.commitAndInsert=提交并插入
InsertUrlPanel.insertLinkToFile=插入链接
InsertUrlPanel.remove=删除
InsertUrlPanel.removeFile=删除该文件
InsertUrlPanel.noImage=无图片
InsertUrlPanel.noFile=无文件
InsertUrlPanel.select=请选择
InsertUrlPanel.urlLabel.imageUrl=图片URL
InsertUrlPanel.urlLabel.linkUrl=链接URL
InsertUrlPanel.urlHelp.imageHelp=请输入该图片的绝对或相对URL
InsertUrlPanel.urlHelp.linkHelp=请输入该链接的绝对或相对URL
InsertUrlPanel.urlTxt.imageTxt=图片文本
InsertUrlPanel.urlTxt.linkTxt=链接文本
InsertUrlPanel.error.image=应指定图片URL
InsertUrlPanel.error.link=应指定链接URL
InsertUrlPanel.deleteConfirm=确定要删除'${attachName}'吗?
InsertUrlPanel.attachment=附件
InsertUrlPanel.inserLabel.image=插入图片
InsertUrlPanel.inserLabel.link=插入链接
InsertUrlPanel.inputUrl=输入URL
InsertUrlPanel.pickExisting=从现有中选择
InsertUrlPanel.upload=上传
IssueActivitiesPanel.comment.placeholder=请输入您的评论
IssueActivitiesPanel.error.submit=请选择要更新的字段
DropzoneField.dictDefaultMessage=您可以把文件拖到这上传或者点击这里上传
DropzoneField.dictFallbackMessage=您的浏览器不支持拖拽文件上传。
DropzoneField.dictFallbackText=您可以请使用下面的备用表单，像以前一样上传文件。
DropzoneField.dictFileTooBig=您本次上传的文件总共不能超过: ${maxFilesize}MB
DropzoneField.dictInvalidFileType=你不能上传这中类型的文件。
DropzoneField.dictCancelUpload=取消上传
DropzoneField.dictCancelUploadConfirmation=确定要取消吗？
DropzoneField.dictRemoveFile=删除文件
DropzoneField.dictRemoveFileConfirmation=确定要删除吗？
DropzoneField.dictMaxFilesExceeded=您上传的文件太多了，无法继续上传。
allWithI=<i>所有</i>
ServiceLocatorListEditPanel.colServices=适用于哪些服务
ServiceLocatorListEditPanel.colImages=适用于哪些镜像
ServiceLocatorListEditPanel.colNodeSel=#节点选择器加载的数据(Node Selector)
ServiceLocatorEditPanel.title=服务定位器(Service Locator)
AgentStatusBadage.online=在线
AgentStatusBadage.offline=离线
AgentStatusBadage.paused=已暂停
WatchStatusPanel.defaultTitle=(默认)只关注我参与的
WatchStatusPanel.defaultDesc=一旦我参与就关注
WatchStatusPanel.watchTitle=关注
WatchStatusPanel.watchDesc=这里的任何变动您都将会收到通知
WatchStatusPanel.ignoreTitle=忽略
WatchStatusPanel.ignoreDesc=忽略与我无关的任何变动
SubscriptionStatusLink.titleWatched=已关注，点击取消关注
SubscriptionStatusLink.titleUnwatched=未关注，点击关注
FuzzyQueryTips=若执行模糊搜索，我们建议将搜索文本用英文 '~' 包围，这样还可以添加更多条件，例如：~your search~ and ${search}
groupchoice.notSpecified=未指定
groupchoice.chooseGroup=请选择...
profileEditPanel.enable=启用
profileEditPanel.disable=禁用
profileEditPanel.enable.success=用户已启用
profileEditPanel.enable.confirm=确定要启用该账号吗
profileEditPanel.disable.success=用户已禁用
profileEditPanel.disable.confirm=禁用账户将会清除密码、清除访问令牌、并从其他实体中移除所有引用（除过去的活动记录外）。确定要继续吗？
profileEditPanel.nameAlreadyInUsed=登录名已经被使用
profileEditPanel.updateSuccess=更新成功
passwordEditPanel.buttonChange=修改
passwordEditPanel.buttonSet=设置
passwordEditPanel.changeSuccess=密码修改成功
passwordEditPanel.setSuccess=密码设置成功
passwordEditBean.oldPassword=旧密码
passwordEditBean.newPassword=新密码
userDeleteLink.confirmDeleteYourAccount=确定要删除你自己的账号吗?
userDeleteLink.confirm=确定要删除${name}吗？
userDeleteLink.deleteYourAccountSuccess=你的账号已经删除成功
userDeleteLink.deleteSuccess=用户${name}已经删除成功
AccessTokenEditBean.name=名称
AccessTokenEditBean.hasOwnerPermissions.name=是否具有该用户的所有权限
AccessTokenEditBean.hasOwnerPermissions.desc=启用此选项后令牌具有和该用户相同的权限
AccessTokenEditBean.authorizedProjects.name=授权的项目
AccessTokenEditBean.authorizedProjects.desc=只能授权那些被该用户管理的项目
AccessTokenEditBean.expireDate.placeholder=永不过期
AccessTokenEditBean.expireDate.name=失效日期
EmailAddressesPanel.desc1=主电子邮件是用来接收各种通知的。
EmailAddressesPanel.desc2=git电子邮件地址将用作在网页界面上展示提交历史的作者/提交者。
EmailAddressesPanel.desc3=为了确定该用户是否为git提交的作者/提交者时，将检查此处列出的所有电子邮件。
EmailAddressesPanel.desc4=为了确定您是否为git提交的作者/提交者时，将检查此处列出的所有电子邮件。
EmailAddressesPanel.desc5=未经验证的电子邮件不适用于上述功能
EmailAddressesPanel.addLable=添加一个新的电子邮件
EmailAddressesPanel.resendSuccess=验证邮件已发送，请查收
EmailAddressesPanel.unableSend=邮件服务还未配置，无法发送验证邮件
EmailAddressesPanel.deleteFail=无法删除，至少要有一个邮件地址。要删除该邮件地址，请先新增一个新的邮件地址。
EmailAddressesPanel.deleteConfirm=确定要删除该邮件地址?
EmailAddressesPanel.emailAlreadyUsed=该邮件地址已经被使用
EmailAddressesPanel.emailMalformed=电子邮件格式有误
AvatarEditPanel.current=当前头像
AvatarEditPanel.uploadTitle=上传
AvatarEditPanel.uploadButton=上传
ConfirmativePasswordPropertyEditor.confirmIsEmpty=请再次输入密码。
ConfirmativePasswordPropertyEditor.passwordNotEq=两次输入的密码必须相同。
modal.confirm.title=请您确认
InsertSshKeyPanel.title=添加一个SSH密钥
InsertSshKeyPanel.add.fail=这个密钥已经被人使用
SshKeyListPanel.fingerprint=指纹
SshKeyListPanel.comment=描述
SshKeyListPanel.noComment=无描述
SshKeyListPanel.createAt=创建于
SshKeyListPanel.deleteSuccess=删除成功
SshKeyListPanel.deleteConfirm=确定要删除吗？
InsertGpgKeyPanel.title=添加一个GPG公钥
InsertGpgKeyPanel.addFail.alreadyInUsed=该公钥或其子密钥已被使用
InsertGpgKeyPanel.addFail.emailNotVerified=该密钥关联的邮件地址是${email}，但是该邮件地址并不是${who}名下已验证的那个邮件地址。
InsertGpgKeyPanel.addFail.who1=你
InsertGpgKeyPanel.addFail.who2=该用户
GpgKeyListPanel.ineffective=无效
GpgKeyListPanel.KeyID=密钥ID
GpgKeyListPanel.email=邮件地址
GpgKeyListPanel.subKeys=子密钥
GpgKeyListPanel.None=<i>无</i>
GpgKeyListPanel.deleteSuccess=删除成功
GpgKeyListPanel.deleteConfirm=确定要删除该密钥吗？
AccessTokenPanel.deleteConfirm=确定要删除这个访问令牌吗？
AccessTokenPanel.regenTitle=重新生成访问令牌
AccessTokenPanel.regenConfirm=生成新的访问令牌会导致当前的访问令牌失效。确定要继续吗？
AccessTokenPanel.regenSuccess=访问令牌生成成功
AccessTokenEditPanel.addFail.nameAlreadyUsed=该名称已经被使用
ConfirmLeaveListener.confirmLeaveMsg=您的修改还未保存，确定要放弃吗？
SavedQueriesPanel.useDefaultConfirm=这将丢弃你之前保存的快捷查询，确定吗？
SavedQueriesPanel.saveFail=名称重复了:${name}
SavedQueriesPanel.autoHide.title=开启自动隐藏，鼠标移走后，快捷查询栏自动隐藏和显示
JobPrivilegeEditPanel.title=任务权限
JobPrivilegeListEditPanel.jobNames=任务名称
JobPrivilegeListEditPanel.privilege=权限
JobPrivilegeListEditPanel.manageJob=可管理
JobPrivilegeListEditPanel.runJob=可执行
JobPrivilegeListEditPanel.access.log=构建日志
JobPrivilegeListEditPanel.access.pipeline=构建流水线
JobPrivilegeListEditPanel.access.reports=构建报告: ${reports}
JobPrivilegeListEditPanel.access.artifacts=构建产物
JobPrivilegeListEditPanel.access.all=拥有访问[${all}]的权限
JobPrivilegeListEditPanel.deleteConfirm=确定要删除这个权限吗？
Role.pm.name=项目管理
Role.pm.desc=能否拥有整个项目的管理权限
Role.ccp.name=创建子项目
Role.ccp.desc=能否在项目下创建子项目
Role.prm.name=拉取请求管理
Role.prm.desc=能否拥有项目内的拉取请求的管理权限，包括对多个拉取请求的批量操作权限
Role.ccm.name=代码评论管理
Role.ccm.desc=能否拥有项目内的代码评论的管理权限，包括对多个代码评论的批量操作权限
Role.cp.name=代码权限
Role.pp.name=包与镜像库的权限
Role.im.name=问题管理
Role.im.desc=能否拥有项目内的问题管理权限，包括对多个问题的批量操作权限
Role.aci.name=访问私密问题
Role.aci.desc=此权限可让用户访问私密问题
Role.att.name=访问时间跟踪
Role.att.desc=此权限可让用户访问时间跟踪
Role.si.name=为问题安排迭代
Role.si.desc=此权限可以将问题安排进迭代中
Role.eif.name=哪些字段允许编辑
Role.eif.desc=可指定在开启新问题后哪些字段允许编辑
Role.eil.name=哪种问题关联允许编辑
Role.eil.desc=可指定在开启新问题后哪种问题关联(例如：子问题/父问题)允许编辑
Role.bm.name=构建管理
Role.bm.desc=能否拥有项目内的所有构建任务管理权限，包括对多个构建的批量操作权限
Role.uc.name=上传缓存
Role.uc.desc=如果启用，则允许上传构建任务运行期间生成的构建缓存。这样，上传的缓存就可以被项目的后续构建复用
Role.jp.name=构建任务权限
ExcludeIssueFields.allExcept=全部但除了
ExcludeIssueFields.excludeFields=要排除的字段
IncludeIssueFields.specifiedFields=指定字段
IncludeIssueFields.includeFields=包含
JobPrivilege.jobNames.name=构建任务名称
JobPrivilege.jobNames.desc=指定构建任务名称，多个构建任务以空格分隔。使用'*'或'?'进行通配符匹配，以'-'开头的会被排除。 <b class='text-danger'>注意: </b> 即使此处未指定任何权限，也会默认授予这些构建任务访问构建产物的权限。
JobPrivilege.manageJob.name=管理构建任务权限
JobPrivilege.manageJob.desc=能否管理构建任务的权限，包括删除这些任务的构建。
JobPrivilege.runJob.name=执行任务权限
JobPrivilege.runJob.desc=能否手动的执行任务。也意味着可以访问构建日志，构建流水线和以发布的构建报告。
JobPrivilege.accessBuildLog.name=访问构建日志
JobPrivilege.accessBuildLog.desc=能否访问构建日志
JobPrivilege.accessBuildPipeline.name=访问构建流水线
JobPrivilege.accessBuildPipeline.desc=能否访问构建流水线
JobPrivilege.accessBuildReports.name=访问构建报告
JobPrivilege.accessBuildReports.placeholder=无
JobPrivilege.accessBuildReports.desc=指定报告名称，多个报告以空格分隔。使用'*'或'?'进行通配符匹配，以'-'开头的会被排除。
newInvitation.sendLog=正在给'%s'发送邀请...
newInvitation.title=邀请用户
newInvitation.sent=邀请已发送
newInvitation.configureMailSetting=请先点击
newInvitation.mailSetting=邮件配置
inviteUserListFilter=请输入邮件进行筛选
resendInvitaion=重新发送邀请邮件
cancelInvitation=取消邀请
newInvitation.emailAddress=邮件地址
mailServiceNotConfigured=您的邮件服务还没有配置
invitationSentTo=邀请邮件已成功发送给${email}
confirmCancelInvitation=确定要取消发送给${email}的邀请吗 ?
invitationDeleted=发送给${email}的邀请已被删除
inviteButton=邀请
invitationBean.email.name=邮件地址
invitationBean.email.description=发送邀请到指定的邮件地址，一行只能填一个邮件地址
validate.emailAddress.invalid=邮件格式错误: 
validate.emailAddress.alreadyInUse=该邮件已被使用: 
validate.emailAddress.alreadyInvited=该邮件已经被邀请了: 
validate.emailAddress.notFound=至少指定一个邮件地址
myPage.Title=个人信息
myAccessTokenPage.desc=访问令牌(Access Token)可用于api的调用、git的版本库拉取/推送，但它不能用于登录网页界面。
myAccessTokensPage.title=我的访问令牌(Access Token)
myAvatarPage.title=编辑我的头像
myEmailAddressesPage.title=我的电子邮件地址
myGpgKeysPage.title=我的GPG密钥
myGpgKeysPage.plusButtonTitle=创建GPG密钥
myGpgKeysPage.desc=在此处添加GPG密钥，以验证你签名的提交(commit)/标记(tag)
myGpgKeysPage.howToUse=访问<a href="https://docs.github.com/en/authentication/managing-commit-signature-verification/about-commit-signature-verification#gpg-commit-signature-verification" target="_blank">GitHub文档</a>学习如何生成和使用GPG密钥对你的提交进行签名
myGpgKeysPage.notBelongTo=<b>请记住</b>带有<span class="badge badge-warning badge-sm">无效</span>标记的邮件地址要么不属于密钥所有者要么没有通过密钥所有者的验证。
myPasswordPage.title=修改我的密码
myPreferencesPage.title=我的偏好
myProfilePage.title=我的信息
myProfilePage.authWithInternalDatabase.desc=您正在使用我们系统内建的身份认证。目前我们系统还支持多种外部系统认证，如LDAP、AD、第三方登录等。
myProfilePage.authWithExternalSystem.desc=您正在使用外部系统进行身份认证。
mySshKeysPage.title=我的SSH密钥
mySshKeysPage_plusButtonTitle=创建SSH密钥
myTwoFactorAuthentication.title=双因素认证
GeneralErrorPage.errorTitle=发生了意外的错误
PasswordResetBean.newPwd=请输入新的密码
PasswordResetPage.resetPasswordTitle=重置密码
PasswordResetPage.resetSuccess=密码重置成功，请使用新密码重新登录。
PasswordResetPage.subject=[密码重置]您请求重置DevGrip的密码
PasswordResetPage.checkEmail=重置密码请求已发送，请检查您的邮件，并按照邮件的提示操作。
PasswordResetPage.unableSend=无法发送密码重置邮件，因为您的邮件服务还没有配置。
PasswordResetPage.noUserFound=用户名或邮件地址错误: 
PasswordResetPage.disableUserOrServiceAccount=无法重置密码，因为该用户是机器人账号或者已被禁用
PasswordResetPage.authViaExternaSystem=无法重置密码，因为该用户正在使用外部系统进行身份认证
SignUpBean.email=邮箱地址
SignUpBean.descMsg1=允许的域名: 
SignUpBean.descMsg2=，不允许的域名: 
SignUpBean.descMsg3=不允许的域名: 
CreateUserFromInvitationPage.submitError=登录名已经被使用
CreateUserFromInvitationPage.submitSuccess=账号设置成功。
LogoutPage.logoutSuccess=您已成功退出登录
simple.userSignup.nameAlreadyUsed=该用户名已经被其他用户使用
simple.userSignup.emailAlreadyUsed=该邮件地址已经被其他用户使用
simple.userSignup.emailNotAllowed=该邮件域名不允许注册，请联系管理员。
simple.userSignup.success=注册成功啦
simple.userSignup.signUp=注册
simple.userSignup.enterYourDetails=请输入以下信息来创建账户
Description=描述
No_Description=无描述
Color=颜色
None=无
Pull_Requests=拉取请求
Projects=项目
Applicable_Projects=适用于哪些项目
Any_Project=任何项目
System_Settings=系统设置
Security_Settings=安全设置
Users=用户
Groups=用户组
Invitations=邀请
User_Management=用户管理
Role_Management=角色管理
Group_Management=用户组管理
External_Authenticator=外部认证
Single_Sign_On=第三方登录
Authenticator=认证
SSH_Server_Key=SSH服务器密钥
GPG_Signing_Key=GPG签名密钥
GPG_Trusted_Keys=受信任的GPG密钥
SSH_GPG_Keys=SSH和GPG密钥
Custom_Fields=自定义字段
States=状态
State_Transitions=状态转换
Default_Boards=默认看板
IssueLinks=问题关联
Time_Tracking=时间跟踪
Description_Templates=描述模板
Commit_Message_Fix_Patterns=在提交消息中关闭问题
Check_Issue_Integrity=问题一致性检查
Issue_Settings=问题设置
Job_Executors=任务执行器
Agents=任务代理
Mail_Service=邮件服务
Service_Desk_Settings=服务台设置
Issue_Notification=问题通知
Issue_Notification_Unsubscribed=取消问题通知
Service_Desk_Issue_Opened=服务台问题已开启
Service_Desk_Issue_Open_Failed=服务台问题开启失败
Issue_Stopwatch_Overdue=问题逾期
Pull_Request_Notification=拉取请求通知
Pull_Request_Notification_Unsubscribed=取消拉取请求通知
Build_Notification=构建通知
Package_Notification=包管理通知
Commit_Notification=提交通知
User_Invitation=用户邀请
Email_Verification=电子邮件验证
Password_Reset=重置密码
System_Alert=系统告警
Email_Templates=电子邮件模板
Alert_Settings=告警设置
Label_Management=分类标签
Performance_Settings=性能设置
Groovy_Scripts=Groovy脚本
Branding=您的品牌
Database_Backup=数据库备份
Server_Log=服务器日志
Server_Information=服务器信息
System_Maintenance=系统维护
Administration=管理员
Administrator=管理员
High_Availability_Scalability=集群和高可用
Replicas=副本
Subscription_Management=订阅管理
StorageSetting=存储设置
Type_To_Filter=在此筛选...
Filter_By_Name=按名称筛选...
Exited_Impersonation=退出伪身份成功
Sign_Out=退出登录
When=时间
Message=消息
Dashboards=看板
Documentation=文档
Try_Subsciption=购买订阅
Get_Support=获取支持
Support_And_Bug_Report=报告错误
Restful_API=RESTFul API
Incompatibilities=不兼容的地方
about=关于
Current_Version=当前版本
devgrip=DevGrip
Current_Commit=提交号
Copyright=版权
Alerts_Title=系统告警
New_Version_Status=有新版本可用。红色表示安全/关键更新，黄色表示错误修复，蓝色表示功能更新。点击查看变更。在系统设置中禁用
Toggle_Mode=切换黑暗/明亮模式
Expired=过期
Role=角色
Project=项目
Git=git
Sign_In=登录
You_Have_Unverified=您有未验证的
Primary=主
Email_Address=电子邮件地址
Not_Specified=未指定
Unspecified=未指定
UnspecifiedWithI=<i>未指定</i>
Please_Choose=请选择...
Profile=个人资料
Edit_Avatar=编辑头像
Edit_Project_Avatar=编辑图标
SSH_Keys=SSH密钥
GPG_Keys=GPG密钥
Access_Tokens=访问令牌
Preferences=偏好
Two_Factor_Authentication=双因素认证
Delete_All=删除所有
Title_Hide_Save_Queries=隐藏快捷查询
More_Info=更多信息
Delete=删除
Hide=隐藏
Update=更新
Regular_Expression=正则表达式
Whole_Word=整字匹配
Case_Sensitive=区分大小写
File_Name_Patterns=文件名模式（用逗号分隔）
File_Name_Patterns_Desc=文件名模式，例如 *.java, *.c
Symbol_Name=符号名称
Search_For=搜索
Jump_To=打开快捷导航
Help=帮助
Delete_All_Package=删除查询到的所有包和镜像
Delete_Selected_Package=删除选中的包和镜像
Query=查询
Save_Query=保存到快捷查询
Save_As_Mine=仅为我保存
Mine=我的
All_Users=公共的
Specify_name_of_the_saved_query=为快捷查询设置一个名称
Order_By=排序
Operations=操作
Tokens=授权令牌
Memory=内存
Show_Saved_Queries=显示快捷查询
Saved_Queries=快捷查询
Edit_Save_Queries=编辑快捷查询
Hide_Save_Queries=隐藏快捷查询
Files=文件
SourceCodes=源代码
Commits=提交
Branches=分支
Tags=git标签
Code_Comments=代码评论
Code_Compare=代码比较
Code_Search=超级代码搜索
List=列表
Boards=看板
Iterations=迭代
Builds=构建
Packages=包与镜像库
Code=代码
Issues=问题
General_Settings=常规设置
Authorization=授权
By_User=按用户
By_Group=按组
Branch_Protection=分支保护
Tag_Protection=git标签保护
Code_Analysis=代码分析
Git_Pack_Config=Git Pack设置
Job_Secrets=任务密钥
Job_Properties=任务属性
Build_Preserve_Rules=构建保留规则
Default_Fixed_Issue_Filters=默认的已关闭问题筛选器
Cache_Management=缓存管理
Service_Desk=服务台
Web_Hooks=Web Hooks
Settings=设置
Statistics=统计
Child_Projects=子项目
Notification=通知
Timesheets=工时统计表
Login_Sub_Title=使用邮箱或用户名登录
Two_Factor_Auth_Enabled=已启用双因素认证，请输入您的TOTP身份验证器上显示的动态密码。
Setup_Two_Factor_Auth=设置双因素认证
Invalid_Credentials=无效的凭据
Passcode_Verification_Failed=动态密码验证失败
Input_Recovery_Code=请输入启用双因素认证时保存的恢复代码之一
Recovery_Code_Verification_Failed=恢复代码验证失败
Sign_In_Indicator=登录
User_Name=用户名
Remember_Me=记住我
Copy=复制
Updated_On=最后更新于 ${lastUpdated}
Loading=正在加载...
Specify_Value_Of_The_Environment_Variable=指定环境变量的值
Specify_Name_Of_The_Environment_Variable=指定环境变量的名称
Created=已创建
Create_New_File=创建新文件
Upload_Files=上传文件
Cancel_Selected_Builds=取消选定的构建
Re-run_Selected_Builds=重新运行选定的构建
Delete_Selected_Builds=删除选定的构建
Cancel_All_Queried_Builds=取消所有查询到的构建
Re-run_All_Queried_Builds=重新运行所有查询到的构建
Delete_All_Queried_Builds=删除所有查询到的构建
Set_Selected_Comments_as_Resolved=将选定的评论标记为已解决
Set_Selected_Comments_as_Unresolved=将选定的评论标记为未解决
Delete_Selected_Comments=删除选定的评论
Set_All_Queried_Comments_as_Resolved=将所有查询到的评论标记为已解决
Set_All_Queried_Comments_as_Unresolved=将所有查询到的评论标记为未解决
Delete_All_Queried_Comments=删除所有查询到的评论
Set_All_Queried_Comments_as_Read=将所有查询到的评论标记为已读
Commit=提交
Discard=丢弃
From=从
Sync_Timing_of_Selected_Issues=同步选定问题的时间
Batch_Edit_Selected_Issues=批量编辑选定的问题
Move_Selected_Issues_To=移动选定的问题到...
Copy_Selected_Issues_To=复制选定的问题到...
Delete_Selected_Issues=删除选定的问题
Sync_Timing_of_All_Queried_Issues=同步所有查询问题的时间
Batch_Edit_All_Queried_Issues=批量编辑所有查询到的问题
Move_All_Queried_Issues_To=移动所有查询到的问题到...
Copy_All_Queried_Issues_To=复制所有查询到的问题到...
Delete_All_Queried_Issues=删除所有查询到的问题
Watch/Unwatch_All_Queried_Issues=关注/取消关注所有查询到的问题
Set_All_Queried_Issues_as_Read=将所有查询到的问题标记为已读
Move_Selected_Projects_To=移动选定的项目到...
Set_Selected_As_Root_Projects=将选定的项目设为根项目
Delete_Selected_Projects=删除选定的项目
Move_All_Queried_Projects_To=移动所有查询到的项目到...
Set_All_Queried_As_Root_Projects=将所有查询到的项目设为根项目
Delete_All_Queried_Projects=删除所有查询到的项目
Watch/Unwatch_Selected_Pull_Requests=关注/取消关注选定的拉取请求
Discard_Selected_Pull_Requests=丢弃选定的拉取请求
Delete_Selected_Pull_Requests=删除选定的拉取请求
Watch/Unwatch_All_Queried_Pull_Requests=关注/取消关注所有查询到的拉取请求
Discard_All_Queried_Pull_Requests=丢弃所有查询到的拉取请求
Delete_All_Queried_Pull_Requests=删除所有查询到的拉取请求
Set_All_Queried_Pull_Requests_as_Read=将所有查询到的拉取请求标记为已读
Set_As_Primary=设为主电子邮件
Use_For_Git_Operations=用于Git操作
Resend_Verification_Email=重新发送验证邮件
Add_before=添加到前面
Add_after=添加到后面
Pause_Selected_Agents=暂停选定的代理
Resume_Selected_Agents=恢复选定的代理
Restart_Selected_Agents=重启选定的代理
Remove_Selected_Agents=删除选定的代理
Pause_All_Queried_Agents=暂停所有查询到的代理
Resume_All_Queried_Agents=恢复所有查询到的代理
Restart_All_Queried_Agents=重启所有查询到的代理
Remove_All_Queried_Agents=删除所有查询到的代理
Delete_Selected_Groups=退出选定的用户组
Delete_All_Queried_Groups=退出所有查询到的用户组
Delete_Selected_Memberships=移除选定的组员
Delete_All_Queried_Memberships=移除所有查询到的组员
Quick_Search=点此快速搜索
Advanced_Search=高级搜索
Outline_Search=代码结构搜索
Select_Project=选择项目
Select_Branch/Tag=选择分支/git标签
Set_Up_Your_Account=设置您的账户
Email_Address_Verification=电子邮件地址验证
OOPS_There_Is_An_Error=哎呀！发生了错误
Unable_To_Delete_Right_Now=现在无法删除或禁用
The_object_you_are_deleting_is_still_being_used=您正在删除/禁用的对象仍在使用中
Importing_from=从${name}导入...
Test_importing_from=测试从${name}导入...
Please_wait=请稍候...
Page_Not_Found=页面未找到
Page_Not_Found_Desc=该服务器上并没有你要去的页面。
Forgotten_Password=忘记密码？
Enter_New_Password=输入新密码
Confirm_Approve=批准
Confirm_Request_For_Changes=请求更改
Confirm_Discard=丢弃
Confirm_Reopen=重新开启
Confirm_Delete_Source_Branch=删除源分支
Confirm_Restore_Source_Branch=恢复源分支
Enter_your_user_name_or_email_to_reset_password=请输入您的用户名或邮件进行重置密码
Email_Verified=电子邮件地址已验证
Email_Failed_Verified=电子邮件地址无法验证
Back_To_Home=回到主页
Go_Back=返回
Show_Error_Detail=显示错误详情
Import=导入
Save=保存
Download=下载
Add=添加
Search=搜索
Text_Search=搜索文本
File_Search=搜索文件
Symbol_Search=搜索符号
In_Default_Branch=只在默认分支中搜索
In_Projects=选择项目
In_Projects_Placeholder=要搜索全部项目，请留空并确保您拥有所有项目的代码读权限
In_Projects_Desc=可以选择多个项目，多个项目中间用空格分隔。 使用 '**', '*' 或者 '?' 进行 <a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。'-'不能出现在结尾。留空则表示在所有项目中搜索。
No_Matchs=搜索不到任何相匹配的结果
Matches_Found=搜索到这些匹配的结果
Match_More=更多
Indexing_default_branches=我们正在努力的索引中，请稍等...
Hint_Need_Quoted=如果模式串包含空格或以开头，则需要用引号引起来。
Hint_Path_Wildcard_2=使用 '**', '*' 或者 '?' 进行<a href='https://docs.devgrip.net/appendix/path-wildcard' target='_blank'>通配符匹配</a>。以'-'开头的会被排除。'-'不能出现在结尾。
Hint_Path_Wildcard_1=使用 '*' 或者 '?' 进行通配符匹配。以'-'开头的会被排除。'-'不能出现在结尾。
Hint_Name_Wildcard=使用'*'进行通配符匹配
Hint_Escape_Quotes=使用'\'对引号进行转义
Hint_Escape_Brackets=使用 '\' 对括号进行转义
space=空格
To_Navigate=进行选择，
To_Complete=自动完成，
or=或
All=全部
History=历史
Clone=克隆
Select_File=请选择文件
Search_Branch=搜索分支
No_Branches=未找到分支
Create=创建
CreateNew=创建
Create_Branch=创建分支
Job=构建任务
Edit_job=编辑构建任务
Submitted_By=提交人
Submitted_At=提交时间
Submit_Reason=提交原因
Retried_At=重试时间
Queueing_Takes=排队耗时
Running_Takes=运行耗时
Cancelled_By=取消者
Labels=分类标签
Labels_Desc=分类标签可以在管理员/分类标签中定义
Depends_on=依赖于
Depends_On_Me=依赖我
Show_in_build_list=在构建列表中显示
Display_Params=显示构建参数
Params_to_Display=显示哪些参数
Use_Default=使用默认值
Use_default=使用默认值
Edit=编辑
View=查看
Quote=引用
Reply=回复
Set_Resolved=设为已解决
Set_Unresolved=设为未解决
Unresolved=未解决
Resolved=已解决
Comment=评论
File_Name=文件名
File_Name_Desc=*代表任意字符串, ?代表任意字符
Command_Palette=快捷导航
Command_Palette_Desc=只要能通过URL访问的功能，您就可以输入部分URL进行匹配并跳转。提示：对于多级URL，在上级URL中使用TAB键自动补全下级URL。
No_suggestions=没有匹配到
Tab_To_Search=继续匹配
Enter_To_Go=跳转
To_Move=进行移动
To_Close=关闭
Browse_code=浏览代码
More_commits=更多提交
Too_many_commits=提交过多，无法加载
Query_Commits_Hint=查询提交历史
Create_Tag=创建git标签
Filter_by_path=请输入文件名或路径进行筛选
Revision_Index_In_Progress=正在索引修订版本...（索引完成后符号导航才会准确）
Outdated=已失效
Please_Note=注意
Disabled=禁用
No_Comment=无评论
Committed=提交于
With=、
##### BEGIN 注意 这些都是提示器说明国际化，提示器有值和说明两部分，英文版的无需额外说明，所以国际化值都是空的，这是正常的。######
ValueNeedsToBeEnclosedInParenthesis=值必须用圆括号括起来
CommitQueryBehavior.fuzzySugg=用 ~ 包裹起来将执行提交消息或提交哈希的模糊搜索
CommitQueryDescriber.branch=分支
CommitQueryDescriber.tag=git标签
CommitQueryDescriber.commit=提交哈希
CommitQueryDescriber.build=构建
CommitQueryDescriber.since=在...以后
CommitQueryDescriber.until=在...以前
CommitQueryDescriber.defaultBranch=默认分支
CommitQueryDescriber.authoredByMe=我是作者
CommitQueryDescriber.committedByMe=由我提交
CommitQueryDescriber.committer=提交者
CommitQueryDescriber.author=作者
CommitQueryDescriber.message=提交消息
CommitQueryDescriber.before=在指定日期之前
CommitQueryDescriber.after=在指定日期之后
CommitQueryDescriber.path=涉及指定文件
CommitQueryBehavior.pm530=下午5:30
CommitQueryBehavior.today=今天
CommitQueryBehavior.yesterday=昨天
CommitQueryBehavior.threeDaysAgo=3天前
CommitQueryBehavior.lastWeek=上周
CommitQueryBehavior.fourWeeksAgo=4周前
CommitQueryBehavior.oneYearAgo=1年前
CommitQueryBehavior.yesterdayMidnight=昨晚
PullRequestQueryDescriber.open=已开启
PullRequestQueryDescriber.merged=已合并
PullRequestQueryDescriber.discarded=已丢弃
PullRequestQueryDescriber.needMyAction=待我操作
PullRequestQueryDescriber.toBeReviewedByMe=待我评审
PullRequestQueryDescriber.toBeChangedByMe=待我修改
PullRequestQueryDescriber.toBeMergedByMe=待我合并
PullRequestQueryDescriber.requestedForChangesByMe=我发起的请求更改
PullRequestQueryDescriber.assignedToMe=由我负责
PullRequestQueryDescriber.approvedByMe=由我批准
PullRequestQueryDescriber.submittedByMe=由我提交
PullRequestQueryDescriber.watchedByMe=由我关注
PullRequestQueryDescriber.commentedByMe=由我评论
PullRequestQueryDescriber.mentionedMe=提到了我
PullRequestQueryDescriber.readyToMerge=已准备合并
PullRequestQueryDescriber.someoneRequestedForChanges=有人请求修改
PullRequestQueryDescriber.hasPendingReviews=有待处理的评审
PullRequestQueryDescriber.hasUnsuccessfulBuilds=有不成功的构建
PullRequestQueryDescriber.hasUnfinishedBuilds=有未完成的构建
PullRequestQueryDescriber.hasMergeConflicts=有合并冲突
PullRequestQueryDescriber.needActionOf=待谁操作
PullRequestQueryDescriber.toBeReviewedBy=待谁评审
PullRequestQueryDescriber.toBeChangedBy=待谁修改
PullRequestQueryDescriber.toBeMergedBy=待谁合并
PullRequestQueryDescriber.requestedForChangesBy=谁发起的请求变更
PullRequestQueryDescriber.assignedTo=由谁负责
PullRequestQueryDescriber.approvedBy=由谁批准
PullRequestQueryDescriber.submittedBy=由谁提交
PullRequestQueryDescriber.watchedBy=由谁关注
PullRequestQueryDescriber.commentedBy=由谁评论
PullRequestQueryDescriber.mentioned=提到谁
PullRequestQueryDescriber.includesCommit=包含提交
PullRequestQueryDescriber.includesIssue=包含问题
OperatorDescriber.orderBy=排序方式
OperatorDescriber.is=为
OperatorDescriber.isNot=不为
OperatorDescriber.contains=包含
OperatorDescriber.isGreaterThan=大于
OperatorDescriber.isLessThan=小于
OperatorDescriber.isSince=开始时间
OperatorDescriber.isUntil=结束时间
OperatorDescriber.and=与
OperatorDescriber.or=或
OperatorDescriber.not=非
OperatorDescriber.any=符合以下任意条件
OperatorDescriber.hasAny=存在任何
OperatorDescriber.all=所有
OperatorDescriber.isMe=是我的
OperatorDescriber.isNotMe=不是我的
OperatorDescriber.isAfter=之后
OperatorDescriber.isBefore=之前
OperatorDescriber.isEmpty=为空
OperatorDescriber.isNotEmpty=不为空
OperatorDescriber.matching=匹配
OperatorDescriber.anyone=任何人
OperatorDescriber.except=除了
OperatorDescriber.asc=升序
OperatorDescriber.desc=降序
OperatorDescriber.never=从不
OperatorDescriber.reference=引用
OperatorDescriber.fuzzy=模糊搜索
OperatorDescriber.exclude=排除
OperatorDescriber.space=空格，可以继续添加别的条件
PullRequestQueryDescriber.findPullRequestByNumber=根据编号查找拉取请求
OrMatchAnotherValue=或继续匹配其他值
AddAnotherSorting=添加另外的排序规则
PullRequestQueryDescriber.field.number=编号
PullRequestQueryDescriber.field.status=状态
PullRequestQueryDescriber.field.title=标题
PullRequestQueryDescriber.field.label=分类标签
PullRequestQueryDescriber.field.targetProject=目标项目
PullRequestQueryDescriber.field.targetBranch=目标分支
PullRequestQueryDescriber.field.sourceProject=源项目
PullRequestQueryDescriber.field.sourceBranch=源分支
PullRequestQueryDescriber.field.description=描述
PullRequestQueryDescriber.field.comment=评论
PullRequestQueryDescriber.field.commentCount=评论数量
PullRequestQueryDescriber.field.submitDate=提交日期
PullRequestQueryDescriber.field.lastActivityDate=最后活跃日期
PullRequestQueryDescriber.field.closeDate=关闭日期
PullRequestQueryDescriber.field.mergeStrategy=合并策略
ValueShouldBeQuoted=值应加引号
PullRequestQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行标题、描述、评论的模糊搜索
PullRequestQueryDescriber.findPrWithThisNumber=使用该编号查找拉取请求
ReactionDescriber.thumbsUpCount=表情回应:拇指向上的数量
ReactionDescriber.thumbsDownCount=表情回应:拇指向下的数量
ReactionDescriber.smileCount=表情回应:笑脸的数量
ReactionDescriber.tadaCount=表情回应:庆祝的数量
ReactionDescriber.confusedCount=表情回应:困惑脸的数量
ReactionDescriber.heartCount=表情回应:爱心的数量
ReactionDescriber.rocketCount=表情回应:火箭的数量
ReactionDescriber.eyesCount=表情回应:眼睛的数量
EntityQuery.invalidNumber=无效的数字: %s
EntityQuery.invalidDecimal=无效的小数: %s
EntityQuery.undefinedLabel=无效的分类标签: %s
EntityQuery.invalidWorkingPeriod=无效的工时: %s
EntityQuery.unableFindUser=无效的登录名: %s
EntityQuery.unableFindProject=无效的项目 '%s'
EntityQuery.invalidBoolean=无效的布尔值: %s
EntityQuery.unrecognizedDate=无效的日期: %s
EntityQuery.unableFindRevision=无效的修订版本: %s
EntityQuery.unableFindCommit=无效的提交: %s
EntityQuery.unableFindIssue=无效的问题: %s
EntityQuery.unableFindIteration=无效的迭代: %s
EntityQuery.unableFindPr=无效的拉取请求: %s
EntityQuery.unableFindBuild=无效的构建: %s
ProjectQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行项目名称或项目路径的模糊搜索
ProjectQueryDescriber.ownedby=谁的项目
ProjectQueryDescriber.ownedByMe=我的项目
ProjectQueryDescriber.ownedByNone=孤儿项目，即没有项目管理者
ProjectQueryDescriber.forksOf=由哪个项目分叉而来
ProjectQueryDescriber.roots=根项目，即无父项目
ProjectQueryDescriber.leafs=叶子项目，即无任何子项目
ProjectQueryDescriber.forkRoots=从根项目分叉而来
ProjectQueryDescriber.withoutEnoughReplicas=没有足够的副本
ProjectQueryDescriber.hasOutdatedReplicas=有失效的副本
ProjectQueryDescriber.missingStorage=丢失存储的副本
ProjectQueryDescriber.pendingDelete=待删除
ProjectQueryDescriber.childrenOf=哪个项目的子项目
ProjectQueryDescriber.field.id=项目编号
ProjectQueryDescriber.field.name=项目名称
ProjectQueryDescriber.field.path=项目路径
ProjectQueryDescriber.field.key=项目简写
ProjectQueryDescriber.field.label=项目分类标签
ProjectQueryDescriber.field.description=项目描述
ProjectQueryDescriber.field.serviceDeskEmailAddress=服务台邮件地址
ProjectQueryDescriber.field.lastCommitDate=最后提交日期
ProjectQueryDescriber.field.lastActivityDate=最后活跃日期
IssueQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行问题名称、描述、评论的模糊搜索
IssueQueryDescriber.findIssueByNumber=根据编号查找问题
IssueQueryDescriber.submittedBy=由谁提交
IssueQueryDescriber.watchedBy=被谁关注
IssueQueryDescriber.ignoredBy=被谁忽略
IssueQueryDescriber.commentedBy=由谁评论
IssueQueryDescriber.mentioned=提到谁
IssueQueryDescriber.fixedInCommit=在哪个提交中修复
IssueQueryDescriber.fixedInCurrentCommit=在当前提交中修复
IssueQueryDescriber.fixedInPullRequest=在哪个拉取请求中修复
IssueQueryDescriber.fixedInCurrentPullRequest=在当前拉取请求中修复
IssueQueryDescriber.fixedInBuild=在哪个构建中修复
IssueQueryDescriber.fixedInCurrentBuild=在当前构建中修复
IssueQueryDescriber.isCurrent=当前
IssueQueryDescriber.isPrevious=之前
IssueQueryDescriber.fixedBetween=在...之间被修复
IssueQueryDescriber.submittedByMe=我提交的
IssueQueryDescriber.commentedByMe=我评论的
IssueQueryDescriber.watchedByMe=我关注的
IssueQueryDescriber.ignoredByMe=我忽略的
IssueQueryDescriber.mentionedMe=提到我
IssueQueryDescriber.confidential=私密
IssueQueryDescriber.currentIssue=当前问题
IssueQueryDescriber.build=构建
IssueQueryDescriber.commit=提交
IssueQueryDescriber.tag=标签
IssueQueryDescriber.branch=分支
IssueQueryDescriber.project=项目
IssueQueryDescriber.number=编号
IssueQueryDescriber.state=状态
IssueQueryDescriber.title=标题
IssueQueryDescriber.description=描述
IssueQueryDescriber.estimatedTime=预估时间
IssueQueryDescriber.spentTime=花费时间
IssueQueryDescriber.progress=进度
IssueQueryDescriber.comment=评论
IssueQueryDescriber.submitDate=提交日期
IssueQueryDescriber.lastActivityDate=最后活跃日期
IssueQueryDescriber.voteCount=投票数量
IssueQueryDescriber.commentCount=评论数量
IssueQueryDescriber.iteration=迭代
IssueQueryDescriber.boardPosition=看板位置
IssueQueryDescriber.totalEstimatedTime=总预估时间
IssueQueryDescriber.totalSpentTime=总花费时间
IssueQueryDescriber.totalSpentAndEstimatedTime=总花费时间/总预估时间
IssueQueryDescriber.spentAndEstimatedTime=花费时间/预估时间
IssueQueryDescriber.workingPeriod=工时示例
IssueQueryDescriber.decimal=进度示例
BuildQueryDescriber.findBuildByNumber=根据构建编号查找
BuildQueryDescriber.findBuildByThisNumber=使用该编号查找构建
BuildQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行构建名称和版本的模糊搜索
BuildQueryDescriber.successful=已成功
BuildQueryDescriber.failed=已失败
BuildQueryDescriber.cancelled=已取消
BuildQueryDescriber.timedOut=已超时
BuildQueryDescriber.finished=已完成
BuildQueryDescriber.running=正在运行
BuildQueryDescriber.pending=正在排队
BuildQueryDescriber.waiting=正在等待
BuildQueryDescriber.submittedByMe=我提交的任务
BuildQueryDescriber.submittedBy=谁提交的任务
BuildQueryDescriber.cancelledByMe=我取消的
BuildQueryDescriber.cancelledBy=谁取消的
BuildQueryDescriber.dependsOn=哪些任务依赖我
BuildQueryDescriber.dependenciesOf=我依赖哪些任务
BuildQueryDescriber.ranOn=运行于
BuildQueryDescriber.fixedIssue=修复了哪个问题
BuildQueryDescriber.field.project=项目
BuildQueryDescriber.field.job=任务
BuildQueryDescriber.field.status=状态
BuildQueryDescriber.field.number=编号
BuildQueryDescriber.field.branch=分支
BuildQueryDescriber.field.tag=git标签
BuildQueryDescriber.field.version=版本
BuildQueryDescriber.field.label=分类标签
BuildQueryDescriber.field.pullRequest=拉取请求
BuildQueryDescriber.field.commit=git提交
BuildQueryDescriber.field.submitDate=任务提交日期
BuildQueryDescriber.field.pendingDate=任务排队日期
BuildQueryDescriber.field.runningDate=任务运行日期
BuildQueryDescriber.field.finishDate=任务完成日期
BuildMetricQueryDescriber.buildIsSuccessful=构建成功
BuildMetricQueryDescriber.buildIsFailed=构建失败
BuildMetricQueryDescriber.report=报告
CodeCommentQueryDescriber.createdByMe=我创建的
CodeCommentQueryDescriber.repliedByMe=我回复的
CodeCommentQueryDescriber.createdBy=谁创建的
CodeCommentQueryDescriber.repliedBy=谁回复的
CodeCommentQueryDescriber.mentionedMe=提到我的
CodeCommentQueryDescriber.mentioned=提到谁的
CodeCommentQueryDescriber.resolved=已解决的
CodeCommentQueryDescriber.unresolved=未解决的
CodeCommentQueryDescriber.onCommit=指定提交上的
CodeCommentQueryDescriber.field.content=内容
CodeCommentQueryDescriber.field.reply=回复
CodeCommentQueryDescriber.field.path=路径
CodeCommentQueryDescriber.field.status=状态
CodeCommentQueryDescriber.field.createDate=创建日期
CodeCommentQueryDescriber.field.lastActivityDate=最后活跃日期
CodeCommentQueryDescriber.field.replyCount=回复数量
CodeCommentQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行路径、内容、回复的模糊搜索
PackQueryDescriber.publishedByMe=我发布的
PackQueryDescriber.publishedByUser=谁发布的
PackQueryDescriber.publishedByBuild=哪个构建发布的
PackQueryDescriber.publishedByProject=哪个项目发布的
PackQueryDescriber.field.project=项目
PackQueryDescriber.field.type=类型
PackQueryDescriber.field.name=名称
PackQueryDescriber.field.version=版本
PackQueryDescriber.field.label=分类标签
PackQueryDescriber.field.publishDate=发布日期
PackQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行模糊搜索
AgentQueryDescriber.online=在线
AgentQueryDescriber.offline=离线
AgentQueryDescriber.paused=已暂停
AgentQueryDescriber.hasRunningBuilds=正在执行构建
AgentQueryDescriber.hasAttribute=带有哪个属性
AgentQueryDescriber.notUsedSince=自哪天后就一直未被使用
AgentQueryDescriber.everUsedSince=使用至今
AgentQueryDescriber.selectedByExecutor=被执行器使用
AgentQueryDescriber.ranBuild=执行过构建
AgentQueryDescriber.field.name=名称
AgentQueryDescriber.field.ipAddress=ip地址
AgentQueryDescriber.field.osName=操作系统
AgentQueryDescriber.field.osVersion=操作系统版本
AgentQueryDescriber.field.osArch=操作系统架构
AgentQueryDescriber.field.lastUsedDate=最后使用日期
AgentQueryDescriber.fuzzySugg=用 ~ 包裹起来将执行名称、ip、操作系统的模糊搜索
SymbolTooltipPanel.possibleDeclaration=可能的声明
SymbolTooltipPanel.allOccurrences=搜索所有出现的位置
JobMatchDescriber.onBranch=哪个分支
JobMatchDescriber.submittedByUser=哪个用户提交的
JobMatchDescriber.submittedByGroup=哪个组提交的
JobMatchDescriber.project=哪个项目
JobMatchDescriber.job=哪个任务
ActionConditionDescriber.onBranch=哪个分支
ActionConditionDescriber.field.project=正在运行任务的项目
ActionConditionDescriber.field.branch=正在运行任务的分支
ActionConditionDescriber.field.tag=正在运行任务的git标签
ActionConditionDescriber.field.pullRequest=正在运行的任务的拉取请求
ActionConditionDescriber.field.log=正在运行的任务的日志
ActionConditionDescriber.always=总是
ActionConditionDescriber.successful=已成功
ActionConditionDescriber.failed=已失败
ActionConditionDescriber.cancelled=已取消
ActionConditionDescriber.timedOut=已超时
ActionConditionDescriber.prevIsSuccessful=上一次运行成功
ActionConditionDescriber.prevIsFailed=上一次运行失败
ActionConditionDescriber.prevIsNotFailed=上一次没有运行失败
ActionConditionDescriber.prevIsCancelled=上一次运行被取消了
ActionConditionDescriber.prevIsTimedOut=删一次运行超时了
ValueNeedsToBeEnclosedInBrackets=值需要用圆括号括起来
ReviewRequirementBehavior.numberOfReviewersFromTheGroup=需要几个来自该组的评审者
ReviewRequirementBehavior.hintSpecifyReviewer=从该组中指定评审者
ReviewRequirementBehavior.hintIfNotSpecified=如果未指定，则需要1个来自该组的评审者
ReviewRequirementBehavior.requireOne=需要1个来自该组的评审者
ReviewRequirementBehavior.requireTwo=需要2个来自该组的评审者
ReviewRequirementBehavior.requireThree=需要3个来自该组的评审者
ReviewRequirementBehavior.user=用户
ReviewRequirementBehavior.group=用户组
RetryConditionDescriber.javaRegularExpression=这里期望一个<a href='https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/regex/Pattern.html'>正则表达式</a>
RetryConditionDescriber.field.log=日志
UserMatchDescriber.user=用户
UserMatchDescriber.group=用户组
UserMatchDescriber.role=角色
NotificationReceiverDescriber.committers=代码提交者
NotificationReceiverDescriber.authors=代码作者
NotificationReceiverDescriber.committersSincePreviousSuccessful=自上一次构建成功以来的代码提交者
NotificationReceiverDescriber.authorsSincePreviousSuccessful=自上一次构建成功以来的代码作者
NotificationReceiverDescriber.submitter=构建任务提交者
JobVariableDescriber.projectName=项目名称
JobVariableDescriber.projectPath=项目路径
JobVariableDescriber.jobName=任务名称
JobVariableDescriber.jobToken=任务令牌
JobVariableDescriber.ref=git引用
JobVariableDescriber.branch=分支
JobVariableDescriber.tag=git标签
JobVariableDescriber.commitHash=提交哈希
JobVariableDescriber.buildNumber=构建编号
JobVariableDescriber.buildVersion=构建版本
JobVariableDescriber.pullRequestNumber=拉取请求编号
JobVariableDescriber.issueNumber=问题编号
JobVariableDescriber.server=服务器地址(不包含协议部分)
JobVariableDescriber.serverHost=服务器地址主机部分(不包含端口)
JobVariableDescriber.serverUrl=服务器网址(可以通过浏览器直接访问的URL)
CommandPaletteDescriber.packages=包与镜像列表
CommandPaletteDescriber.rootPackages=全部包与镜像
CommandPaletteDescriber.builds=构建列表
CommandPaletteDescriber.rootBuilds=全部构建列表
CommandPaletteDescriber.issues=问题列表
CommandPaletteDescriber.rootIssues=全部问题列表
CommandPaletteDescriber.pulls=拉取请求列表
CommandPaletteDescriber.rootPulls=全部拉取请求
CommandPaletteDescriber.projects=全部项目
CommandPaletteDescriber.projectsNew=创建项目
CommandPaletteDescriber.boards=问题看板
CommandPaletteDescriber.branches=分支列表
CommandPaletteDescriber.children=子项目列表
CommandPaletteDescriber.codeComments=代码评论
CommandPaletteDescriber.commits=提交历史
CommandPaletteDescriber.compare=代码比较
CommandPaletteDescriber.files=源文件列表
CommandPaletteDescriber.issuesNew=创建问题
CommandPaletteDescriber.iterations=迭代列表
CommandPaletteDescriber.iterationsNew=创建迭代
CommandPaletteDescriber.pullsNew=创建拉取请求
CommandPaletteDescriber.tags=git标签
CommandPaletteDescriber.timesheets=工时统计表
CommandPaletteDescriber.settings.avatarEdit=项目设置/编辑项目图标
CommandPaletteDescriber.settings.branchProtection=项目设置/分支保护
CommandPaletteDescriber.settings.build.buildPreserveRules=项目设置/构建/构建保留规则
CommandPaletteDescriber.settings.build.cacheManagement=项目设置/构建/缓存管理
CommandPaletteDescriber.settings.build.defaultFixedIssuesFilter=项目设置/构建/默认的已修复问题过滤器
CommandPaletteDescriber.settings.build.jobProperties=项目设置/构建/任务属性
CommandPaletteDescriber.settings.build.jobSecrets=项目设置/构建/任务密钥
CommandPaletteDescriber.settings.codeAnalysis=项目设置/代码分析
CommandPaletteDescriber.settings.discordNotifications=项目设置/discord通知
CommandPaletteDescriber.settings.general=项目设置/常规设置
CommandPaletteDescriber.settings.gitPackConfig=项目设置/git pack设置
CommandPaletteDescriber.settings.groupAuthorizations=项目设置/用户组授权
CommandPaletteDescriber.settings.ntfyshNotifications=项目设置/ntfy.sh通知
CommandPaletteDescriber.settings.pullRequest=项目设置/拉取请求设置
CommandPaletteDescriber.settings.slackNotifications=项目设置/slack通知
CommandPaletteDescriber.settings.tagProtection=项目设置/git标签保护
CommandPaletteDescriber.settings.userAuthorizations=项目设置/用户授权
CommandPaletteDescriber.settings.webHooks=项目设置/webhooks
CommandPaletteDescriber.settings.servicedesk=项目设置/服务台邮件地址设置
CommandPaletteDescriber.settings.slackNotificationSetting=项目设置/slack通知设置
CommandPaletteDescriber.settings.discordNotificationSetting=项目设置/discord通知设置
CommandPaletteDescriber.settings.ntfyNotificationSetting=项目设置/ntfy.sh通知设置
CommandPaletteDescriber.settings.dingtalkNotificationSetting=项目设置/钉钉通知设置
CommandPaletteDescriber.settings.wecomNotificationSetting=项目设置/企业微信通知设置
CommandPaletteDescriber.stats.build.duration=统计/构建时长
CommandPaletteDescriber.stats.build.frequency=统计/构建次数
CommandPaletteDescriber.stats.code.contribs=统计/代码贡献
CommandPaletteDescriber.stats.code.lines=统计/代码行数
CommandPaletteDescriber.stats.coverage=统计/覆盖率
CommandPaletteDescriber.stats.issue.duration=统计/问题状态时长
CommandPaletteDescriber.stats.issue.frequency=统计/问题状态转换次数
CommandPaletteDescriber.stats.pullRequest.duration=统计/拉取请求时长
CommandPaletteDescriber.stats.pullRequest.frequency=统计/拉取请求次数
CommandPaletteDescriber.stats.unitTest=统计/单元测试
CommandPaletteDescriber.stats.problem=统计/代码问题统计
CommandPaletteDescriber.admin.agents=管理员/任务代理
CommandPaletteDescriber.admin.groups=管理员/用户组
CommandPaletteDescriber.admin.groupsNew=管理员/新建用户组
CommandPaletteDescriber.admin.invitations=管理员/邀请用户列表
CommandPaletteDescriber.admin.invitationsNew=管理员/邀请用户
CommandPaletteDescriber.admin.labels=管理员/分类标签
CommandPaletteDescriber.admin.roles=管理员/角色列表
CommandPaletteDescriber.admin.rolesNew=管理员/创建新角色
CommandPaletteDescriber.admin.serverInformation=管理员/服务器信息
CommandPaletteDescriber.admin.serverLog=管理员/服务器日志
CommandPaletteDescriber.admin.settings.alert=管理员/告警设置
CommandPaletteDescriber.admin.settings.authenticator=管理员/外部认证设置
CommandPaletteDescriber.admin.settings.backup=管理员/备份设置
CommandPaletteDescriber.admin.settings.branding=管理员/您的品牌设置
CommandPaletteDescriber.admin.settings.checkIssueIntegrity=管理员/问题一致性检查设置
CommandPaletteDescriber.admin.settings.commitMessageFixPatterns=管理员/在提交消息中修复问题设置
CommandPaletteDescriber.admin.settings.emailTemplates.alert=管理员/邮件模版设置/系统告警
CommandPaletteDescriber.admin.settings.emailTemplates.buildNotification=管理员/邮件模版设置/构建通知
CommandPaletteDescriber.admin.settings.emailTemplates.commitNotification=管理员/邮件模版设置/提交通知
CommandPaletteDescriber.admin.settings.emailTemplates.emailVerification=管理员/邮件模版设置/邮件验证
CommandPaletteDescriber.admin.settings.emailTemplates.issueNotification=管理员/邮件模版设置/问题通知
CommandPaletteDescriber.admin.settings.emailTemplates.issueNotificationUnsubscribed=管理员/邮件模版设置/取消问题通知
CommandPaletteDescriber.admin.settings.emailTemplates.packNotification=管理员/邮件模版设置/包与镜像库通知
CommandPaletteDescriber.admin.settings.emailTemplates.passwordReset=管理员/邮件模版设置/密码重置
CommandPaletteDescriber.admin.settings.emailTemplates.pullRequestNotification=管理员/邮件模版设置/拉取请求通知
CommandPaletteDescriber.admin.settings.emailTemplates.pullRequestNotificationUnsubscribed=管理员/邮件模版设置/取消拉取请求通知
CommandPaletteDescriber.admin.settings.emailTemplates.serviceDeskIssueOpenFailed=管理员/邮件模版设置/服务台问题开启失败通知
CommandPaletteDescriber.admin.settings.emailTemplates.serviceDeskIssueOpened=管理员/邮件模版设置/服务台问题开启通知
CommandPaletteDescriber.admin.settings.emailTemplates.stopwatchOverdue=管理员/邮件模版设置/问题逾期通知
CommandPaletteDescriber.admin.settings.emailTemplates.userInvitation=管理员/邮件模版设置/邀请用户通知
CommandPaletteDescriber.admin.settings.gpgSigningKey=管理员/gpg签名密钥设置
CommandPaletteDescriber.admin.settings.gpgTrustedKeys=管理员/受信的gpg密钥设置
CommandPaletteDescriber.admin.settings.groovyScripts=管理员/groovy脚本设置
CommandPaletteDescriber.admin.settings.issueBoards=管理员/问题默认看板设置
CommandPaletteDescriber.admin.settings.issueFields=管理员/问题自定义字段设置
CommandPaletteDescriber.admin.settings.issueLinks=管理员/问题关联设置
CommandPaletteDescriber.admin.settings.issueStates=管理员/问题状态设置
CommandPaletteDescriber.admin.settings.issueTemplates=管理员/问题描述模版设置
CommandPaletteDescriber.admin.settings.jobExecutors=管理员/任务执行器设置
CommandPaletteDescriber.admin.settings.mailService=管理员/邮件服务设置
CommandPaletteDescriber.admin.settings.performance=管理员/性能设置
CommandPaletteDescriber.admin.settings.security=管理员/安全设置
CommandPaletteDescriber.admin.settings.serviceDeskSetting=管理员/服务台设置
CommandPaletteDescriber.admin.settings.sshServerKey=管理员/服务器ssh私钥设置
CommandPaletteDescriber.admin.settings.ssoConnectors=管理员/第三方登录设置
CommandPaletteDescriber.admin.settings.stateTransitions=管理员/问题状态转换设置
CommandPaletteDescriber.admin.settings.system=管理员/系统设置
CommandPaletteDescriber.admin.settings.storagesetting=管理员/存储设置
CommandPaletteDescriber.admin.settings.timetracking=管理员/时间跟踪设置
CommandPaletteDescriber.admin.subscriptionManagement=管理员/订阅管理
CommandPaletteDescriber.admin.users=管理员/用户管理
CommandPaletteDescriber.admin.cluster=管理员/集群和高可用
CommandPaletteDescriber.admin.usersNew=管理员/创建用户
CommandPaletteDescriber.administrator=管理员页面
CommandPaletteDescriber.my=我的基本信息
CommandPaletteDescriber.my.accessTokens=我的访问令牌
CommandPaletteDescriber.my.avatar=编辑我的头像
CommandPaletteDescriber.my.emailAddresses=配置我的邮件地址
CommandPaletteDescriber.my.gpgKeys=配置我的gpg密钥
CommandPaletteDescriber.my.password=修改我的密码
CommandPaletteDescriber.my.preferences=我的偏好设置
CommandPaletteDescriber.my.sshKeys=配置我的ssh密钥
CommandPaletteDescriber.my.twoFactorAuthentication=配置我的双因素认证
CommandPaletteDescriber.codesearch.files=超级代码搜索/文件搜索
CommandPaletteDescriber.codesearch.text=超级代码搜索/文本搜索
CommandPaletteDescriber.codesearch.symbols=超级代码搜索/符号搜索
CommandPaletteParamDescriber.commitParam=请输入分支名/标签名/提交号以便进一步补全提示
CommandPaletteParamDescriber.revisionAndPathParam.searchInDefaultBranch=请输入以便在默认分支中搜索
CommandPaletteParamDescriber.revisionAndPathParam.tooGeneral=该查询过于笼统
OrderByDescriber.status=状态
OrderByDescriber.createDate=创建时间
OrderByDescriber.lastActivityDate=最后活跃日期
OrderByDescriber.replyCount=回复数量
OrderByDescriber.path=路径
OrderByDescriber.id=项目id
OrderByDescriber.name=名称
OrderByDescriber.key=简写
OrderByDescriber.serviceDeskEmailAddress=服务台邮件地址
OrderByDescriber.lastCommitDate=最后提交日期
OrderByDescriber.ipAddress=ip地址
OrderByDescriber.os=操作系统
OrderByDescriber.osVersion=操作系统版本
OrderByDescriber.osArch=操作系统架构
OrderByDescriber.lastUsedDate=最后使用日期
OrderByDescriber.job=构建任务
OrderByDescriber.number=编号
OrderByDescriber.submitDate=提交日期
OrderByDescriber.pendingDate=排队日期
OrderByDescriber.runningDate=运行日期
OrderByDescriber.finishDate=完成日期
OrderByDescriber.project=项目
OrderByDescriber.commit=提交
OrderByDescriber.closeDate=关闭日期
OrderByDescriber.targetProject=目标项目
OrderByDescriber.sourceProject=源项目
OrderByDescriber.targetBranch=目标分支
OrderByDescriber.sourceBranch=源分支
OrderByDescriber.commentCount=评论数量
OrderByDescriber.voteCount=投票数量
OrderByDescriber.state=状态
OrderByDescriber.estimatedTime=预估时间
OrderByDescriber.spentTime=花费时间
OrderByDescriber.progress=进度
OrderByDescriber.boardPosition=看板位置
OrderByDescriber.type=类型
OrderByDescriber.priority=优先级
OrderByDescriber.version=版本
OrderByDescriber.publishDate=发布日期
##### END 注意 这些都是提示器说明国际化，提示器有值和说明两部分，英文版的无需额外说明，所以国际化值都是空的，这是正常的。######

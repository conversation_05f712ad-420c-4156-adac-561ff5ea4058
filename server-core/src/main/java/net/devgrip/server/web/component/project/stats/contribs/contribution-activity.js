onedev.server.contributionActivity = {
	onDomReady: function(contributionActivitys, noDataLabel, componentId, maxValue) {
		if (contributionActivitys) {
			return
		}
		var $chart = $("#" + componentId + " .contribution-activity>.chart");

		const data = Object.keys(contributionActivitys).map(function (day) {
			return [
				parseInt(day),
				contributionActivitys[day]
			];
		}).sort(function (a, b) {
			return parseInt(a) - parseInt(b);
		});

		let maxYAxis = parseInt(maxValue);

		const dayList = data.map(function (item) {
			return item[0];
		});
		const valueList = data.map(function (item) {
			return item[1];
		});

		var myChart = echarts.init($chart[0]);
		var option;
		option = {
			title: {
				show:false,
				left: 'center',
				text: 'Gradient along the y axis'
			},
			tooltip: {
				show:false,
				trigger: 'axis'
			},
			xAxis: {
				show:false,
				data: dayList
			},
			yAxis: {
				show:false,
				min:0,
				max:maxYAxis
			},
			grid: {
				left: 0,
				right: 0,
				top: 0,
				bottom: 0,
				containLabel: false
			},
			series: [
				{
					type: 'line',
					showSymbol: false,
					data: valueList
				}
			]
		};

		option && myChart.setOption(option);

		myChart.on("resized", function() {
			myChart.resize();
		});
	}
}

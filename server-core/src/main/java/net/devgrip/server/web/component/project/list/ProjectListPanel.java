package net.devgrip.server.web.component.project.list;

import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.BuildManager;
import net.devgrip.server.entitymanager.IssueManager;
import net.devgrip.server.entitymanager.PackManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.PullRequestManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.imports.ProjectImporter;
import net.devgrip.server.imports.ProjectImporterContribution;
import net.devgrip.server.model.Build;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.ProjectLastEventDate;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.support.administration.GlobalIssueSetting;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.search.entity.EntitySort;
import net.devgrip.server.search.entity.EntitySort.Direction;
import net.devgrip.server.search.entity.project.ChildrenOfCriteria;
import net.devgrip.server.search.entity.project.FuzzyCriteria;
import net.devgrip.server.search.entity.project.ProjectQuery;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.security.permission.CreateChildren;
import net.devgrip.server.util.DateUtils;
import net.devgrip.server.util.ProjectBuildStatusStat;
import net.devgrip.server.util.ProjectIssueStateStat;
import net.devgrip.server.util.ProjectPackTypeStat;
import net.devgrip.server.util.ProjectPullRequestStatusStat;
import net.devgrip.server.util.facade.ProjectCache;
import net.devgrip.server.util.facade.ProjectFacade;
import net.devgrip.server.web.WebConstants;
import net.devgrip.server.web.WebSession;
import net.devgrip.server.web.behavior.ProjectQueryBehavior;
import net.devgrip.server.web.component.AjaxLazyLoadPanel;
import net.devgrip.server.web.component.datatable.DefaultDataTable;
import net.devgrip.server.web.component.datatable.selectioncolumn.SelectionColumn;
import net.devgrip.server.web.component.entity.labels.EntityLabelsPanel;
import net.devgrip.server.web.component.floating.FloatingPanel;
import net.devgrip.server.web.component.link.ActionablePageLink;
import net.devgrip.server.web.component.link.DropdownLink;
import net.devgrip.server.web.component.menu.MenuItem;
import net.devgrip.server.web.component.menu.MenuLink;
import net.devgrip.server.web.component.modal.confirm.ConfirmModalPanel;
import net.devgrip.server.web.component.project.ProjectAvatar;
import net.devgrip.server.web.component.project.childrentree.ProjectChildrenTree;
import net.devgrip.server.web.component.project.selector.ProjectSelector;
import net.devgrip.server.web.component.project.stats.build.BuildStatsPanel;
import net.devgrip.server.web.component.project.stats.code.CodeStatsPanel;
import net.devgrip.server.web.component.project.stats.codelang.SourceCodePrimaryLangPanel;
import net.devgrip.server.web.component.project.stats.contribs.ContributionActivityPanel;
import net.devgrip.server.web.component.project.stats.issue.IssueStatsPanel;
import net.devgrip.server.web.component.project.stats.pack.PackStatsPanel;
import net.devgrip.server.web.component.project.stats.pullrequest.PullRequestStatsPanel;
import net.devgrip.server.web.component.savedquery.SavedQueriesClosed;
import net.devgrip.server.web.component.savedquery.SavedQueriesOpened;
import net.devgrip.server.web.component.sortedit.SortEditPanel;
import net.devgrip.server.web.page.base.BasePage;
import net.devgrip.server.web.page.project.NewProjectPage;
import net.devgrip.server.web.page.project.children.ProjectChildrenPage;
import net.devgrip.server.web.page.project.dashboard.ProjectDashboardPage;
import net.devgrip.server.web.page.project.imports.ProjectImportPage;
import net.devgrip.server.web.util.LoadableDetachableDataProvider;
import net.devgrip.server.web.util.QuerySaveSupport;
import net.devgrip.server.web.util.WicketUtils;
import net.devgrip.server.web.util.paginghistory.PagingHistorySupport;
import org.apache.commons.lang3.StringUtils;
import org.apache.wicket.Component;
import org.apache.wicket.Session;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.event.Broadcast;
import org.apache.wicket.event.IEvent;
import org.apache.wicket.extensions.markup.html.repeater.data.grid.ICellPopulator;
import org.apache.wicket.extensions.markup.html.repeater.data.table.AbstractColumn;
import org.apache.wicket.extensions.markup.html.repeater.data.table.DataTable;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortableDataProvider;
import org.apache.wicket.feedback.FencedFeedbackPanel;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.Item;
import org.apache.wicket.model.AbstractReadOnlyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.ResourceModel;
import org.apache.wicket.model.StringResourceModel;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static net.devgrip.server.model.Project.SORT_FIELDS;
import static org.apache.wicket.model.Model.ofMap;

public class ProjectListPanel extends Panel {
	
	private static final int MAX_DESCRIPTION_LEN = 1000;
	
	private final IModel<String> queryStringModel;
	
	private final int expectedCount;
	
	private final IModel<ProjectQuery> queryModel = new LoadableDetachableModel<>() {

		@Override
		protected ProjectQuery load() {
			ProjectQuery baseQuery = getBaseQuery();
			if (getParentProject() != null)
				baseQuery = ProjectQuery.merge(baseQuery, new ProjectQuery(new ChildrenOfCriteria(getParentProject().getPath())));
			return parse(queryStringModel.getObject(), baseQuery);
		}

	};
	
	private final IModel<List<ProjectIssueStateStat>> issueStatsModel =
			new LoadableDetachableModel<>() {

				@Override
				protected List<ProjectIssueStateStat> load() {
					List<Project> projects = new ArrayList<>();
					for (Component row : (WebMarkupContainer) projectsTable.get("body").get("rows"))
						projects.add((Project) row.getDefaultModelObject());
					return AppServer.getInstance(IssueManager.class).queryStateStats(projects);
				}

			}; 
	
	private final IModel<List<ProjectBuildStatusStat>> buildStatsModel =
			new LoadableDetachableModel<>() {

				@Override
				protected List<ProjectBuildStatusStat> load() {
					List<Project> projects = new ArrayList<>();
					for (Component row : (WebMarkupContainer) projectsTable.get("body").get("rows"))
						projects.add((Project) row.getDefaultModelObject());
					return AppServer.getInstance(BuildManager.class).queryStatusStats(projects);
				}

			};

	private final IModel<List<ProjectPackTypeStat>> packStatsModel =
			new LoadableDetachableModel<>() {

				@Override
				protected List<ProjectPackTypeStat> load() {
					List<Project> projects = new ArrayList<>();
					for (Component row : (WebMarkupContainer) projectsTable.get("body").get("rows"))
						projects.add((Project) row.getDefaultModelObject());
					return AppServer.getInstance(PackManager.class).queryTypeStats(projects);
				}

			};

	private final IModel<List<ProjectPullRequestStatusStat>> pullRequestStatsModel =
			new LoadableDetachableModel<>() {

				@Override
				protected List<ProjectPullRequestStatusStat> load() {
					List<Project> projects = new ArrayList<>();
					for (Component row : (WebMarkupContainer) projectsTable.get("body").get("rows"))
						projects.add((Project) row.getDefaultModelObject());
					return AppServer.getInstance(PullRequestManager.class).queryStatusStats(projects);
				}

			};
	private Component countLabel;
	
	private DataTable<Project, Void> projectsTable;	
	
	private SelectionColumn<Project, Void> selectionColumn;
	
	private SortableDataProvider<Project, Void> dataProvider;	
	
	private WebMarkupContainer body;
	
	private Component saveQueryLink;	
	
	private TextField<String> queryInput;
	
	private boolean querySubmitted = true;
	
	public ProjectListPanel(String id, IModel<String> queryModel, int expectedCount) {
		super(id);
		this.queryStringModel = queryModel;
		this.expectedCount = expectedCount;
	}
	
	private ProjectManager getProjectManager() {
		return AppServer.getInstance(ProjectManager.class);
	}
	
	@Override
	protected void onDetach() {
		pullRequestStatsModel.detach();
		buildStatsModel.detach();
		issueStatsModel.detach();
		queryStringModel.detach();
		queryModel.detach();
		super.onDetach();
	}
	
	@Nullable
	protected PagingHistorySupport getPagingHistorySupport() {
		return null;
	}
	
	@Nullable
	protected QuerySaveSupport getQuerySaveSupport() {
		return null;
	}

	private void doQuery(AjaxRequestTarget target) {
		projectsTable.setCurrentPage(0);
		target.add(countLabel);
		target.add(body);
		if (selectionColumn != null)
			selectionColumn.getSelections().clear();
		querySubmitted = true;
		if (SecurityUtils.getAuthUser() != null && getQuerySaveSupport() != null)
			target.add(saveQueryLink);
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();

		add(new AjaxLink<Void>("showSavedQueries") {

			@Override
			public void onEvent(IEvent<?> event) {
				super.onEvent(event);
				if (event.getPayload() instanceof SavedQueriesClosed) 
					((SavedQueriesClosed) event.getPayload()).getHandler().add(this);
			}
			
			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(getQuerySaveSupport() != null && !getQuerySaveSupport().isSavedQueriesVisible());
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				send(getPage(), Broadcast.BREADTH, new SavedQueriesOpened(target));
				target.add(this);
			}
			
		}.setOutputMarkupPlaceholderTag(true));

		add(saveQueryLink = new AjaxLink<Void>("saveQuery") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setEnabled(querySubmitted && queryModel.getObject() != null);
				setVisible(SecurityUtils.getAuthUser() != null && getQuerySaveSupport() != null);
			}

			@Override
			protected void onComponentTag(ComponentTag tag) {
				super.onComponentTag(tag);
				configure();
				if (!isEnabled()) 
					tag.append("class", "disabled", " ");
				if (!querySubmitted)
					tag.put("title", new ResourceModel("Qns", "Query not submitted").getObject());
				else if (queryModel.getObject() == null)
					tag.put("title", new ResourceModel("CanNotSaveMq", "Can not save malformed query").getObject());
			}

			@Override
			public void onClick(AjaxRequestTarget target) {
				getQuerySaveSupport().onSaveQuery(target, queryModel.getObject().toString());
			}		
			
		}.setOutputMarkupPlaceholderTag(true));
		
		add(new DropdownLink("filter") {
			@Override
			protected Component newContent(String id, FloatingPanel dropdown) {
				return new ProjectFilterPanel(id, new IModel<EntityQuery<Project>>() {
					@Override
					public void detach() {
					}
					@Override
					public EntityQuery<Project> getObject() {
						var query = parse(queryStringModel.getObject(), new ProjectQuery());
						return query!=null? query : new ProjectQuery();
					}
					@Override
					public void setObject(EntityQuery<Project> object) {
						ProjectListPanel.this.getFeedbackMessages().clear();
						queryStringModel.setObject(object.toString());
						var target = RequestCycle.get().find(AjaxRequestTarget.class);
						target.add(queryInput);
						doQuery(target);
					}
				});
			}
		});

		add(new DropdownLink("orderBy") {

			@Override
			protected Component newContent(String id, FloatingPanel dropdown) {
				Map<String, Direction> sortFields = new LinkedHashMap<>();
				for (var entry: SORT_FIELDS.entrySet())
					sortFields.put(entry.getKey(), entry.getValue().getDefaultDirection());
				return new SortEditPanel<Project>(id, sortFields, new IModel<List<EntitySort>> () {

					@Override
					public void detach() {
					}

					@Override
					public List<EntitySort> getObject() {
						var query = parse(queryStringModel.getObject(), new ProjectQuery());
						return query!=null? query.getSorts() : new ArrayList<>();
					}

					@Override
					public void setObject(List<EntitySort> object) {
						ProjectQuery query = parse(queryStringModel.getObject(), new ProjectQuery());
						ProjectListPanel.this.getFeedbackMessages().clear();
						if (query == null)
							query = new ProjectQuery();
						query.setSorts(object);
						queryStringModel.setObject(query.toString());
						var target = RequestCycle.get().find(AjaxRequestTarget.class); 
						target.add(queryInput);
						doQuery(target);
					}
					
				});
			}
			
		});
		
		boolean canCreateProjects;
		if (getParentProject() != null)
			canCreateProjects = SecurityUtils.canCreateChildren(getParentProject());
		else
			canCreateProjects = true;
		
		add(new MenuLink("operations") {

			@Override
			protected List<MenuItem> getMenuItems(FloatingPanel dropdown) {
				List<MenuItem> menuItems = new ArrayList<>();
				menuItems.add(new MenuItem() {

					@Override
					public String getLabel() {
						return getString("Move_Selected_Projects_To");
					}

					@Override
					public WebMarkupContainer newLink(String id) {
						return new DropdownLink(id) {

							@Override
							protected Component newContent(String id, FloatingPanel dropdown2) {
								return new ProjectSelector(id, new LoadableDetachableModel<List<Project>>() {
				
									@Override
									protected List<Project> load() {
										return getTargetProjects();
									}
									
								}, new LoadableDetachableModel<List<Project>>() {
									@Override
									protected List<Project> load() {
										String queryString = getProjectManager().getFavoriteQuery();
										if (queryString == null) {
											return new ArrayList<>();
										}
										ProjectQuery query = ProjectQuery.parse(queryString);
										return getProjectManager().query(query, false, 0, WebConstants.PAGE_SIZE);
									}
								}) {
				
									@Override
									protected void onSelect(AjaxRequestTarget target, Project project) {
										dropdown.close();
										dropdown2.close();
										
										Long projectId = project.getId();
										String errorMessage = null;
										for (IModel<Project> each: selectionColumn.getSelections()) {
											Project eachProject = each.getObject();
											Map<String,Object> params = new HashMap<>();
											params.put("project", eachProject);
											if (!SecurityUtils.canManageProject(eachProject)) {
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveError.noPrivilage", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											} else if (eachProject.isSelfOrAncestorOf(project)) {
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveError.underItself", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											} else {
												Project projectWithSameName = getProjectManager().find(project, eachProject.getName());
												if (projectWithSameName != null && !projectWithSameName.equals(eachProject)) {
													params.clear();
													params.put("child", eachProject.getName());
													params.put("project", project.getPath());
													StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveError.nameAlreadyUsed", ofMap(params));
													errorMessage = stringResourceModel.getString();
													break;
												}
											}
										}
										
										if (errorMessage != null) {
											getSession().error(errorMessage);
										} else {
											new ConfirmModalPanel(target) {
												
												private Project getTargetProject() {
													return getProjectManager().load(projectId);
												}
												
												@Override
												protected void onConfirm(AjaxRequestTarget target) {
													Collection<Project> projects = new ArrayList<>();
													for (IModel<Project> each: selectionColumn.getSelections()) 
														projects.add(each.getObject());
													getProjectManager().move(projects, getTargetProject());
													target.add(countLabel);
													target.add(body);
													selectionColumn.getSelections().clear();
													Session.get().success(new ResourceModel("ProjectListPanel.moveSuccess", "Projects moved").getObject());
												}
												
												@Override
												protected String getConfirmMessage() {
													Map<String,Object> params = new HashMap<>();
													params.put("project", getTargetProject());
													StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveConfirm", ofMap(params));
													return stringResourceModel.getString();
												}
												
												@Override
												protected String getConfirmInput() {
													return "yes";
												}
												
											};
										}
									}
				
								}.add(AttributeAppender.append("class", "no-current"));
							}
						
							@Override
							protected void onConfigure() {
								super.onConfigure();
								setEnabled(!selectionColumn.getSelections().isEmpty());
							}

							@Override
							protected void onComponentTag(ComponentTag tag) {
								super.onComponentTag(tag);
								configure();
								if (!isEnabled()) {
									tag.put("disabled", "disabled");
									tag.put("title", new ResourceModel("ProjectListPanel.moveTitle", "Please select projects to move").getObject());
								}
							}
							
						};	
					}
					
				});
				
				if (SecurityUtils.canCreateRootProjects()) {
					menuItems.add(new MenuItem() {
	
						@Override
						public String getLabel() {
							return getString("Set_Selected_As_Root_Projects");
						}
	
						@Override
						public WebMarkupContainer newLink(String id) {
							return new AjaxLink<Void>(id) {
	
								@Override
								public void onClick(AjaxRequestTarget target) {
									dropdown.close();
									
									String errorMessage = null;
									for (IModel<Project> each: selectionColumn.getSelections()) {
										Project eachProject = each.getObject();
										Map<String,Object> params = new HashMap<>();
										if (!SecurityUtils.canManageProject(eachProject)) {
											params.put("project", eachProject);
											StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.selectAsRoot.noPrivilage", ofMap(params));
											errorMessage = stringResourceModel.getString();
											break;
										} else {
											Project projectWithSameName = getProjectManager().findByPath(eachProject.getName());
											if (projectWithSameName != null && !projectWithSameName.equals(eachProject)) {
												params.put("project", eachProject.getName());
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.selectAsRoot.nameAlreadyUsed", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											}
										}
									}
									
									if (errorMessage != null) {
										getSession().error(errorMessage);
									} else {
										new ConfirmModalPanel(target) {
											
											@Override
											protected void onConfirm(AjaxRequestTarget target) {
												Collection<Project> projects = new ArrayList<>();
												for (IModel<Project> each: selectionColumn.getSelections()) 
													projects.add(each.getObject());
												getProjectManager().move(projects, null);
												target.add(countLabel);
												target.add(body);
												selectionColumn.getSelections().clear();
												Session.get().success(new ResourceModel("ProjectListPanel.selectAsRootSuccess", "Projects modified").getObject());
											}
											
											@Override
											protected String getConfirmMessage() {
												return new ResourceModel("ProjectListPanel.selectAsRootConfirm").getObject();
											}
											
											@Override
											protected String getConfirmInput() {
												return "yes";
											}
											
										};
									}
								}
								
								@Override
								protected void onConfigure() {
									super.onConfigure();
									setEnabled(!selectionColumn.getSelections().isEmpty());
								}
	
								@Override
								protected void onComponentTag(ComponentTag tag) {
									super.onComponentTag(tag);
									configure();
									if (!isEnabled()) {
										tag.put("disabled", "disabled");
										tag.put("title", new ResourceModel("ProjectListPanel.selectAsRootTitle", "Please select projects to modify").getObject());
									}
								}
								
							};
						}
					});
				}
				
				menuItems.add(new MenuItem() {

					@Override
					public String getLabel() {
						return getString("Delete_Selected_Projects");
					}
					
					@Override
					public WebMarkupContainer newLink(String id) {
						return new AjaxLink<Void>(id) {

							@Override
							public void onClick(AjaxRequestTarget target) {
								dropdown.close();
								
								String errorMessage = null;
								for (IModel<Project> each: selectionColumn.getSelections()) { 
									Project eachProject = each.getObject();
									if (!SecurityUtils.canManageProject(eachProject)) {
										Map<String,Object> params = new HashMap<>();
										params.put("project", eachProject);
										StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.deleteSelected.noPrivilage", ofMap(params));

										errorMessage = stringResourceModel.getString();
										break;
									}
								}
								if (errorMessage != null) {
									getSession().error(errorMessage);
								} else {
									new ConfirmModalPanel(target) {
										
										@Override
										protected void onConfirm(AjaxRequestTarget target) {
											Collection<Project> projects = new ArrayList<>();
											Collection<String> observables = new ArrayList<>(); 
											for (IModel<Project> each: selectionColumn.getSelections()) {
												var project = each.getObject();
												projects.add(project);
												observables.add(project.getDeleteChangeObservable());
											}
											getProjectManager().delete(projects);
											selectionColumn.getSelections().clear();
											target.add(countLabel);
											target.add(body);
											Session.get().success(new ResourceModel("ProjectListPanel.deleteSelectedSuccess", "Projects deleted").getObject());
											var page = (BasePage) getPage();
											page.notifyObservablesChange(target, observables);
										}
										
										@Override
										protected String getConfirmMessage() {
											return new ResourceModel("ProjectListPanel.deleteSelectedConfirm", "Type <code>yes</code> below to delete selected projects").getObject();
										}
										
										@Override
										protected String getConfirmInput() {
											return "yes";
										}
										
									};
								}
							}
							
							@Override
							protected void onConfigure() {
								super.onConfigure();
								setEnabled(!selectionColumn.getSelections().isEmpty());
							}
							
							@Override
							protected void onComponentTag(ComponentTag tag) {
								super.onComponentTag(tag);
								configure();
								if (!isEnabled()) {
									tag.put("disabled", "disabled");
									tag.put("title", new ResourceModel("ProjectListPanel.deleteSelectedTitle", "Please select projects to delete").getObject());
								}
							}
							
						};
					}
					
				});
				
				menuItems.add(new MenuItem() {

					@Override
					public String getLabel() {
						return getString("Move_All_Queried_Projects_To");
					}
					
					@Override
					public WebMarkupContainer newLink(String id) {
						return new DropdownLink(id) {

							@Override
							protected Component newContent(String id, FloatingPanel dropdown2) {
								return new ProjectSelector(id, new LoadableDetachableModel<List<Project>>() {
				
									@Override
									protected List<Project> load() {
										return getTargetProjects();
									}
									
								}, new LoadableDetachableModel<List<Project>>() {
									@Override
									protected List<Project> load() {
										String queryString = getProjectManager().getFavoriteQuery();
										if (queryString == null) {
											return new ArrayList<>();
										}
										ProjectQuery query = ProjectQuery.parse(queryString);
										return getProjectManager().query(query, false, 0, WebConstants.PAGE_SIZE);
									}
								}) {
				
									@SuppressWarnings("unchecked")
									@Override
									protected void onSelect(AjaxRequestTarget target, Project project) {
										dropdown.close();
										dropdown2.close();
										
										Long projectId = project.getId();
										String errorMessage = null;
										for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) {
											Project eachProject = it.next();
											Map<String,Object> params = new HashMap<>();
											params.put("project", eachProject);
											if (!SecurityUtils.canManageProject(eachProject)) {
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveAllQueriedError.noPrivilage", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											} else if (eachProject.isSelfOrAncestorOf(project)) {
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveAllQueriedError.underItself", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											} else {
												Project projectWithSameName = getProjectManager().find(project, eachProject.getName());
												if (projectWithSameName != null && !projectWithSameName.equals(eachProject)) {
													params.clear();
													params.put("child", eachProject.getName());
													params.put("project", project.getPath());
													StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveAllQueriedError.nameAlreadyUsed", ofMap(params));
													errorMessage = stringResourceModel.getString();
													break;
												}
											}
										}
										
										if (errorMessage != null) {
											getSession().error(errorMessage);
										} else {
											new ConfirmModalPanel(target) {
												
												private Project getTargetProject() {
													return getProjectManager().load(projectId);
												}
												
												@Override
												protected void onConfirm(AjaxRequestTarget target) {
													Collection<Project> projects = new ArrayList<>();
													for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) 
														projects.add(it.next());
													getProjectManager().move(projects, getTargetProject());
													dataProvider.detach();
													target.add(countLabel);
													target.add(body);
													selectionColumn.getSelections().clear();
													Session.get().success(new ResourceModel("ProjectListPanel.moveAllQueriedSuccess", "Projects moved").getObject());
												}
												
												@Override
												protected String getConfirmMessage() {
													Map<String,Object> params = new HashMap<>();
													params.put("project", getTargetProject());
													StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.moveAllQueriedConfirm", ofMap(params));

													return stringResourceModel.getString();
												}
												
												@Override
												protected String getConfirmInput() {
													return "yes";
												}
												
											};
										}
									}
				
								}.add(AttributeAppender.append("class", "no-current"));
							}
						
							@Override
							protected void onConfigure() {
								super.onConfigure();
								setEnabled(projectsTable.getItemCount() != 0);
							}
							
							@Override
							protected void onComponentTag(ComponentTag tag) {
								super.onComponentTag(tag);
								configure();
								if (!isEnabled()) {
									tag.put("disabled", "disabled");
									tag.put("title", new ResourceModel("ProjectListPanel.moveAllQueriedTitle", "No projects to move").getObject());
								}
							}
							
						};	
					}
					
				});
				
				if (SecurityUtils.canCreateRootProjects()) {
					menuItems.add(new MenuItem() {

						@Override
						public String getLabel() {
							return getString("Set_All_Queried_As_Root_Projects");
						}
						
						@Override
						public WebMarkupContainer newLink(String id) {
							return new AjaxLink<Void>(id) {

								@SuppressWarnings("unchecked")
								@Override
								public void onClick(AjaxRequestTarget target) {
									dropdown.close();
									String errorMessage = null;
									for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) {
										Project eachProject = it.next();
										Map<String,Object> params = new HashMap<>();
										if (!SecurityUtils.canManageProject(eachProject)) {
											params.put("project", eachProject);
											StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.selectAllQueriedAsRoot.noPrivilage", ofMap(params));
											errorMessage = stringResourceModel.getString();
											break;
										} else {
											Project projectWithSameName = getProjectManager().findByPath(eachProject.getName());
											if (projectWithSameName != null && !projectWithSameName.equals(eachProject)) {
												params.put("project", eachProject.getName());
												StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.selectAllQueriedAsRoot.nameAlreadyUsed", ofMap(params));
												errorMessage = stringResourceModel.getString();
												break;
											}
										}
									}
									
									if (errorMessage != null) {
										getSession().error(errorMessage);
									} else {
										new ConfirmModalPanel(target) {
											
											@Override
											protected void onConfirm(AjaxRequestTarget target) {
												Collection<Project> projects = new ArrayList<>();
												for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) 
													projects.add(it.next());
												getProjectManager().move(projects, null);
												dataProvider.detach();
												target.add(countLabel);
												target.add(body);
												selectionColumn.getSelections().clear();
												Session.get().success(new ResourceModel("ProjectListPanel.selectAllQueriedAsRootSuccess", "Projects modified").getObject());
											}
											
											@Override
											protected String getConfirmMessage() {
												return new ResourceModel("ProjectListPanel.selectAllQueriedAsRootConfirm", "Type <code>yes</code> below to set all queried as root projects").getObject();
											}
											
											@Override
											protected String getConfirmInput() {
												return "yes";
											}
											
										};	
									}
								}
								
								@Override
								protected void onConfigure() {
									super.onConfigure();
									setEnabled(projectsTable.getItemCount() != 0);
								}
								
								@Override
								protected void onComponentTag(ComponentTag tag) {
									super.onComponentTag(tag);
									configure();
									if (!isEnabled()) {
										tag.put("disabled", "disabled");
										tag.put("title", new ResourceModel("ProjectListPanel.selectAllQueriedAsRootTitle", "No projects to modify").getObject());
									}
								}
								
							};
						}
						
					});
				}
				
				menuItems.add(new MenuItem() {

					@Override
					public String getLabel() {
						return getString("Delete_All_Queried_Projects");
					}
					
					@Override
					public WebMarkupContainer newLink(String id) {
						return new AjaxLink<Void>(id) {

							@SuppressWarnings("unchecked")
							@Override
							public void onClick(AjaxRequestTarget target) {
								dropdown.close();
								
								String errorMessage = null;
								for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) {
									Project eachProject = it.next();
									if (!SecurityUtils.canManageProject(eachProject)) {
										Map<String,Object> params = new HashMap<>();
										params.put("project", eachProject);
										StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.deleteAllQueried.noPrivilage", ofMap(params));

										errorMessage = stringResourceModel.getString();
										break;
									}
								}
								
								if (errorMessage != null) {
									getSession().error(errorMessage);
								} else {
									new ConfirmModalPanel(target) {
										
										@Override
										protected void onConfirm(AjaxRequestTarget target) {
											Collection<Project> projects = new ArrayList<>();
											Collection<String> observables = new ArrayList<>();
											for (Iterator<Project> it = (Iterator<Project>) dataProvider.iterator(0, projectsTable.getItemCount()); it.hasNext();) {
												var project = it.next();
												projects.add(project);
												observables.add(project.getDeleteChangeObservable());
											}
											getProjectManager().delete(projects);
											dataProvider.detach();
											selectionColumn.getSelections().clear();
											target.add(countLabel);
											target.add(body);
											Session.get().success(new ResourceModel("ProjectListPanel.deleteAllQueriedSuccess", "Projects deleted").getObject());
											var page = (BasePage) getPage();
											page.notifyObservablesChange(target, observables);
										}
										
										@Override
										protected String getConfirmMessage() {
											return new ResourceModel("ProjectListPanel.deleteAllQueriedConfirm", "Type <code>yes</code> below to delete all queried projects").getObject();
										}
										
										@Override
										protected String getConfirmInput() {
											return "yes";
										}
										
									};
								}
								
							}
							
							@Override
							protected void onConfigure() {
								super.onConfigure();
								setEnabled(projectsTable.getItemCount() != 0);
							}
							
							@Override
							protected void onComponentTag(ComponentTag tag) {
								super.onComponentTag(tag);
								configure();
								if (!isEnabled()) {
									tag.put("disabled", "disabled");
									tag.put("title", new ResourceModel("ProjectListPanel.deleteAllQueriedTitle", "No projects to delete").getObject());
								}
							}
							
						};
					}
					
				});
				
				return menuItems;
			}
			
			private List<Project> getTargetProjects() {
				CreateChildren permission = new CreateChildren();
				Collection<Project> authorizedProjects = SecurityUtils.getAuthorizedProjects(permission);
				List<Project> projects = new ArrayList<>(authorizedProjects);
				ProjectCache projectCache = WicketUtils.getProjectCache();
				projects.sort(projectCache.comparingPath());
				return projects;
			}

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(SecurityUtils.getAuthUser() != null);
			}

			@Override
			protected String getHelp() {
				if (!SecurityUtils.isAdministrator())
					return new ResourceModel("ProjectListPanel.helpMsg", "Permission will be checked upon actual operation").getObject();
				else
					return null;
			}
			
		});
		
		if (getParentProject() == null && canCreateProjects) {
			add(new MenuLink("importProjects") {
	
				@Override
				protected List<MenuItem> getMenuItems(FloatingPanel dropdown) {
					Collection<ProjectImporter> importers = new ArrayList<>();
					List<ProjectImporterContribution> contributions = 
							new ArrayList<>(AppServer.getExtensions(ProjectImporterContribution.class));
					Collections.sort(contributions, new Comparator<ProjectImporterContribution>() {
	
						@Override
						public int compare(ProjectImporterContribution o1, ProjectImporterContribution o2) {
							return o1.getOrder() - o2.getOrder();
						}
						
					});
					
					for (ProjectImporterContribution contribution: contributions)
						importers.addAll(contribution.getImporters());
					
					List<MenuItem> menuItems = new ArrayList<>();
					for (ProjectImporter importer: importers) {
						menuItems.add(new MenuItem() {
	
							@Override
							public String getLabel() {
								Map<String,String> params = new HashMap<>();
								params.put("name", importer.getName());
								StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.import.nameLable", ofMap(params));

								return stringResourceModel.getString();
							}
	
							@Override
							public WebMarkupContainer newLink(String id) {
								return new BookmarkablePageLink<Void>(id, ProjectImportPage.class, 
										ProjectImportPage.paramsOf(importer.getName()));
							}
							
						});
					}
					return menuItems;
				}
				
			});
		} else {
			add(new WebMarkupContainer("importProjects").setVisible(false));
		}
		
		queryInput = new TextField<String>("input", queryStringModel);
		queryInput.setOutputMarkupId(true);
		queryInput.add(new ProjectQueryBehavior(getParentProject() != null) {

			@Override
			protected void onInput(AjaxRequestTarget target, String inputContent) {
				ProjectListPanel.this.getFeedbackMessages().clear();
				querySubmitted = StringUtils.trimToEmpty(queryStringModel.getObject())
						.equals(StringUtils.trimToEmpty(inputContent));
				target.add(saveQueryLink);
			}
			
		});
		
		queryInput.add(new AjaxFormComponentUpdatingBehavior("clear") {
			
			@Override
			protected void onUpdate(AjaxRequestTarget target) {
				doQuery(target);
			}
			
		});
		
		Form<?> queryForm = new Form<Void>("query");
		queryForm.add(queryInput);
		queryForm.add(new AjaxButton("submit") {

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);
				doQuery(target);
			}
			
		});
		add(queryForm);
		
		if (getParentProject() != null) {
			PageParameters params = NewProjectPage.paramsOf(getParentProject());
			add(new BookmarkablePageLink<Void>("addProject", NewProjectPage.class, params)
					.add(AttributeAppender.replace("title", new ResourceModel("ProjectListPanel.AddChildTitle", "Add child project").getObject()))
					.setVisible(canCreateProjects));
		} else {
			add(new BookmarkablePageLink<Void>("addProject", NewProjectPage.class)
					.setVisible(canCreateProjects));
		}

		add(countLabel = new Label("count", new AbstractReadOnlyModel<String>() {
			@Override
			public String getObject() {
				long count = 1L;
				if (dataProvider.size() > 1) {
					count = dataProvider.size();
				}
				Map<String, Object> params = new HashMap<>();
				params.put("count", count);
				StringResourceModel stringResourceModel = new StringResourceModel("ProjectListPanel.foundCount", ofMap(params));
				return stringResourceModel.getString();
			}
		}) {
			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(dataProvider.size() != 0);
			}
		}.setOutputMarkupPlaceholderTag(true));
		
		dataProvider = new LoadableDetachableDataProvider<>() {

			@Override
			public Iterator<? extends Project> iterator(long first, long count) {
				try {
					ProjectQuery query = queryModel.getObject();
					if (query != null)
						return getProjectManager().query(query, true, (int) first, (int) count).iterator();
				} catch (ExplicitException e) {
					error(e.getMessage());
				}
				return new ArrayList<Project>().iterator();
			}

			@Override
			public long calcSize() {
				try {
					ProjectQuery query = queryModel.getObject();
					if (query != null)
						return getProjectManager().count(query.getCriteria());
				} catch (ExplicitException e) {
					error(e.getMessage());
				}
				return 0;
			}

			@Override
			public IModel<Project> model(Project object) {
				Long projectId = object.getId();
				return new LoadableDetachableModel<Project>() {

					@Override
					protected Project load() {
						return getProjectManager().load(projectId);
					}

				};
			}

		};
		
		if (expectedCount != 0 && expectedCount != dataProvider.size())
			warn(new ResourceModel("ProjectListPanel.hiddenMsg", "Some projects might be hidden due to permission policy").getObject());
		
		body = new WebMarkupContainer("body");
		add(body.setOutputMarkupId(true));
		
		body.add(new FencedFeedbackPanel("feedback", this));
		
		List<IColumn<Project, Void>> columns = new ArrayList<>();
		
		if (SecurityUtils.getAuthUser() != null)
			columns.add(selectionColumn = new SelectionColumn<Project, Void>());
		
		columns.add(new AbstractColumn<Project, Void>(Model.of("")) {

			@Override
			public void populateItem(Item<ICellPopulator<Project>> cellItem, String componentId, IModel<Project> rowModel) {
				Project project = rowModel.getObject();
				Fragment fragment = new Fragment(componentId, "projectFrag", ProjectListPanel.this);
				
				Long projectId = project.getId();
				
				ActionablePageLink projectLink = new ActionablePageLink("path", 
						ProjectDashboardPage.class, ProjectDashboardPage.paramsOf(project)) {

					@Override
					protected void doBeforeNav(AjaxRequestTarget target) {
						String redirectUrlAfterDelete = RequestCycle.get().urlFor(
								getPage().getClass(), getPage().getPageParameters()).toString();
						WebSession.get().setRedirectUrlAfterDelete(Project.class, redirectUrlAfterDelete);
					}
					
				};
				
				projectLink.add(new ProjectAvatar("avatar", projectId));
				if (getParentProject() != null)
					projectLink.add(new Label("text", project.getPath().substring(getParentProject().getPath().length()+1)));
				else
					projectLink.add(new Label("text", project.getPath()));
				fragment.add(projectLink);

				fragment.add(new EntityLabelsPanel<>("labels", rowModel));
				if (project.getDescription() != null) {
					fragment.add(new Label("description", StringUtils.abbreviate(project.getDescription(), MAX_DESCRIPTION_LEN)));
				} else {
					fragment.add(new WebMarkupContainer("description").setVisible(false));
				}

				ProjectLastEventDate lastEventDate = project.getLastEventDate();
				if (lastEventDate != null) {
					Map<String, Object> params = new HashMap<>();
					params.put("lastUpdated", DateUtils.formatAge(lastEventDate.getActivity()));
					StringResourceModel updatedOn = new StringResourceModel("Updated_On", ofMap(params));
					fragment.add(new Label("lastActiveAt", updatedOn));
				}else{
					fragment.add(new WebMarkupContainer("lastActiveAt").setVisible(false));
				}

				fragment.add(new AjaxLazyLoadPanel("codeLang") {
					@Override
					public Component getLazyLoadComponent(String markupId) {
						return new SourceCodePrimaryLangPanel(markupId, rowModel, true);
					}
				});

				fragment.add(new AjaxLazyLoadPanel("contributionActivity") {
					@Override
					public Component getLazyLoadComponent(String markupId) {
						return new ContributionActivityPanel(markupId, rowModel);
					}
				});

				WebMarkupContainer toggleMoreStats = new WebMarkupContainer("toggleMoreStats");

				if (project.getActiveServer(false) != null) {
					fragment.add(toggleMoreStats);
					if (project.isCodeManagement() && SecurityUtils.canReadCode(project)) {
						fragment.add(new CodeStatsPanel("codeStats", rowModel));
						fragment.add(new PullRequestStatsPanel("pullRequestStats", rowModel,
								new LoadableDetachableModel<>() {

									@Override
									protected Map<PullRequest.Status, Long> load() {
										Map<PullRequest.Status, Long> statusCounts = new LinkedHashMap<>();
										for (ProjectPullRequestStatusStat stats : pullRequestStatsModel.getObject()) {
											if (stats.getProjectId().equals(projectId))
												statusCounts.put(stats.getPullRequestStatus(), stats.getStatusCount());
										}
										return statusCounts;
									}

								}));
					} else {
						fragment.add(new WebMarkupContainer("codeStats").setVisible(false));
						fragment.add(new WebMarkupContainer("pullRequestStats").setVisible(false));
					}
					
					if (project.isIssueManagement()) {
						fragment.add(new IssueStatsPanel("issueStats", rowModel, new LoadableDetachableModel<>() {

							@Override
							protected Map<Integer, Long> load() {
								Map<Integer, Long> stateCounts = new LinkedHashMap<>();
								GlobalIssueSetting issueSetting = AppServer.getInstance(SettingManager.class).getIssueSetting();
								for (ProjectIssueStateStat stats : issueStatsModel.getObject()) {
									if (stats.getProjectId().equals(projectId)
											&& stats.getStateOrdinal() >= 0
											&& stats.getStateOrdinal() < issueSetting.getStateSpecs().size()) {
										stateCounts.put(stats.getStateOrdinal(), stats.getStateCount());
									}
								}
								return stateCounts;
							}

						}));
					} else {
						fragment.add(new WebMarkupContainer("issueStats"));
					}
					
					if (project.isCodeManagement()) {
						fragment.add(new BuildStatsPanel("buildStats", rowModel, new LoadableDetachableModel<>() {

							@Override
							protected Map<Build.Status, Long> load() {
								Map<Build.Status, Long> statusCounts = new LinkedHashMap<>();
								for (ProjectBuildStatusStat stats : buildStatsModel.getObject()) {
									if (stats.getProjectId().equals(projectId))
										statusCounts.put(stats.getBuildStatus(), stats.getStatusCount());
								}
								return statusCounts;
							}

						}));
					} else {
						fragment.add(new WebMarkupContainer("buildStats").setVisible(false));
					}
					fragment.add(new PackStatsPanel("packStats", rowModel, new LoadableDetachableModel<>() {

						@Override
						protected Map<String, Long> load() {
							Map<String, Long> statusCounts = new LinkedHashMap<>();
							for (ProjectPackTypeStat stats : packStatsModel.getObject()) {
								if (stats.getProjectId().equals(projectId))
									statusCounts.put(stats.getType(), stats.getTypeCount());
							}
							return statusCounts;
						}

					}));
					
					fragment.add(new WebMarkupContainer("noStorage").setVisible(false));
				} else {
					fragment.add(toggleMoreStats.setVisible(false));
					fragment.add(new WebMarkupContainer("codeStats").setVisible(false));
					fragment.add(new WebMarkupContainer("pullRequestStats").setVisible(false));
					fragment.add(new WebMarkupContainer("issueStats").setVisible(false));
					fragment.add(new WebMarkupContainer("buildStats").setVisible(false));
					fragment.add(new WebMarkupContainer("packStats").setVisible(false));
					fragment.add(new WebMarkupContainer("contributionActivity")).setVisible(false);
					fragment.add(new WebMarkupContainer("noStorage"));
				}


				List<ProjectFacade> children = WicketUtils.getProjectCache().getChildren(projectId);
				if (!children.isEmpty()) {
					Fragment childrenFrag = new Fragment("children", "childrenFrag", ProjectListPanel.this);
					childrenFrag.add(new AjaxLink<Void>("toggle") {

						@Override
						public void onClick(AjaxRequestTarget target) {
							if (WebSession.get().getExpandedProjectIds().contains(projectId))
								WebSession.get().getExpandedProjectIds().remove(projectId);
							else
								WebSession.get().getExpandedProjectIds().add(projectId);
							target.add(childrenFrag);
						}
						
					}.add(AttributeAppender.append("class", new LoadableDetachableModel<String>() {

						@Override
						protected String load() {
							return WebSession.get().getExpandedProjectIds().contains(projectId)? "expanded": "collapsed";
						}
						
					})));
					
 					childrenFrag.add(new BookmarkablePageLink<Void>("link", ProjectChildrenPage.class, 
 							ProjectChildrenPage.paramsOf(projectId)) {

						@Override
						protected void onInitialize() {
							super.onInitialize();
							Map<String, Object> count = new HashMap<>();
							count.put("count", children.size());
		 					add(new Label("label", new StringResourceModel("ProjectListPanel.childProjectsCount", ofMap(count))));
						}
 						
 					});
					
					childrenFrag.add(new ProjectChildrenTree("tree", projectId) {
						
						@Override
						protected void onConfigure() {
							super.onConfigure();
							setVisible(WebSession.get().getExpandedProjectIds().contains(projectId));
						}

						@Override
						protected Set<Long> getExpandedProjectIds() {
							return WebSession.get().getExpandedProjectIds();
						}
						
					});
					fragment.add(toggleMoreStats.setVisible(false));
					childrenFrag.setOutputMarkupId(true);
					fragment.add(childrenFrag);
				} else {
					fragment.add(new WebMarkupContainer("children").setVisible(false));
				}
				
				cellItem.add(fragment);
			}
			
		});
		
		body.add(projectsTable = new DefaultDataTable<>("projects", columns, dataProvider,
				WebConstants.PAGE_SIZE, getPagingHistorySupport()));
		
		setOutputMarkupId(true);
	}
	
	@Nullable
	private ProjectQuery parse(@Nullable String queryString, ProjectQuery baseQuery) {
		ProjectQuery parsedQuery;
		try {
			parsedQuery = ProjectQuery.parse(queryString);
		} catch (Exception e) {
			getFeedbackMessages().clear();
			if (e instanceof ExplicitException) {
				error(e.getMessage());
				return null;
			} else {
				Map<String,Object> myParams = new HashMap<>();
				myParams.put("search", "owned by me");
				StringResourceModel stringResourceModel = new StringResourceModel("FuzzyQueryTips", ofMap(myParams));

				info(stringResourceModel.getString());
				parsedQuery = new ProjectQuery(new FuzzyCriteria(queryString));
			}
		}
		return ProjectQuery.merge(baseQuery, parsedQuery);
	}
	
	@Nullable
	protected Project getParentProject() {
		return null;
	}
	
	protected ProjectQuery getBaseQuery() {
		return new ProjectQuery();
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new ProjectListCssResourceReference()));
	}
		
}

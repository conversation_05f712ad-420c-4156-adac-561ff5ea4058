<wicket:panel>
	<div class="project-list card no-autofocus">
		<div class="card-body">
			<div class="d-flex mb-4">
				<form wicket:id="query" class="clearable-wrapper flex-grow-1">
					<div class="input-group">
						<input wicket:id="input" spellcheck="false" autocomplete="off" class="form-control" placeholder="Query/order projects" wicket:message="placeholder:ProjectListPanel.queryPlaceholder">
						<span class="input-group-append">
							<button wicket:id="submit" type="submit" class="btn btn-outline-secondary btn-icon" wicket:message="title:Query"><wicket:svg href="magnify" class="icon"></wicket:svg></button>
						</span>
					</div>
				</form>
				<a wicket:id="addProject" class="btn btn-primary btn-icon flex-shrink-0 ml-3" title="Add project" wicket:message="title:Create"><wicket:svg href="plus" class="icon"/></a>
			</div>
			<div class="operations mb-4">
				<a wicket:id="showSavedQueries" class="show-saved-queries text-gray d-inline-block mb-2 mr-4">
					<wicket:svg href="eye" class="icon"></wicket:svg> <wicket:message key="Show_Saved_Queries" />
				</a>
				<a wicket:id="saveQuery" class="save-query text-gray d-inline-block mb-2 mr-4">
					<wicket:svg href="save" class="icon"></wicket:svg> <wicket:message key="Save_Query" />
				</a>
				<a wicket:id="operations" class="operations d-inline-block mb-2 mr-4 text-gray">
					<wicket:svg href="ellipsis-circle" class="icon"></wicket:svg> <wicket:message key="Operations" />
				</a>
				<a wicket:id="filter" class="filter text-gray mr-4 mb-2 d-inline-block text-nowrap">
					<wicket:svg href="filter" class="icon"></wicket:svg> <wicket:message key="QueryFilter"></wicket:message>
				</a>
				<a wicket:id="orderBy" class="order-by text-gray d-inline-block mb-2 mr-4">
					<wicket:svg href="sort" class="icon"></wicket:svg> <wicket:message key="Order_By" />
				</a>
				<a wicket:id="importProjects" class="import-projects d-inline-block mb-2 mr-4 font-weight-boldest link-info">
					<wicket:svg href="import" class="icon"></wicket:svg> <wicket:message key="Import" />
				</a>
				<span wicket:id="count" class="float-right text-gray"></span>
			</div>
			<div wicket:id="body">
				<div wicket:id="feedback"></div>
				<table wicket:id="projects" class="table"></table>
			</div>
		</div>
	</div>
	<wicket:fragment wicket:id="projectFrag">
		<div class="d-flex align-items-center flex-wrap row-gap-2 font-size-h5">
			<a wicket:id="path" class="mr-2">
				<img wicket:id="avatar">
				<span wicket:id="text"></span>
			</a>
			<a class="btn btn-xs h-75 btn-secondary btn-icon ml-2 mr-2" href="javascript:void(0);" data-tippy-content="Toggle More stats" onclick="$(this).parent().siblings('.toggleMore').toggle(); $(window).resize();" wicket:id="toggleMoreStats" wicket:message="data-tippy-content:ProjectListPanel.toggleMoreInfo">
				<wicket:svg href="ellipsis" class="icon"/>
			</a>
			<span wicket:id="labels" class="mr-2"></span>
		</div>
		<div class="d-flex mt-1 justify-content-between">
			<div class="flex-grow-1">
				<div wicket:id="description"></div>
				<div class="d-flex justify-content-start align-items-center mt-1">
					<div wicket:id="codeLang" class="font-size-sm text-gray flex-shrink-0 mr-3"></div>
					<div wicket:id="lastActiveAt" class="font-size-sm text-muted flex-shrink-0"></div>
				</div>
			</div>
			<div wicket:id="contributionActivity" class="flex-shrink-0 ml-3"></div>
		</div>
		<div class="toggleMore" style="display: none">
			<div class="d-flex flex-wrap project-summary">
				<div wicket:id="codeStats" class="code-stats mt-2 d-flex align-items-center flex-wrap mr-3"></div>
				<div wicket:id="pullRequestStats" class="pull-request-stats mt-2 d-flex align-items-center flex-wrap"></div>
			</div>
			<div class="d-flex flex-wrap project-summary">
				<div wicket:id="issueStats" class="issue-stats mt-2 d-flex align-items-center flex-wrap mr-3"></div>
				<div wicket:id="buildStats" class="build-stats mt-2 d-flex align-items-center flex-wrap mr-3"></div>
				<div wicket:id="packStats" class="pack-stats mt-2 d-flex align-items-center flex-wrap"></div>
			</div>

		</div>

		<div wicket:id="noStorage" class="mt-2 font-italic"><wicket:message key="ProjectListPanel.snf"></wicket:message></div>
		<div wicket:id="children" class="project-summary"></div>
	</wicket:fragment>
	<wicket:fragment wicket:id="childrenFrag">
		<div class="d-flex flex-nowrap align-items-center mt-2">
			<wicket:svg href="subdir" class="icon mr-1"/>
			<a wicket:id="toggle" class="toggle-children mr-1"><wicket:svg href="arrow" class="icon"/></a>
			<a wicket:id="link"><span wicket:id="label"></span></a>
		</div>
		<div wicket:id="tree" class="child-projects mt-2"></div>
	</wicket:fragment>
</wicket:panel>

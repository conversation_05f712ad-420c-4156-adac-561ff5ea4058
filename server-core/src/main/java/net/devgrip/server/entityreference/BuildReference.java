package net.devgrip.server.entityreference;

import net.devgrip.server.model.Project;

import javax.annotation.Nullable;

public class BuildReference extends EntityReference {

	private static final long serialVersionUID = 1L;

	public static final String TYPE = "build";
	public static final String TYPE_I18N_KEY = "Reference.type.build";

	public BuildReference(Project project, Long number) {
		super(project, number);
	}

	public BuildReference(Long projectId, Long number) {
		super(projectId, number);
	}
	
	public static BuildReference of(String referenceString, @Nullable Project currentProject) {
		return (BuildReference) of(TYPE, referenceString, currentProject);
	}

	@Override
	public String getType() {
		return TYPE;
	}

	@Override
	public String getTypeAsI18nKey() {
		return TYPE_I18N_KEY;
	}

}

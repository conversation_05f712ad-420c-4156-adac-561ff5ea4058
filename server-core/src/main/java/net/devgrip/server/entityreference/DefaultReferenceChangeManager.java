package net.devgrip.server.entityreference;

import static net.devgrip.server.entityreference.ReferenceUtils.extractReferences;

import java.util.Date;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import net.devgrip.server.entitymanager.IssueChangeManager;
import net.devgrip.server.entitymanager.IssueManager;
import net.devgrip.server.entitymanager.PullRequestChangeManager;
import net.devgrip.server.entitymanager.PullRequestManager;
import net.devgrip.server.event.Listen;
import net.devgrip.server.event.project.codecomment.CodeCommentCreated;
import net.devgrip.server.event.project.codecomment.CodeCommentEdited;
import net.devgrip.server.event.project.codecomment.CodeCommentReplyCreated;
import net.devgrip.server.event.project.codecomment.CodeCommentReplyEdited;
import net.devgrip.server.event.project.codecomment.CodeCommentStatusChanged;
import net.devgrip.server.event.project.issue.IssueChanged;
import net.devgrip.server.event.project.issue.IssueCommentCreated;
import net.devgrip.server.event.project.issue.IssueCommentEdited;
import net.devgrip.server.event.project.issue.IssueOpened;
import net.devgrip.server.event.project.pullrequest.PullRequestChanged;
import net.devgrip.server.event.project.pullrequest.PullRequestCommentCreated;
import net.devgrip.server.event.project.pullrequest.PullRequestCommentEdited;
import net.devgrip.server.event.project.pullrequest.PullRequestOpened;
import net.devgrip.server.markdown.MarkdownManager;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueChange;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestChange;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.issue.changedata.IssueReferencedFromCodeCommentData;
import net.devgrip.server.model.support.issue.changedata.IssueReferencedFromIssueData;
import net.devgrip.server.model.support.issue.changedata.IssueReferencedFromPullRequestData;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestReferencedFromCodeCommentData;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestReferencedFromIssueData;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestReferencedFromPullRequestData;
import net.devgrip.server.persistence.annotation.Transactional;

@Singleton
public class DefaultReferenceChangeManager implements ReferenceChangeManager {
	
	private final IssueManager issueManager;
	
	private final PullRequestManager pullRequestManager;
	
	private final IssueChangeManager issueChangeManager;
	
	private final PullRequestChangeManager pullRequestChangeManager;
	
	private final MarkdownManager markdownManager;
	
	@Inject
	public DefaultReferenceChangeManager(IssueManager issueManager, 
										 PullRequestManager pullRequestManager, IssueChangeManager issueChangeManager,
										 PullRequestChangeManager pullRequestChangeManager, MarkdownManager markdownManager) {
		this.issueManager = issueManager;
		this.pullRequestManager = pullRequestManager;
		this.issueChangeManager = issueChangeManager;
		this.pullRequestChangeManager = pullRequestChangeManager;
		this.markdownManager = markdownManager;
	}
	
	@Override
	public void addReferenceChange(User user, Issue issue, String markdown) {
		if (markdown != null) {
			Document document = Jsoup.parseBodyFragment(markdownManager.render(markdown));			
			for (var reference: extractReferences(document, issue.getProject())) {
				if (reference instanceof IssueReference) {
					var referencedIssue = issueManager.find(reference.getProject(), reference.getNumber());
					if (referencedIssue != null && !referencedIssue.equals(issue)) {
						boolean found = false;
						for (var change : referencedIssue.getChanges()) {
							if (change.getData() instanceof IssueReferencedFromIssueData) {
								var referencedFromIssueData = (IssueReferencedFromIssueData) change.getData();
								if (referencedFromIssueData.getIssueId().equals(issue.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromIssueData = new IssueReferencedFromIssueData(issue);
							var change = new IssueChange();
							change.setData(referencedFromIssueData);
							change.setDate(new Date());
							change.setUser(user);
							change.setIssue(referencedIssue);
							referencedIssue.getChanges().add(change);
							issueChangeManager.create(change, null);
						}
					}
				} else if (reference instanceof PullRequestReference) {
					var referencedPullRequest  = pullRequestManager.find(reference.getProject(), reference.getNumber());
					if (referencedPullRequest != null) {
						boolean found = false;
						for (var change: referencedPullRequest.getChanges()) {
							if (change.getData() instanceof PullRequestReferencedFromIssueData) {
								var referencedFromIssueData = (PullRequestReferencedFromIssueData) change.getData();
								if (referencedFromIssueData.getIssueId().equals(issue.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromIssueData = new PullRequestReferencedFromIssueData(issue);
							var change = new PullRequestChange();
							change.setData(referencedFromIssueData);
							change.setDate(new Date());
							change.setUser(user);
							change.setRequest(referencedPullRequest);
							referencedPullRequest.getChanges().add(change);
							pullRequestChangeManager.create(change, null);
						}
					}
				}
			}
		}
	}

	
	@Override 
	public void addReferenceChange(User user, PullRequest request, String markdown) {
		if (markdown != null) {
			Document document = Jsoup.parseBodyFragment(markdownManager.render(markdown));			
			for (var reference: extractReferences(document, request.getTargetProject())) {
				if (reference instanceof IssueReference) {
					var referencedIssue = issueManager.find(reference.getProject(), reference.getNumber());
					if (referencedIssue != null) {
						boolean found = false;
						for (var change : referencedIssue.getChanges()) {
							if (change.getData() instanceof IssueReferencedFromPullRequestData) {
								var referencedFromPullRequestData = (IssueReferencedFromPullRequestData) change.getData();
								if (referencedFromPullRequestData.getRequestId().equals(request.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromPullRequestData = new IssueReferencedFromPullRequestData(request);
							var change = new IssueChange();
							change.setData(referencedFromPullRequestData);
							change.setDate(new Date());
							change.setUser(user);
							change.setIssue(referencedIssue);
							referencedIssue.getChanges().add(change);
							issueChangeManager.create(change, null);
						}
					}
				} else if (reference instanceof PullRequestReference) {
					var referencedPullRequest = pullRequestManager.find(reference.getProject(), reference.getNumber());
					if (referencedPullRequest != null && !referencedPullRequest.equals(request)) {
						boolean found = false;
						for (var change: referencedPullRequest.getChanges()) {
							if (change.getData() instanceof PullRequestReferencedFromPullRequestData) {
								var referencedFromPullRequestData = (PullRequestReferencedFromPullRequestData) change.getData();
								if (referencedFromPullRequestData.getRequestId().equals(request.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromPullRequestData = new PullRequestReferencedFromPullRequestData(request);
							var change = new PullRequestChange();
							change.setData(referencedFromPullRequestData);
							change.setDate(new Date());
							change.setUser(user);
							change.setRequest(referencedPullRequest);
							referencedPullRequest.getChanges().add(change);
							pullRequestChangeManager.create(change, null);
						}
					}
				}
			}
		}
	}
	
	@Override
	public void addReferenceChange(User user, CodeComment comment, String markdown) {
		if (markdown != null) {
			Document document = Jsoup.parseBodyFragment(markdownManager.render(markdown));			
			for (var reference: extractReferences(document, comment.getProject())) {
				if (reference instanceof IssueReference) {
					var referencedIssue = issueManager.find(reference.getProject(), reference.getNumber());
					if (referencedIssue != null) {
						boolean found = false;
						for (var change : referencedIssue.getChanges()) {
							if (change.getData() instanceof IssueReferencedFromCodeCommentData) {
								var referencedFromCodeCommentData = (IssueReferencedFromCodeCommentData) change.getData();
								if (referencedFromCodeCommentData.getCommentId().equals(comment.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromCodeCommentData = new IssueReferencedFromCodeCommentData(comment);
							var change = new IssueChange();
							change.setData(referencedFromCodeCommentData);
							change.setDate(new Date());
							change.setUser(user);
							change.setIssue(referencedIssue);
							referencedIssue.getChanges().add(change);
							issueChangeManager.create(change, null);
						}
					}					
				} else if (reference instanceof PullRequestReference) {
					var referencedPullRequest = pullRequestManager.find(reference.getProject(), reference.getNumber());
					if (referencedPullRequest != null) {
						boolean found = false;
						for (var change: referencedPullRequest.getChanges()) {
							if (change.getData() instanceof PullRequestReferencedFromCodeCommentData) {
								var referencedFromCodeCommentData = (PullRequestReferencedFromCodeCommentData) change.getData();
								if (referencedFromCodeCommentData.getCommentId().equals(comment.getId())) {
									found = true;
									break;
								}
							}
						}
						if (!found) {
							var referencedFromCodeCommentData = new PullRequestReferencedFromCodeCommentData(comment);
							var change = new PullRequestChange();
							change.setData(referencedFromCodeCommentData);
							change.setDate(new Date());
							change.setUser(user);
							change.setRequest(referencedPullRequest);
							referencedPullRequest.getChanges().add(change);
							pullRequestChangeManager.create(change, null);
						}
					}
				}
			}
		}
	}
	
	@Transactional
	@Listen
	public void on(IssueCommentCreated event) {
		addReferenceChange(event.getUser(), event.getIssue(), event.getComment().getContent());
	}

	@Transactional
	@Listen
	public void on(IssueCommentEdited event) {
		addReferenceChange(event.getUser(), event.getIssue(), event.getComment().getContent());
	}
	
	@Transactional
	@Listen
	public void on(IssueChanged event) {
		addReferenceChange(event.getUser(), event.getIssue(), event.getComment());
	}
	
	@Transactional
	@Listen
	public void on(PullRequestCommentCreated event) {
		addReferenceChange(event.getUser(), event.getRequest(), event.getComment().getContent());
	}

	@Transactional
	@Listen
	public void on(PullRequestCommentEdited event) {
		addReferenceChange(event.getUser(), event.getRequest(), event.getComment().getContent());
	}
	
	@Transactional
	@Listen
	public void on(PullRequestChanged event) {
		addReferenceChange(event.getUser(), event.getRequest(), event.getComment());
	}
	
	@Transactional
	@Listen
	public void on(CodeCommentReplyCreated event) {
		addReferenceChange(event.getUser(), event.getComment(), event.getReply().getContent());
	}

	@Transactional
	@Listen
	public void on(CodeCommentReplyEdited event) {
		addReferenceChange(event.getUser(), event.getComment(), event.getReply().getContent());
	}
	
	@Transactional
	@Listen
	public void on(CodeCommentStatusChanged event) {
		addReferenceChange(event.getUser(), event.getComment(), event.getNote());
	}
	
	@Transactional
	@Listen
	public void on(PullRequestOpened event) {
		addReferenceChange(event.getUser(), event.getRequest(), event.getRequest().getTitle());
		addReferenceChange(event.getUser(), event.getRequest(), event.getRequest().getDescription());
	}
	
	@Transactional
	@Listen
	public void on(IssueOpened event) {
		addReferenceChange(event.getUser(), event.getIssue(), event.getIssue().getTitle());
		addReferenceChange(event.getUser(), event.getIssue(), event.getIssue().getDescription());
	}
	
	@Transactional
	@Listen
	public void on(CodeCommentCreated event) {
		addReferenceChange(event.getUser(), event.getComment(), event.getComment().getContent());
	}
	
	@Transactional
	@Listen
	public void on(CodeCommentEdited event) {
		addReferenceChange(event.getUser(), event.getComment(), event.getComment().getContent());
	}
	
}

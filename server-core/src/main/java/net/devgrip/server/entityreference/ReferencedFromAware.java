package net.devgrip.server.entityreference;

import javax.annotation.Nullable;

import net.devgrip.server.model.AbstractEntity;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.security.SecurityUtils;

public interface ReferencedFromAware<T extends AbstractEntity> {

	@Nullable
	T getReferencedFrom();
	
	public static boolean canDisplay(ReferencedFromAware<?> referencedFromAware) {
		AbstractEntity referencedFrom = referencedFromAware.getReferencedFrom();
		if (referencedFrom instanceof Issue) 
			return SecurityUtils.canAccessIssue((Issue) referencedFrom);
		else if (referencedFrom instanceof PullRequest) 
			return SecurityUtils.canReadCode(((PullRequest) referencedFrom).getProject());
		else if (referencedFrom instanceof CodeComment) 
			return SecurityUtils.canReadCode(((CodeComment) referencedFrom).getProject());
		else 
			return referencedFrom != null; 
	}
}

package net.devgrip.server.entityreference;

import net.devgrip.server.model.Project;

import javax.annotation.Nullable;

public class PullRequestReference extends EntityReference {

	private static final long serialVersionUID = 1L;
	
	public static final String TYPE = "pull request";
	public static final String TYPE_I18N_KEY = "Reference.type.pr";
	
	public PullRequestReference(Project project, Long number) {
		super(project, number);
	}

	public PullRequestReference(Long projectId, Long number) {
		super(projectId, number);
	}

	@Override
	public String getType() {
		return TYPE;
	}

	@Override
	public String getTypeAsI18nKey() {
		return TYPE_I18N_KEY;
	}
	
	public static PullRequestReference of(String referenceString, @Nullable Project currentProject) {
		return (PullRequestReference) of(TYPE, referenceString, currentProject);
	}
	
}

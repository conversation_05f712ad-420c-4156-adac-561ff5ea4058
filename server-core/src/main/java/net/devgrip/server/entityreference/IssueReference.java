package net.devgrip.server.entityreference;

import net.devgrip.server.model.Project;

import javax.annotation.Nullable;

public class IssueReference extends EntityReference {

	private static final long serialVersionUID = 1L;
	
	public static final String TYPE = "issue";
	public static final String TYPE_I18N_KEY = "Reference.type.issue";
	
	public IssueReference(Project project, Long number) {
		super(project, number);
	}

	public IssueReference(Long projectId, Long number) {
		super(projectId, number);
	}

	@Override
	public String getType() {
		return TYPE;
	}

	@Override
	public String getTypeAsI18nKey() {
		return TYPE_I18N_KEY;
	}

	public static IssueReference of(String referenceString, @Nullable Project currentProject) {
		return (IssueReference) of(TYPE, referenceString, currentProject);
	}
	
}

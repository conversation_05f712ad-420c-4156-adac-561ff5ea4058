package net.devgrip.server.entityreference;

import javax.annotation.Nullable;

import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.User;

public interface ReferenceChangeManager {

	void addReferenceChange(User user, Issue issue, @Nullable String markdown);
	
	void addReferenceChange(User user, PullRequest request, @Nullable String markdown);
	
	void addReferenceChange(User user, CodeComment comment, @Nullable String markdown);
	
}

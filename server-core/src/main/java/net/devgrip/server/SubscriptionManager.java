package net.devgrip.server;

import net.devgrip.server.web.component.modal.ModalPanel;
import org.apache.wicket.Component;

import javax.annotation.Nullable;

public interface SubscriptionManager {
	
	boolean isSubscriptionActive();
	
	@Nullable
	String getLicensee();

	/**
	 * <p>
	 *     同时支持在WebMarkupContainer和MenuItem中使用。
	 * 	   在MenuItem中使用时，modalPanel必填;
	 * 	   在WebMarkupContainer中使用modalPanel填null即可。
	 * </p>
	 * 如果在MenuItem中使用,在重写newLink的方法中大概这样
	 * <pre>
	 *     return new ModalLink(id) {
	 *          onclick(){ do your logic}
	 *          protected Component newContent(String id, ModalPanel modal) {return renderSupportRequestLink(id, modal) }
	 *     }
	 * </pre>
	 * @param componentId 必填，the componentId
	 * @param modalPanel 选填
	 *                      
	 * @return
	 */
	Component renderSupportRequestLink(String componentId, ModalPanel modalPanel);

	
	
}

package net.devgrip.server.commandhandler;

import net.devgrip.commons.bootstrap.Bootstrap;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.commons.utils.FileUtils;
import net.devgrip.commons.utils.ZipUtils;
import net.devgrip.server.persistence.HibernateConfig;
import net.devgrip.server.data.DataManager;
import net.devgrip.server.persistence.SessionFactoryManager;
import net.devgrip.server.security.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.io.File;
import java.sql.SQLException;

import static net.devgrip.server.persistence.PersistenceUtils.callWithTransaction;

@Singleton
public class BackupDatabase extends CommandHandler {

	public static final String COMMAND = "backup-db";
	
	private static final Logger logger = LoggerFactory.getLogger(BackupDatabase.class);
	
	private final DataManager dataManager;
	
	private final SessionFactoryManager sessionFactoryManager;
		
	private File backupFile;
	
	@Inject
	public BackupDatabase(DataManager dataManager, SessionFactoryManager sessionFactoryManager,
                          HibernateConfig hibernateConfig) {
		super(hibernateConfig);
		this.dataManager = dataManager;
		this.sessionFactoryManager = sessionFactoryManager;
	}
	
	@Override
	public void start() {
		SecurityUtils.bindAsSystem();
		
		if (Bootstrap.command.getArgs().length == 0) {
			logger.error("Missing backup file parameter. Usage: {} <path to database backup file>", Bootstrap.command.getScript());
			System.exit(1);
		}
		backupFile = new File(Bootstrap.command.getArgs()[0]);
		if (!backupFile.isAbsolute() && System.getenv("WRAPPER_BIN_DIR") != null)
			backupFile = new File(System.getenv("WRAPPER_BIN_DIR"), backupFile.getPath());
		
		if (backupFile.exists()) {
			logger.error("Backup file already exists: {}", backupFile.getAbsolutePath());
			System.exit(1);
		}

		try {
			doMaintenance(() -> {
				sessionFactoryManager.start();

				try (var conn = dataManager.openConnection()) {
					callWithTransaction(conn, () -> {
						dataManager.checkDataVersion(conn, false);
						return null;
					});
				} catch (SQLException e) {
					throw new RuntimeException(e);
				}

				logger.info("Backing up database to {}...", backupFile.getAbsolutePath());

				File tempDir = FileUtils.createTempDir("backup");
				try {
					dataManager.exportData(tempDir);
					ZipUtils.zip(tempDir, backupFile, null);
				} catch (Exception e) {
					throw ExceptionUtils.unchecked(e);
				} finally {
					FileUtils.deleteDir(tempDir);
				}

				logger.info("Database is successfully backed up to {}", backupFile.getAbsolutePath());
				return null;
			});
			System.exit(0);
		} catch (ExplicitException e) {
			logger.error(e.getMessage());
			System.exit(1);
		}
	}

	@Override
	public void stop() {
		sessionFactoryManager.stop();
	}

}

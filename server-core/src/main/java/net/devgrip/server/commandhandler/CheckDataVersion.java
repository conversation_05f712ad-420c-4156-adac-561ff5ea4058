package net.devgrip.server.commandhandler;

import static net.devgrip.server.persistence.PersistenceUtils.callWithTransaction;

import java.sql.SQLException;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.data.DataManager;
import net.devgrip.server.persistence.HibernateConfig;
import net.devgrip.server.persistence.SessionFactoryManager;
import net.devgrip.server.security.SecurityUtils;

@Singleton
public class CheckDataVersion extends CommandHandler {

	public static final String COMMAND = "check-data-version";
	
	private static final Logger logger = LoggerFactory.getLogger(CheckDataVersion.class);

	private final SessionFactoryManager sessionFactoryManager;
	
	private final DataManager dataManager;
		
	@Inject
	public CheckDataVersion(SessionFactoryManager sessionFactoryManager, DataManager dataManager, 
							HibernateConfig hibernateConfig) {
		super(hibernateConfig);
		this.sessionFactoryManager = sessionFactoryManager;
		this.dataManager = dataManager;
	}

	@Override
	public void start() {
		SecurityUtils.bindAsSystem();

		try {
			doMaintenance(() -> {
				sessionFactoryManager.start();

				// Use system.out in case logger is suppressed by user as this output is important to 
				// upgrade procedure
				String dataVersion;
				try (var conn = dataManager.openConnection()) {
					dataVersion = callWithTransaction(conn, () -> dataManager.checkDataVersion(conn, false));
				} catch (SQLException e) {
					throw new RuntimeException(e);
				}
				System.out.println("Data version: " + dataVersion);
				return null;
			});
			System.exit(0);
		} catch (ExplicitException e) {
			logger.error(e.getMessage());
			System.exit(1);
		}
	}

	@Override
	public void stop() {
		sessionFactoryManager.stop();
	}

}

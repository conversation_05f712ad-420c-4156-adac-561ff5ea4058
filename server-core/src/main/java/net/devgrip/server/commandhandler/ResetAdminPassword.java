package net.devgrip.server.commandhandler;

import net.devgrip.commons.bootstrap.Bootstrap;
import net.devgrip.commons.utils.ExplicitException;
import net.devgrip.server.data.DataManager;
import net.devgrip.server.entitymanager.UserManager;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.HibernateConfig;
import net.devgrip.server.persistence.SessionFactoryManager;
import net.devgrip.server.security.SecurityUtils;
import org.apache.shiro.authc.credential.PasswordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.sql.SQLException;

import static net.devgrip.server.persistence.PersistenceUtils.callWithTransaction;

@Singleton
public class ResetAdminPassword extends CommandHandler {

	public static final String COMMAND = "reset-admin-password";
	
	private static final Logger logger = LoggerFactory.getLogger(ResetAdminPassword.class);
	
	private final DataManager dataManager;
	
	private final SessionFactoryManager sessionFactoryManager;
	
	private final UserManager userManager;
	
	private final PasswordService passwordService;
	
	@Inject
	public ResetAdminPassword(HibernateConfig hibernateConfig, DataManager dataManager, 
							  SessionFactoryManager sessionFactoryManager, UserManager userManager, 
							  PasswordService passwordService) {
		super(hibernateConfig);
		this.dataManager = dataManager;
		this.sessionFactoryManager = sessionFactoryManager;
		this.userManager = userManager;
		this.passwordService = passwordService;
	}

	@Override
	public void start() {
		SecurityUtils.bindAsSystem();
		
		if (Bootstrap.command.getArgs().length == 0) {
			logger.error("Missing password parameter. Usage: {} <new password>", Bootstrap.command.getScript());
			System.exit(1);
		}

		try {
			doMaintenance(() -> {
				sessionFactoryManager.start();

				try (var conn = dataManager.openConnection()) {
					callWithTransaction(conn, () -> {
						dataManager.checkDataVersion(conn, false);
						return null;
					});
				} catch (SQLException e) {
					throw new RuntimeException(e);
				}

				User root = userManager.get(User.ROOT_ID);
				if (root == null)
					throw new ExplicitException("Server not set up yet");
				String password = Bootstrap.command.getArgs()[0];
				root.setTwoFactorAuthentication(null);
				root.setPassword(passwordService.encryptPassword(password));
				userManager.update(root, null);
				// wait for a short period to have embedded db flushing data
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					throw new RuntimeException(e);
				}
				logger.info("Password of user '{}' has been reset", root.getName());
				return null;
			});
			System.exit(0);
		} catch (ExplicitException e) {
			logger.error(e.getMessage());
			System.exit(1);
		}
	}

	@Override
	public void stop() {
		sessionFactoryManager.stop();
	}

}

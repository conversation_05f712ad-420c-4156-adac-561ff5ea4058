package net.devgrip.server;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.matcher.AbstractMatcher;
import com.google.inject.matcher.Matchers;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.annotations.XStreamOmitField;
import com.thoughtworks.xstream.converters.basic.NullConverter;
import com.thoughtworks.xstream.converters.extended.ISO8601DateConverter;
import com.thoughtworks.xstream.converters.extended.ISO8601SqlTimestampConverter;
import com.thoughtworks.xstream.converters.reflection.ReflectionProvider;
import com.thoughtworks.xstream.core.JVM;
import com.thoughtworks.xstream.mapper.MapperWrapper;
import com.vladsch.flexmark.util.misc.Extension;
import net.devgrip.agent.ExecutorUtils;
import net.devgrip.commons.bootstrap.Bootstrap;
import net.devgrip.commons.loader.AbstractPlugin;
import net.devgrip.commons.loader.AbstractPluginModule;
import net.devgrip.commons.utils.ExceptionUtils;
import net.devgrip.commons.utils.StringUtils;
import net.devgrip.k8shelper.KubernetesHelper;
import net.devgrip.k8shelper.OsInfo;
import net.devgrip.server.attachment.AttachmentManager;
import net.devgrip.server.attachment.DefaultAttachmentManager;
import net.devgrip.server.buildspec.job.log.instruction.LogInstruction;
import net.devgrip.server.cluster.ClusterResource;
import net.devgrip.server.codequality.CodeProblemContribution;
import net.devgrip.server.codequality.LineCoverageContribution;
import net.devgrip.server.commandhandler.ApplyDatabaseConstraints;
import net.devgrip.server.commandhandler.BackupDatabase;
import net.devgrip.server.commandhandler.CheckDataVersion;
import net.devgrip.server.commandhandler.CleanDatabase;
import net.devgrip.server.commandhandler.ResetAdminPassword;
import net.devgrip.server.commandhandler.RestoreDatabase;
import net.devgrip.server.commandhandler.Upgrade;
import net.devgrip.server.data.DataManager;
import net.devgrip.server.data.DefaultDataManager;
import net.devgrip.server.duration.HumanDuration;
import net.devgrip.server.entitymanager.AccessTokenAuthorizationManager;
import net.devgrip.server.entitymanager.AccessTokenManager;
import net.devgrip.server.entitymanager.AgentAttributeManager;
import net.devgrip.server.entitymanager.AgentLastUsedDateManager;
import net.devgrip.server.entitymanager.AgentManager;
import net.devgrip.server.entitymanager.AgentTokenManager;
import net.devgrip.server.entitymanager.AlertManager;
import net.devgrip.server.entitymanager.BaseProjectRoleManager;
import net.devgrip.server.entitymanager.BuildDependenceManager;
import net.devgrip.server.entitymanager.BuildLabelManager;
import net.devgrip.server.entitymanager.BuildManager;
import net.devgrip.server.entitymanager.BuildMetricManager;
import net.devgrip.server.entitymanager.BuildParamManager;
import net.devgrip.server.entitymanager.BuildQueryPersonalizationManager;
import net.devgrip.server.entitymanager.CodeCommentManager;
import net.devgrip.server.entitymanager.CodeCommentMentionManager;
import net.devgrip.server.entitymanager.CodeCommentQueryPersonalizationManager;
import net.devgrip.server.entitymanager.CodeCommentReplyManager;
import net.devgrip.server.entitymanager.CodeCommentStatusChangeManager;
import net.devgrip.server.entitymanager.CodeCommentTouchManager;
import net.devgrip.server.entitymanager.CommitQueryPersonalizationManager;
import net.devgrip.server.entitymanager.DashboardGroupShareManager;
import net.devgrip.server.entitymanager.DashboardManager;
import net.devgrip.server.entitymanager.DashboardUserShareManager;
import net.devgrip.server.entitymanager.DashboardVisitManager;
import net.devgrip.server.entitymanager.EmailAddressManager;
import net.devgrip.server.entitymanager.GitLfsLockManager;
import net.devgrip.server.entitymanager.GpgKeyManager;
import net.devgrip.server.entitymanager.GroupAuthorizationManager;
import net.devgrip.server.entitymanager.GroupManager;
import net.devgrip.server.entitymanager.IssueAuthorizationManager;
import net.devgrip.server.entitymanager.IssueChangeManager;
import net.devgrip.server.entitymanager.IssueCommentManager;
import net.devgrip.server.entitymanager.IssueCommentReactionManager;
import net.devgrip.server.entitymanager.IssueFieldManager;
import net.devgrip.server.entitymanager.IssueLinkManager;
import net.devgrip.server.entitymanager.IssueManager;
import net.devgrip.server.entitymanager.IssueMentionManager;
import net.devgrip.server.entitymanager.IssueQueryPersonalizationManager;
import net.devgrip.server.entitymanager.IssueReactionManager;
import net.devgrip.server.entitymanager.IssueScheduleManager;
import net.devgrip.server.entitymanager.IssueStateHistoryManager;
import net.devgrip.server.entitymanager.IssueTouchManager;
import net.devgrip.server.entitymanager.IssueVoteManager;
import net.devgrip.server.entitymanager.IssueWatchManager;
import net.devgrip.server.entitymanager.IssueWorkManager;
import net.devgrip.server.entitymanager.IterationManager;
import net.devgrip.server.entitymanager.JobCacheManager;
import net.devgrip.server.entitymanager.LabelSpecManager;
import net.devgrip.server.entitymanager.LinkAuthorizationManager;
import net.devgrip.server.entitymanager.LinkSpecManager;
import net.devgrip.server.entitymanager.MembershipManager;
import net.devgrip.server.entitymanager.PackBlobManager;
import net.devgrip.server.entitymanager.PackBlobReferenceManager;
import net.devgrip.server.entitymanager.PackLabelManager;
import net.devgrip.server.entitymanager.PackManager;
import net.devgrip.server.entitymanager.PackQueryPersonalizationManager;
import net.devgrip.server.entitymanager.PendingSuggestionApplyManager;
import net.devgrip.server.entitymanager.ProjectLabelManager;
import net.devgrip.server.entitymanager.ProjectLastEventDateManager;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.PullRequestAssignmentManager;
import net.devgrip.server.entitymanager.PullRequestChangeManager;
import net.devgrip.server.entitymanager.PullRequestCommentManager;
import net.devgrip.server.entitymanager.PullRequestCommentReactionManager;
import net.devgrip.server.entitymanager.PullRequestLabelManager;
import net.devgrip.server.entitymanager.PullRequestManager;
import net.devgrip.server.entitymanager.PullRequestMentionManager;
import net.devgrip.server.entitymanager.PullRequestQueryPersonalizationManager;
import net.devgrip.server.entitymanager.PullRequestReactionManager;
import net.devgrip.server.entitymanager.PullRequestReviewManager;
import net.devgrip.server.entitymanager.PullRequestTouchManager;
import net.devgrip.server.entitymanager.PullRequestUpdateManager;
import net.devgrip.server.entitymanager.PullRequestWatchManager;
import net.devgrip.server.entitymanager.ReviewedDiffManager;
import net.devgrip.server.entitymanager.RoleManager;
import net.devgrip.server.entitymanager.SettingManager;
import net.devgrip.server.entitymanager.SshKeyManager;
import net.devgrip.server.entitymanager.StopwatchManager;
import net.devgrip.server.entitymanager.UserAuthorizationManager;
import net.devgrip.server.entitymanager.UserInvitationManager;
import net.devgrip.server.entitymanager.UserManager;
import net.devgrip.server.entitymanager.impl.DefaultAccessTokenAuthorizationManager;
import net.devgrip.server.entitymanager.impl.DefaultAccessTokenManager;
import net.devgrip.server.entitymanager.impl.DefaultAgentAttributeManager;
import net.devgrip.server.entitymanager.impl.DefaultAgentLastUsedDateManager;
import net.devgrip.server.entitymanager.impl.DefaultAgentManager;
import net.devgrip.server.entitymanager.impl.DefaultAgentTokenManager;
import net.devgrip.server.entitymanager.impl.DefaultAlertManager;
import net.devgrip.server.entitymanager.impl.DefaultBaseProjectRoleManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildDependenceManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildLabelManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildMetricManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildParamManager;
import net.devgrip.server.entitymanager.impl.DefaultBuildQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentMentionManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentReplyManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentStatusChangeManager;
import net.devgrip.server.entitymanager.impl.DefaultCodeCommentTouchManager;
import net.devgrip.server.entitymanager.impl.DefaultCommitQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultDashboardGroupShareManager;
import net.devgrip.server.entitymanager.impl.DefaultDashboardManager;
import net.devgrip.server.entitymanager.impl.DefaultDashboardUserShareManager;
import net.devgrip.server.entitymanager.impl.DefaultDashboardVisitManager;
import net.devgrip.server.entitymanager.impl.DefaultEmailAddressManager;
import net.devgrip.server.entitymanager.impl.DefaultGitLfsLockManager;
import net.devgrip.server.entitymanager.impl.DefaultGpgKeyManager;
import net.devgrip.server.entitymanager.impl.DefaultGroupAuthorizationManager;
import net.devgrip.server.entitymanager.impl.DefaultGroupManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueAuthorizationManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueChangeManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueCommentManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueCommentReactionManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueFieldManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueLinkManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueMentionManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueReactionManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueScheduleManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueStateHistoryManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueTouchManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueVoteManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueWatchManager;
import net.devgrip.server.entitymanager.impl.DefaultIssueWorkManager;
import net.devgrip.server.entitymanager.impl.DefaultIterationManager;
import net.devgrip.server.entitymanager.impl.DefaultJobCacheManager;
import net.devgrip.server.entitymanager.impl.DefaultLabelSpecManager;
import net.devgrip.server.entitymanager.impl.DefaultLinkAuthorizationManager;
import net.devgrip.server.entitymanager.impl.DefaultLinkSpecManager;
import net.devgrip.server.entitymanager.impl.DefaultMembershipManager;
import net.devgrip.server.entitymanager.impl.DefaultPackBlobManager;
import net.devgrip.server.entitymanager.impl.DefaultPackBlobReferenceManager;
import net.devgrip.server.entitymanager.impl.DefaultPackLabelManager;
import net.devgrip.server.entitymanager.impl.DefaultPackManager;
import net.devgrip.server.entitymanager.impl.DefaultPackQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultPendingSuggestionApplyManager;
import net.devgrip.server.entitymanager.impl.DefaultProjectLabelManager;
import net.devgrip.server.entitymanager.impl.DefaultProjectLastEventDateManager;
import net.devgrip.server.entitymanager.impl.DefaultProjectManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestAssignmentManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestChangeManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestCommentManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestCommentReactionManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestLabelManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestMentionManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestQueryPersonalizationManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestReactionManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestReviewManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestTouchManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestUpdateManager;
import net.devgrip.server.entitymanager.impl.DefaultPullRequestWatchManager;
import net.devgrip.server.entitymanager.impl.DefaultReviewedDiffManager;
import net.devgrip.server.entitymanager.impl.DefaultRoleManager;
import net.devgrip.server.entitymanager.impl.DefaultSettingManager;
import net.devgrip.server.entitymanager.impl.DefaultSshKeyManager;
import net.devgrip.server.entitymanager.impl.DefaultStopwatchManager;
import net.devgrip.server.entitymanager.impl.DefaultUserAuthorizationManager;
import net.devgrip.server.entitymanager.impl.DefaultUserInvitationManager;
import net.devgrip.server.entitymanager.impl.DefaultUserManager;
import net.devgrip.server.entityreference.DefaultReferenceChangeManager;
import net.devgrip.server.entityreference.ReferenceChangeManager;
import net.devgrip.server.event.DefaultEventPublisher;
import net.devgrip.server.event.DefaultListenerRegistry;
import net.devgrip.server.event.EventPublisher;
import net.devgrip.server.event.ListenerRegistry;
import net.devgrip.server.exception.handler.ExceptionHandler;
import net.devgrip.server.git.GitFilter;
import net.devgrip.server.git.GitLfsFilter;
import net.devgrip.server.git.GitLocationProvider;
import net.devgrip.server.git.GoGetFilter;
import net.devgrip.server.git.SshCommandCreator;
import net.devgrip.server.git.hook.GitPostReceiveCallback;
import net.devgrip.server.git.hook.GitPreReceiveCallback;
import net.devgrip.server.git.hook.GitPreReceiveChecker;
import net.devgrip.server.git.location.GitLocation;
import net.devgrip.server.git.service.DefaultGitService;
import net.devgrip.server.git.service.GitService;
import net.devgrip.server.git.signatureverification.DefaultSignatureVerificationManager;
import net.devgrip.server.git.signatureverification.SignatureVerificationManager;
import net.devgrip.server.git.signatureverification.SignatureVerifier;
import net.devgrip.server.i18n.DefaultI18nManager;
import net.devgrip.server.i18n.DefaultLocaleChangedPublisher;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.i18n.LocaleChangedPublisher;
import net.devgrip.server.i18n.LocaleContext;
import net.devgrip.server.i18n.MyLocaleResolver;
import net.devgrip.server.jetty.DefaultJettyLauncher;
import net.devgrip.server.jetty.DefaultSessionDataStoreFactory;
import net.devgrip.server.jetty.JettyLauncher;
import net.devgrip.server.job.DefaultJobManager;
import net.devgrip.server.job.DefaultResourceAllocator;
import net.devgrip.server.job.JobManager;
import net.devgrip.server.job.ResourceAllocator;
import net.devgrip.server.job.log.DefaultLogManager;
import net.devgrip.server.job.log.LogManager;
import net.devgrip.server.mail.DefaultMailManager;
import net.devgrip.server.mail.MailManager;
import net.devgrip.server.markdown.DefaultMarkdownManager;
import net.devgrip.server.markdown.HtmlProcessor;
import net.devgrip.server.markdown.MarkdownManager;
import net.devgrip.server.model.support.administration.GroovyScript;
import net.devgrip.server.model.support.administration.authenticator.Authenticator;
import net.devgrip.server.notification.BuildNotificationManager;
import net.devgrip.server.notification.CodeCommentNotificationManager;
import net.devgrip.server.notification.CommitNotificationManager;
import net.devgrip.server.notification.EventProcessor;
import net.devgrip.server.notification.IssueNotificationManager;
import net.devgrip.server.notification.PackNotificationManager;
import net.devgrip.server.notification.PullRequestNotificationManager;
import net.devgrip.server.notification.WebHookManager;
import net.devgrip.server.pack.PackFilter;
import net.devgrip.server.persistence.DefaultIdManager;
import net.devgrip.server.persistence.DefaultSessionFactoryManager;
import net.devgrip.server.persistence.DefaultSessionManager;
import net.devgrip.server.persistence.DefaultTransactionManager;
import net.devgrip.server.persistence.HibernateInterceptor;
import net.devgrip.server.persistence.IdManager;
import net.devgrip.server.persistence.PersistListener;
import net.devgrip.server.persistence.PrefixedNamingStrategy;
import net.devgrip.server.persistence.SessionFactoryManager;
import net.devgrip.server.persistence.SessionFactoryProvider;
import net.devgrip.server.persistence.SessionInterceptor;
import net.devgrip.server.persistence.SessionManager;
import net.devgrip.server.persistence.SessionProvider;
import net.devgrip.server.persistence.TransactionInterceptor;
import net.devgrip.server.persistence.TransactionManager;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.persistence.dao.DefaultDao;
import net.devgrip.server.persistence.exception.ConstraintViolationExceptionHandler;
import net.devgrip.server.rest.DefaultServletContainer;
import net.devgrip.server.rest.JerseyConfigurator;
import net.devgrip.server.rest.ResourceConfigProvider;
import net.devgrip.server.rest.WebApplicationExceptionHandler;
import net.devgrip.server.rest.resource.ProjectResource;
import net.devgrip.server.search.code.CodeIndexManager;
import net.devgrip.server.search.code.CodeSearchManager;
import net.devgrip.server.search.code.DefaultCodeIndexManager;
import net.devgrip.server.search.code.DefaultCodeSearchManager;
import net.devgrip.server.search.entitytext.CodeCommentTextManager;
import net.devgrip.server.search.entitytext.DefaultCodeCommentTextManager;
import net.devgrip.server.search.entitytext.DefaultIssueTextManager;
import net.devgrip.server.search.entitytext.DefaultPullRequestTextManager;
import net.devgrip.server.search.entitytext.IssueTextManager;
import net.devgrip.server.search.entitytext.PullRequestTextManager;
import net.devgrip.server.security.BasicAuthenticationFilter;
import net.devgrip.server.security.BearerAuthenticationFilter;
import net.devgrip.server.security.CodePullAuthorizationSource;
import net.devgrip.server.security.DefaultFilterChainResolver;
import net.devgrip.server.security.DefaultPasswordService;
import net.devgrip.server.security.DefaultRememberMeManager;
import net.devgrip.server.security.DefaultShiroFilterConfiguration;
import net.devgrip.server.security.DefaultWebSecurityManager;
import net.devgrip.server.security.FilterChainConfigurator;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.security.realm.GeneralAuthorizingRealm;
import net.devgrip.server.ssh.CommandCreator;
import net.devgrip.server.ssh.DefaultSshAuthenticator;
import net.devgrip.server.ssh.DefaultSshManager;
import net.devgrip.server.ssh.SshAuthenticator;
import net.devgrip.server.ssh.SshManager;
import net.devgrip.server.taskschedule.DefaultTaskScheduler;
import net.devgrip.server.taskschedule.TaskScheduler;
import net.devgrip.server.updatecheck.DefaultUpdateCheckManager;
import net.devgrip.server.updatecheck.UpdateCheckManager;
import net.devgrip.server.util.ScriptContribution;
import net.devgrip.server.util.concurrent.BatchWorkManager;
import net.devgrip.server.util.concurrent.DefaultBatchWorkManager;
import net.devgrip.server.util.concurrent.DefaultWorkExecutor;
import net.devgrip.server.util.concurrent.WorkExecutor;
import net.devgrip.server.util.jackson.ObjectMapperConfigurator;
import net.devgrip.server.util.jackson.ObjectMapperProvider;
import net.devgrip.server.util.jackson.git.GitObjectMapperConfigurator;
import net.devgrip.server.util.jackson.hibernate.HibernateObjectMapperConfigurator;
import net.devgrip.server.util.oauth.DefaultOAuthTokenManager;
import net.devgrip.server.util.oauth.OAuthTokenManager;
import net.devgrip.server.util.xstream.CollectionConverter;
import net.devgrip.server.util.xstream.HibernateProxyConverter;
import net.devgrip.server.util.xstream.MapConverter;
import net.devgrip.server.util.xstream.ReflectionConverter;
import net.devgrip.server.util.xstream.StringConverter;
import net.devgrip.server.util.xstream.VersionedDocumentConverter;
import net.devgrip.server.validation.ValidatorProvider;
import net.devgrip.server.web.DefaultUrlManager;
import net.devgrip.server.web.DefaultWicketFilter;
import net.devgrip.server.web.DefaultWicketServlet;
import net.devgrip.server.web.ResourcePackScopeContribution;
import net.devgrip.server.web.UrlManager;
import net.devgrip.server.web.WebApplication;
import net.devgrip.server.web.avatar.AvatarManager;
import net.devgrip.server.web.avatar.DefaultAvatarManager;
import net.devgrip.server.web.component.diff.DiffRenderer;
import net.devgrip.server.web.component.markdown.SourcePositionTrackExtension;
import net.devgrip.server.web.component.markdown.emoji.EmojiExtension;
import net.devgrip.server.web.component.taskbutton.TaskButton;
import net.devgrip.server.web.editable.DefaultEditSupportRegistry;
import net.devgrip.server.web.editable.EditSupport;
import net.devgrip.server.web.editable.EditSupportLocator;
import net.devgrip.server.web.editable.EditSupportRegistry;
import net.devgrip.server.web.exceptionhandler.PageExpiredExceptionHandler;
import net.devgrip.server.web.page.layout.AdministrationMenuContribution;
import net.devgrip.server.web.page.layout.AdministrationSettingContribution;
import net.devgrip.server.web.page.project.blob.render.BlobRenderer;
import net.devgrip.server.web.page.project.setting.ProjectSettingContribution;
import net.devgrip.server.web.upload.DefaultUploadManager;
import net.devgrip.server.web.upload.UploadManager;
import net.devgrip.server.web.websocket.AlertEventBroadcaster;
import net.devgrip.server.web.websocket.BuildEventBroadcaster;
import net.devgrip.server.web.websocket.CodeCommentEventBroadcaster;
import net.devgrip.server.web.websocket.CommitIndexedBroadcaster;
import net.devgrip.server.web.websocket.DefaultWebSocketManager;
import net.devgrip.server.web.websocket.IssueEventBroadcaster;
import net.devgrip.server.web.websocket.PullRequestEventBroadcaster;
import net.devgrip.server.web.websocket.WebSocketManager;
import net.devgrip.server.xodus.CommitInfoManager;
import net.devgrip.server.xodus.DefaultCommitInfoManager;
import net.devgrip.server.xodus.DefaultIssueInfoManager;
import net.devgrip.server.xodus.DefaultPullRequestInfoManager;
import net.devgrip.server.xodus.DefaultVisitInfoManager;
import net.devgrip.server.xodus.IssueInfoManager;
import net.devgrip.server.xodus.PullRequestInfoManager;
import net.devgrip.server.xodus.VisitInfoManager;
import nl.altindag.ssl.SSLFactory;
import org.apache.shiro.authc.credential.PasswordService;
import org.apache.shiro.guice.aop.ShiroAopModule;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.web.config.ShiroFilterConfiguration;
import org.apache.shiro.web.filter.mgt.FilterChainResolver;
import org.apache.shiro.web.mgt.WebSecurityManager;
import org.apache.shiro.web.servlet.ShiroFilter;
import org.apache.wicket.Application;
import org.apache.wicket.protocol.http.WicketFilter;
import org.apache.wicket.protocol.http.WicketServlet;
import org.eclipse.jetty.server.session.SessionDataStoreFactory;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.glassfish.jersey.server.ResourceConfig;
import org.glassfish.jersey.servlet.ServletContainer;
import org.hibernate.CallbackException;
import org.hibernate.Interceptor;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.boot.model.naming.PhysicalNamingStrategy;
import org.hibernate.collection.internal.PersistentBag;
import org.hibernate.type.Type;
import org.hibernate.validator.messageinterpolation.ParameterMessageInterpolator;

import javax.inject.Singleton;
import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import javax.persistence.Version;
import javax.validation.Configuration;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.io.Serializable;
import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.google.common.collect.Lists.newArrayList;


/**
 * NOTE: Do not forget to rename moduleClass property defined in the pom if you've renamed this class.
 *
 */
public class CoreModule extends AbstractPluginModule {

	@Override
	protected void configure() {
		super.configure();
		
		bind(ListenerRegistry.class).to(DefaultListenerRegistry.class);
		bind(JettyLauncher.class).to(DefaultJettyLauncher.class);
		bind(ServletContextHandler.class).toProvider(DefaultJettyLauncher.class);
		
		bind(ObjectMapper.class).toProvider(ObjectMapperProvider.class).in(Singleton.class);
		
		bind(ValidatorFactory.class).toProvider(() -> {
			Configuration<?> configuration = Validation
					.byDefaultProvider()
					.configure()
					.messageInterpolator(new ParameterMessageInterpolator(new HashSet<>(LocaleContext.SUPPORTED_LOCALES), LocaleContext.DEFAULT_LOCALE, new MyLocaleResolver(), false));
			return configuration.buildValidatorFactory();
		}).in(Singleton.class);
		
		bind(Validator.class).toProvider(ValidatorProvider.class).in(Singleton.class);

		configurePersistence();
		configureSecurity();
		configureRestful();
		configureWeb();
		configureGit();
		configureBuild();

		/*
		 * Declare bindings explicitly instead of using ImplementedBy annotation as
		 * HK2 to guice bridge can only search in explicit bindings in Guice   
		 */
		bind(SshAuthenticator.class).to(DefaultSshAuthenticator.class);
		bind(SshManager.class).to(DefaultSshManager.class);
		bind(MarkdownManager.class).to(DefaultMarkdownManager.class);		
		bind(SettingManager.class).to(DefaultSettingManager.class);
		bind(DataManager.class).to(DefaultDataManager.class);
		bind(TaskScheduler.class).to(DefaultTaskScheduler.class);
		bind(PullRequestCommentManager.class).to(DefaultPullRequestCommentManager.class);
		bind(CodeCommentManager.class).to(DefaultCodeCommentManager.class);
		bind(PullRequestManager.class).to(DefaultPullRequestManager.class);
		bind(PullRequestUpdateManager.class).to(DefaultPullRequestUpdateManager.class);
		bind(ProjectManager.class).to(DefaultProjectManager.class);
		bind(ProjectLastEventDateManager.class).to(DefaultProjectLastEventDateManager.class);
		bind(UserInvitationManager.class).to(DefaultUserInvitationManager.class);
		bind(PullRequestReviewManager.class).to(DefaultPullRequestReviewManager.class);
		bind(BuildManager.class).to(DefaultBuildManager.class);
		bind(BuildDependenceManager.class).to(DefaultBuildDependenceManager.class);
		bind(JobManager.class).to(DefaultJobManager.class);		
		bind(JobCacheManager.class).to(DefaultJobCacheManager.class);
		bind(LogManager.class).to(DefaultLogManager.class);
		bind(MailManager.class).to(DefaultMailManager.class);
		bind(IssueManager.class).to(DefaultIssueManager.class);
		bind(IssueFieldManager.class).to(DefaultIssueFieldManager.class);
		bind(BuildParamManager.class).to(DefaultBuildParamManager.class);
		bind(UserAuthorizationManager.class).to(DefaultUserAuthorizationManager.class);
		bind(GroupAuthorizationManager.class).to(DefaultGroupAuthorizationManager.class);
		bind(PullRequestWatchManager.class).to(DefaultPullRequestWatchManager.class);
		bind(RoleManager.class).to(DefaultRoleManager.class);
		bind(CommitInfoManager.class).to(DefaultCommitInfoManager.class);
		bind(IssueInfoManager.class).to(DefaultIssueInfoManager.class);
		bind(VisitInfoManager.class).to(DefaultVisitInfoManager.class);
		bind(BatchWorkManager.class).to(DefaultBatchWorkManager.class);
		bind(WorkExecutor.class).to(DefaultWorkExecutor.class);
		bind(GroupManager.class).to(DefaultGroupManager.class);
		bind(IssueMentionManager.class).to(DefaultIssueMentionManager.class);
		bind(PullRequestMentionManager.class).to(DefaultPullRequestMentionManager.class);
		bind(CodeCommentMentionManager.class).to(DefaultCodeCommentMentionManager.class);
		bind(MembershipManager.class).to(DefaultMembershipManager.class);
		bind(PullRequestChangeManager.class).to(DefaultPullRequestChangeManager.class);
		bind(CodeCommentReplyManager.class).to(DefaultCodeCommentReplyManager.class);
		bind(CodeCommentStatusChangeManager.class).to(DefaultCodeCommentStatusChangeManager.class);
		bind(AttachmentManager.class).to(DefaultAttachmentManager.class);
		bind(PullRequestInfoManager.class).to(DefaultPullRequestInfoManager.class);
		bind(PullRequestNotificationManager.class);
		bind(CommitNotificationManager.class);
		bind(BuildNotificationManager.class);
		bind(PackNotificationManager.class);
		bind(IssueNotificationManager.class);
		bind(EventProcessor.class);
		bind(CodeCommentNotificationManager.class);
		bind(CodeCommentManager.class).to(DefaultCodeCommentManager.class);
		bind(AccessTokenManager.class).to(DefaultAccessTokenManager.class);
		bind(UserManager.class).to(DefaultUserManager.class);
		bind(IssueWatchManager.class).to(DefaultIssueWatchManager.class);
		bind(IssueChangeManager.class).to(DefaultIssueChangeManager.class);
		bind(IssueVoteManager.class).to(DefaultIssueVoteManager.class);
		bind(IssueWorkManager.class).to(DefaultIssueWorkManager.class);
		bind(IterationManager.class).to(DefaultIterationManager.class);
		bind(IssueCommentManager.class).to(DefaultIssueCommentManager.class);
		bind(IssueQueryPersonalizationManager.class).to(DefaultIssueQueryPersonalizationManager.class);
		bind(PullRequestQueryPersonalizationManager.class).to(DefaultPullRequestQueryPersonalizationManager.class);
		bind(CodeCommentQueryPersonalizationManager.class).to(DefaultCodeCommentQueryPersonalizationManager.class);
		bind(CommitQueryPersonalizationManager.class).to(DefaultCommitQueryPersonalizationManager.class);
		bind(BuildQueryPersonalizationManager.class).to(DefaultBuildQueryPersonalizationManager.class);
		bind(PackQueryPersonalizationManager.class).to(DefaultPackQueryPersonalizationManager.class);
		bind(PullRequestAssignmentManager.class).to(DefaultPullRequestAssignmentManager.class);
		bind(SshKeyManager.class).to(DefaultSshKeyManager.class);
		bind(BuildMetricManager.class).to(DefaultBuildMetricManager.class);
		bind(ReferenceChangeManager.class).to(DefaultReferenceChangeManager.class);
		bind(GitLfsLockManager.class).to(DefaultGitLfsLockManager.class);
		bind(IssueScheduleManager.class).to(DefaultIssueScheduleManager.class);
		bind(LinkSpecManager.class).to(DefaultLinkSpecManager.class);
		bind(IssueLinkManager.class).to(DefaultIssueLinkManager.class);
		bind(IssueStateHistoryManager.class).to(DefaultIssueStateHistoryManager.class);
		bind(LinkAuthorizationManager.class).to(DefaultLinkAuthorizationManager.class);
		bind(EmailAddressManager.class).to(DefaultEmailAddressManager.class);
		bind(GpgKeyManager.class).to(DefaultGpgKeyManager.class);
		bind(IssueTextManager.class).to(DefaultIssueTextManager.class);
		bind(PullRequestTextManager.class).to(DefaultPullRequestTextManager.class);
		bind(CodeCommentTextManager.class).to(DefaultCodeCommentTextManager.class);
		bind(PendingSuggestionApplyManager.class).to(DefaultPendingSuggestionApplyManager.class);
		bind(IssueAuthorizationManager.class).to(DefaultIssueAuthorizationManager.class);
		bind(DashboardManager.class).to(DefaultDashboardManager.class);
		bind(DashboardUserShareManager.class).to(DefaultDashboardUserShareManager.class);
		bind(DashboardGroupShareManager.class).to(DefaultDashboardGroupShareManager.class);
		bind(DashboardVisitManager.class).to(DefaultDashboardVisitManager.class);
		bind(LabelSpecManager.class).to(DefaultLabelSpecManager.class);
		bind(ProjectLabelManager.class).to(DefaultProjectLabelManager.class);
		bind(BuildLabelManager.class).to(DefaultBuildLabelManager.class);
		bind(PackLabelManager.class).to(DefaultPackLabelManager.class);
		bind(PullRequestLabelManager.class).to(DefaultPullRequestLabelManager.class);
		bind(IssueTouchManager.class).to(DefaultIssueTouchManager.class);
		bind(PullRequestTouchManager.class).to(DefaultPullRequestTouchManager.class);
		bind(CodeCommentTouchManager.class).to(DefaultCodeCommentTouchManager.class);
		bind(AlertManager.class).to(DefaultAlertManager.class);
		bind(UpdateCheckManager.class).to(DefaultUpdateCheckManager.class);
		bind(StopwatchManager.class).to(DefaultStopwatchManager.class);
		bind(PackManager.class).to(DefaultPackManager.class);
		bind(PackBlobManager.class).to(DefaultPackBlobManager.class);
		bind(PackBlobReferenceManager.class).to(DefaultPackBlobReferenceManager.class);
		bind(AccessTokenAuthorizationManager.class).to(DefaultAccessTokenAuthorizationManager.class);
		bind(I18nManager.class).to(DefaultI18nManager.class);
		bind(LocaleChangedPublisher.class).to(DefaultLocaleChangedPublisher.class);
		bind(EventPublisher.class).to(DefaultEventPublisher.class);
		bind(ReviewedDiffManager.class).to(DefaultReviewedDiffManager.class);
		bind(OAuthTokenManager.class).to(DefaultOAuthTokenManager.class);
		bind(IssueReactionManager.class).to(DefaultIssueReactionManager.class);
		bind(IssueCommentReactionManager.class).to(DefaultIssueCommentReactionManager.class);
		bind(PullRequestReactionManager.class).to(DefaultPullRequestReactionManager.class);
		bind(PullRequestCommentReactionManager.class).to(DefaultPullRequestCommentReactionManager.class);

		bind(BaseProjectRoleManager.class).to(DefaultBaseProjectRoleManager.class);
        bind(HumanDuration.class);
		bind(WebHookManager.class);
		
		contribute(CodePullAuthorizationSource.class, DefaultJobManager.class);
        
		bind(CodeIndexManager.class).to(DefaultCodeIndexManager.class);
		bind(CodeSearchManager.class).to(DefaultCodeSearchManager.class);

		Bootstrap.executorService = new ThreadPoolExecutor(0, Integer.MAX_VALUE, 60L, TimeUnit.SECONDS,
				new SynchronousQueue<>()) {

			@Override
			public void execute(Runnable command) {
				try {
					super.execute(SecurityUtils.inheritSubject(command));
				} catch (RejectedExecutionException e) {
					if (!isShutdown())
						throw ExceptionUtils.unchecked(e);
				}
			}

        };

	    bind(ExecutorService.class).toProvider(() -> Bootstrap.executorService).in(Singleton.class);
	    
	    bind(OsInfo.class).toProvider(() -> ExecutorUtils.getOsInfo()).in(Singleton.class);
	    
	    contributeFromPackage(LogInstruction.class, LogInstruction.class);
	    
	    
		contribute(CodeProblemContribution.class, (build, blobPath, reportName) -> newArrayList());
	    
		contribute(LineCoverageContribution.class, (build, blobPath, reportName) -> new HashMap<>());
		contribute(AdministrationSettingContribution.class, () -> new ArrayList<>());
		contribute(ProjectSettingContribution.class, () -> new ArrayList<>());
		contribute(GitPreReceiveChecker.class, (project, submitter, refName, oldObjectId, newObjectId) -> null);

		bind(PackFilter.class);
	}
	
	private void configureSecurity() {
		contributeFromPackage(Realm.class, GeneralAuthorizingRealm.class);

		bind(ShiroFilterConfiguration.class).to(DefaultShiroFilterConfiguration.class);
		bind(RememberMeManager.class).to(DefaultRememberMeManager.class);
		bind(WebSecurityManager.class).to(DefaultWebSecurityManager.class);
		bind(FilterChainResolver.class).to(DefaultFilterChainResolver.class);
		bind(BasicAuthenticationFilter.class);
		bind(BearerAuthenticationFilter.class);
		bind(PasswordService.class).to(DefaultPasswordService.class);
		bind(ShiroFilter.class);
		install(new ShiroAopModule());
        contribute(FilterChainConfigurator.class, filterChainManager -> {
			filterChainManager.createChain("/**/info/refs", "noSessionCreation, authcBasic, authcBearer");
			filterChainManager.createChain("/**/git-upload-pack", "noSessionCreation, authcBasic, authcBearer");
			filterChainManager.createChain("/**/git-receive-pack", "noSessionCreation, authcBasic, authcBearer");
		});
        contributeFromPackage(Authenticator.class, Authenticator.class);
		
		bind(SSLFactory.class).toProvider(() -> KubernetesHelper.buildSSLFactory(Bootstrap.getTrustCertsDir())).in(Singleton.class);
	}
	
	private void configureGit() {
		contribute(ObjectMapperConfigurator.class, GitObjectMapperConfigurator.class);
		bind(GitService.class).to(DefaultGitService.class);
		bind(GitLocation.class).toProvider(GitLocationProvider.class);
		bind(GitFilter.class);
		bind(GoGetFilter.class);
		bind(GitLfsFilter.class);
		bind(GitPreReceiveCallback.class);
		bind(GitPostReceiveCallback.class);
		bind(SignatureVerificationManager.class).to(DefaultSignatureVerificationManager.class);
		contribute(CommandCreator.class, SshCommandCreator.class);
		contributeFromPackage(SignatureVerifier.class, SignatureVerifier.class);
	}
	
	private void configureRestful() {
		bind(ResourceConfig.class).toProvider(ResourceConfigProvider.class).in(Singleton.class);
		bind(ServletContainer.class).to(DefaultServletContainer.class);
		
		contribute(FilterChainConfigurator.class, filterChainManager -> filterChainManager.createChain("/~api/**", "noSessionCreation, authcBasic, authcBearer"));
		contribute(JerseyConfigurator.class, resourceConfig -> resourceConfig.packages(ProjectResource.class.getPackage().getName()));
		contribute(JerseyConfigurator.class, resourceConfig -> resourceConfig.register(ClusterResource.class));
	}

	private void configureWeb() {
		bind(WicketServlet.class).to(DefaultWicketServlet.class);
		bind(WicketFilter.class).to(DefaultWicketFilter.class);
		bind(EditSupportRegistry.class).to(DefaultEditSupportRegistry.class);
		bind(WebSocketManager.class).to(DefaultWebSocketManager.class);
		bind(SessionDataStoreFactory.class).to(DefaultSessionDataStoreFactory.class);

		contributeFromPackage(EditSupport.class, EditSupport.class);
		
		bind(org.apache.wicket.protocol.http.WebApplication.class).to(WebApplication.class);
		bind(Application.class).to(WebApplication.class);
		bind(AvatarManager.class).to(DefaultAvatarManager.class);
		bind(WebSocketManager.class).to(DefaultWebSocketManager.class);
		
		contributeFromPackage(EditSupport.class, EditSupportLocator.class);
				
		bind(CommitIndexedBroadcaster.class);
		
		contributeFromPackage(DiffRenderer.class, DiffRenderer.class);
		contributeFromPackage(BlobRenderer.class, BlobRenderer.class);

		contribute(Extension.class, new EmojiExtension());
		contribute(Extension.class, new SourcePositionTrackExtension());
		
		contributeFromPackage(HtmlProcessor.class, HtmlProcessor.class);

		contribute(ResourcePackScopeContribution.class, () -> newArrayList(WebApplication.class));
		
		contributeFromPackage(ExceptionHandler.class, ExceptionHandler.class);
		contributeFromPackage(ExceptionHandler.class, ConstraintViolationExceptionHandler.class);
		contributeFromPackage(ExceptionHandler.class, PageExpiredExceptionHandler.class);
		contributeFromPackage(ExceptionHandler.class, WebApplicationExceptionHandler.class);
		
		bind(UrlManager.class).to(DefaultUrlManager.class);
		bind(CodeCommentEventBroadcaster.class);
		bind(PullRequestEventBroadcaster.class);
		bind(IssueEventBroadcaster.class);
		bind(BuildEventBroadcaster.class);
		bind(AlertEventBroadcaster.class);
		bind(UploadManager.class).to(DefaultUploadManager.class);
		
		bind(TaskButton.TaskFutureManager.class);
		
		contribute(AdministrationMenuContribution.class, (AdministrationMenuContribution) ArrayList::new);
	}
	
	private void configureBuild() {
		bind(ResourceAllocator.class).to(DefaultResourceAllocator.class);
		bind(AgentManager.class).to(DefaultAgentManager.class);
		bind(AgentTokenManager.class).to(DefaultAgentTokenManager.class);
		bind(AgentAttributeManager.class).to(DefaultAgentAttributeManager.class);
		bind(AgentLastUsedDateManager.class).to(DefaultAgentLastUsedDateManager.class);
		
		contribute(ScriptContribution.class, new ScriptContribution() {

			@Override
			public GroovyScript getScript() {
				GroovyScript script = new GroovyScript();
				script.setName("determine-build-failure-investigator");
				script.setContent(newArrayList("io.onedev.server.util.ScriptContribution.determineBuildFailureInvestigator()"));
				return script;
			}
			
		});
		contribute(ScriptContribution.class, new ScriptContribution() {

			@Override
			public GroovyScript getScript() {
				GroovyScript script = new GroovyScript();
				script.setName("get-build-number");
				script.setContent(newArrayList("io.onedev.server.util.ScriptContribution.getBuildNumber()"));
				return script;
			}
			
		});
		contribute(ScriptContribution.class, new ScriptContribution() {

			@Override
			public GroovyScript getScript() {
				GroovyScript script = new GroovyScript();
				script.setName("get-current-user");
				script.setContent(newArrayList("io.onedev.server.util.ScriptContribution.getCurrentUser()"));
				return script;
			}

		});
	}
	
	private void configurePersistence() {
		bind(DataManager.class).to(DefaultDataManager.class);
		
		bind(Session.class).toProvider(SessionProvider.class);
		bind(EntityManager.class).toProvider(SessionProvider.class);
		bind(SessionFactory.class).toProvider(SessionFactoryProvider.class);
		bind(EntityManagerFactory.class).toProvider(SessionFactoryProvider.class);
		bind(SessionFactoryManager.class).to(DefaultSessionFactoryManager.class);
		
	    contribute(ObjectMapperConfigurator.class, HibernateObjectMapperConfigurator.class);
	    
		bind(Interceptor.class).to(HibernateInterceptor.class);
		bind(PhysicalNamingStrategy.class).toInstance(new PrefixedNamingStrategy("o_"));
		
		bind(SessionManager.class).to(DefaultSessionManager.class);
		bind(TransactionManager.class).to(DefaultTransactionManager.class);
		bind(IdManager.class).to(DefaultIdManager.class);
		bind(Dao.class).to(DefaultDao.class);
		
	    TransactionInterceptor transactionInterceptor = new TransactionInterceptor();
	    requestInjection(transactionInterceptor);
	    
	    bindInterceptor(Matchers.any(), new AbstractMatcher<AnnotatedElement>() {

			@Override
			public boolean matches(AnnotatedElement element) {
				return element.isAnnotationPresent(Transactional.class) && !((Method) element).isSynthetic();
			}
	    	
	    }, transactionInterceptor);
	    
	    SessionInterceptor sessionInterceptor = new SessionInterceptor();
	    requestInjection(sessionInterceptor);
	    
	    bindInterceptor(Matchers.any(), new AbstractMatcher<AnnotatedElement>() {

			@Override
			public boolean matches(AnnotatedElement element) {
				return element.isAnnotationPresent(Sessional.class) && !((Method) element).isSynthetic();
			}
	    	
	    }, sessionInterceptor);
	    
	    contribute(PersistListener.class, new PersistListener() {
			
			@Override
			public boolean onSave(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types)
					throws CallbackException {
				return false;
			}
			
			@Override
			public boolean onLoad(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types)
					throws CallbackException {
				return false;
			}
			
			@Override
			public boolean onFlushDirty(Object entity, Serializable id, Object[] currentState, Object[] previousState,
					String[] propertyNames, Type[] types) throws CallbackException {
				return false;
			}
			
			@Override
			public void onDelete(Object entity, Serializable id, Object[] state, String[] propertyNames, Type[] types)
					throws CallbackException {
			}

		});
	    
		bind(XStream.class).toProvider(new com.google.inject.Provider<XStream>() {

			@SuppressWarnings("rawtypes")
			@Override
			public XStream get() {
				ReflectionProvider reflectionProvider = JVM.newReflectionProvider();
				XStream xstream = new XStream(reflectionProvider) {

					@Override
					protected MapperWrapper wrapMapper(MapperWrapper next) {
						return new MapperWrapper(next) {
							
							@Override
							public boolean shouldSerializeMember(Class definedIn, String fieldName) {
								Field field = reflectionProvider.getField(definedIn, fieldName);
								
								return field.getAnnotation(XStreamOmitField.class) == null 
										&& field.getAnnotation(Transient.class) == null 
										&& field.getAnnotation(OneToMany.class) == null 
										&& (field.getAnnotation(OneToOne.class) == null || field.getAnnotation(JoinColumn.class) != null)  
										&& field.getAnnotation(Version.class) == null;
							}
							
							@Override
							public String serializedClass(Class type) {
								if (type == null)
									return super.serializedClass(type);
								else if (type == PersistentBag.class)
									return super.serializedClass(ArrayList.class);
								else if (type.getName().contains("$HibernateProxy$"))
									return StringUtils.substringBefore(type.getName(), "$HibernateProxy$");
								else
									return super.serializedClass(type);
							}
							
						};
					}
					
				};
				xstream.allowTypesByWildcard(new String[] {"**"});				
				
				// register NullConverter as highest; otherwise NPE when unmarshal a map 
				// containing an entry with value set to null.
				xstream.registerConverter(new NullConverter(), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new StringConverter(), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new VersionedDocumentConverter(), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new HibernateProxyConverter(), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new CollectionConverter(xstream.getMapper()), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new MapConverter(xstream.getMapper()), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new ISO8601DateConverter(), XStream.PRIORITY_VERY_HIGH);
				xstream.registerConverter(new ISO8601SqlTimestampConverter(), XStream.PRIORITY_VERY_HIGH); 
				xstream.registerConverter(new ReflectionConverter(xstream.getMapper(), xstream.getReflectionProvider()), 
						XStream.PRIORITY_VERY_LOW);
				xstream.autodetectAnnotations(true);
				return xstream;
			}
			
		}).in(Singleton.class);
	}
	
	@Override
	protected Class<? extends AbstractPlugin> getPluginClass() {
		if (Bootstrap.command != null) {
			if (RestoreDatabase.COMMAND.equals(Bootstrap.command.getName()))
				return RestoreDatabase.class;
			else if (ApplyDatabaseConstraints.COMMAND.equals(Bootstrap.command.getName()))
				return ApplyDatabaseConstraints.class;
			else if (BackupDatabase.COMMAND.equals(Bootstrap.command.getName()))
				return BackupDatabase.class;
			else if (CheckDataVersion.COMMAND.equals(Bootstrap.command.getName()))
				return CheckDataVersion.class;
			else if (Upgrade.COMMAND.equals(Bootstrap.command.getName()))
				return Upgrade.class;
			else if (CleanDatabase.COMMAND.equals(Bootstrap.command.getName()))
				return CleanDatabase.class;
			else if (ResetAdminPassword.COMMAND.equals(Bootstrap.command.getName()))
				return ResetAdminPassword.class;
			else
				throw new RuntimeException("Unrecognized command: " + Bootstrap.command.getName());
		} else {
			return AppServer.class;
		}		
	}

}

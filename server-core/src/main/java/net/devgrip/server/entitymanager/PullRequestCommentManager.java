package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PullRequestComment;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

public interface PullRequestCommentManager extends EntityManager<PullRequestComment> {

	void create(PullRequestComment comment);

	void create(PullRequestComment comment, Collection<String> notifiedEmailAddresses);

	void update(PullRequestComment comment);
	
}

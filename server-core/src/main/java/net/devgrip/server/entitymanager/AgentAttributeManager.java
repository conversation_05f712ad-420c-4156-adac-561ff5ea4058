package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Agent;
import net.devgrip.server.model.AgentAttribute;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;
import java.util.Map;

public interface AgentAttributeManager extends EntityManager<AgentAttribute> {

	void create(AgentAttribute attribute);
	
	Collection<String> getAttributeNames();

	void syncAttributes(Agent agent, Map<String, String> attributeMap);
	
}

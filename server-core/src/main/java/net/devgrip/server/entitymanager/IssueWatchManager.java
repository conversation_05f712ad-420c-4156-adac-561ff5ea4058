package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueWatch;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.util.watch.WatchStatus;

import javax.annotation.Nullable;
import java.util.Collection;

public interface IssueWatchManager extends EntityManager<IssueWatch> {
	
	@Nullable
	IssueWatch find(Issue issue, User user);

	void watch(Issue issue, User user, boolean watching);

    void createOrUpdate(IssueWatch watch);
	
	void setWatchStatus(User user, Collection<Issue> issues, WatchStatus watchStatus);
	
}

package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.PullRequestMentionManager;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestMention;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DefaultPullRequestMentionManager extends BaseEntityManager<PullRequestMention>
		implements PullRequestMentionManager {

	@Inject
	public DefaultPullRequestMentionManager(Dao dao) {
		super(dao);
	}

	@Override
	public void mention(PullRequest request, User user) {
		if (request.getMentions().stream().noneMatch(it->it.getUser().equals(user))) {
			PullRequestMention mention = new PullRequestMention();
			mention.setRequest(request);
			mention.setUser(user);
			dao.persist(mention);
		}
	}

}

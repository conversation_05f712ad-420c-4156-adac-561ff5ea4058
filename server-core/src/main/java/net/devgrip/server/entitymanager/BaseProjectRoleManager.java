package net.devgrip.server.entitymanager;

import net.devgrip.server.model.BaseProjectRole;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.Role;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

/**
 * A manager to manages default roles of project
 */

public interface BaseProjectRoleManager extends EntityManager<BaseProjectRole> {

    /**
     * sync default roles of project
     * @param project
     * @param roles
     */
    void syncRoles(Project project, Collection<Role> roles);


    void create(BaseProjectRole baseProjectRole);
}

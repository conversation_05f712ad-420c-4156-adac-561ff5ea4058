package net.devgrip.server.entitymanager;

import net.devgrip.server.model.LabelSpec;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;
import java.util.List;

public interface LabelSpecManager extends EntityManager<LabelSpec> {
	
	@Nullable
	LabelSpec find(String name);

	void sync(List<LabelSpec> labelSpecs);
	
	void createOrUpdate(LabelSpec labelSpec);
	
}

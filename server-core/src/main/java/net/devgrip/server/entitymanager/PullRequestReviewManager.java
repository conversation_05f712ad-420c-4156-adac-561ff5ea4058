package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestReview;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;
import java.util.Collection;

public interface PullRequestReviewManager extends EntityManager<PullRequestReview> {
	
    void review(PullRequest request, boolean approved, @Nullable String note);
	
	void populateReviews(Collection<PullRequest> requests);

    void createOrUpdate(PullRequestReview review);
	
}

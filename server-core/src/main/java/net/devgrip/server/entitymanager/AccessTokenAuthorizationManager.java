package net.devgrip.server.entitymanager;

import net.devgrip.server.model.AccessToken;
import net.devgrip.server.model.AccessTokenAuthorization;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

public interface AccessTokenAuthorizationManager extends EntityManager<AccessTokenAuthorization> {

	void syncAuthorizations(AccessToken token, Collection<AccessTokenAuthorization> authorizations);
	
    void createOrUpdate(AccessTokenAuthorization authorization);
	
}

package net.devgrip.server.entitymanager.impl;

import static net.devgrip.server.model.PackBlobReference.PROP_PACK;
import static net.devgrip.server.model.PackBlobReference.PROP_PACK_BLOB;

import java.io.ObjectStreamException;
import java.io.Serializable;

import javax.inject.Inject;
import javax.inject.Singleton;

import org.hibernate.criterion.Restrictions;

import com.google.common.base.Preconditions;

import net.devgrip.commons.loader.ManagedSerializedForm;
import net.devgrip.server.entitymanager.PackBlobReferenceManager;
import net.devgrip.server.model.Pack;
import net.devgrip.server.model.PackBlob;
import net.devgrip.server.model.PackBlobReference;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

@Singleton
public class DefaultPackBlobReferenceManager extends BaseEntityManager<PackBlobReference> 
		implements PackBlobReferenceManager, Serializable {

	@Inject
	public DefaultPackBlobReferenceManager(Dao dao) {
		super(dao);
	}

	@Transactional
	@Override
	public void create(PackBlobReference blobReference) {
		Preconditions.checkState(blobReference.isNew());
		dao.persist(blobReference);
	}

	@Transactional
	@Override
	public void createIfNotExist(Pack pack, PackBlob packBlob) {
		var criteria = newCriteria();
		criteria.add(Restrictions.eq(PROP_PACK, pack));
		criteria.add(Restrictions.eq(PROP_PACK_BLOB, packBlob));
		if (find(criteria) == null) {
			var blobReference = new PackBlobReference();
			blobReference.setPack(pack);
			blobReference.setPackBlob(packBlob);
			dao.persist(blobReference);
		}
	}
	
	public Object writeReplace() throws ObjectStreamException {
		return new ManagedSerializedForm(PackBlobReferenceManager.class);
	}
	
}

package net.devgrip.server.entitymanager.impl;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.AgentLastUsedDateManager;
import net.devgrip.server.model.AgentLastUsedDate;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DefaultAgentLastUsedDateManager extends BaseEntityManager<AgentLastUsedDate> implements AgentLastUsedDateManager {

	@Inject
	public DefaultAgentLastUsedDateManager(Dao dao) {
		super(dao);
	}

	@Transactional
	@Override
	public void create(AgentLastUsedDate lastUsedDate) {
		Preconditions.checkState(lastUsedDate.isNew());
		dao.persist(lastUsedDate);
	}
	
}

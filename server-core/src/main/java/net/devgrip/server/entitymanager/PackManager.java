package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Pack;
import net.devgrip.server.model.PackBlob;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.util.ProjectPackTypeStat;
import net.devgrip.server.util.criteria.Criteria;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

public interface PackManager extends EntityManager<Pack> {

	List<Pack> query(@Nullable Project project, EntityQuery<Pack> packQuery, 
					 boolean loadLabelsAndBlobs, int firstResult, int maxResults);

	int count(@Nullable Project project, Criteria<Pack> packCriteria);

	List<Pack> queryPrevComparables(Pack compareWith, String fuzzyQuery, int count);
	
	List<String> queryVersions(Project project, String type, String name, 
							   @Nullable String lastVersion, int count);
	
	List<String> queryProps(Project project, String propName, String matchWith, int count);
	
	List<ProjectPackTypeStat> queryTypeStats(Collection<Project> projects);
	
	@Nullable
	Pack findByNameAndVersion(Project project, String type, String name, String version);

	List<Pack> query(Project project, String type, @Nullable Boolean prerelease);
	
	List<Pack> queryByName(Project project, String type, String name, 
						   @Nullable Comparator<Pack> sortComparator);
	
	List<Pack> queryLatests(Project project, String type, @Nullable String nameQuery, 
							boolean includePrerelease, int firstResult, int maxResults);

	int countNames(Project project, String type, @Nullable String nameQuery, 
				   boolean includePrerelease);

	List<String> queryNames(Project project, String type, @Nullable String nameQuery, 
							boolean includePrerelease, int firstResult, int maxResults);
	
	Map<String, List<Pack>> loadPacks(List<String> names, boolean includePrerelease, 
									  @Nullable Comparator<Pack> sortComparator);
	
	void deleteByNameAndVersion(Project project, String type, String name, String version);

	void createOrUpdate(Pack pack, @Nullable Collection<PackBlob> packBlobs, boolean postPublishEvent);
	
	void delete(Collection<Pack> packs);

}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestReaction;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PullRequestReactionManager extends EntityManager<PullRequestReaction> {

    void create(PullRequestReaction reaction);

    void toggleEmoji(User user, PullRequest request, String emoji);

} 

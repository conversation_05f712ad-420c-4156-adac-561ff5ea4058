package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.CodeCommentMentionManager;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.CodeCommentMention;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DefaultCodeCommentMentionManager extends BaseEntityManager<CodeCommentMention>
		implements CodeCommentMentionManager {

	@Inject
	public DefaultCodeCommentMentionManager(Dao dao) {
		super(dao);
	}

	@Override
	public void mention(CodeComment comment, User user) {
		if (comment.getMentions().stream().noneMatch(it->it.getUser().equals(user))) {
			CodeCommentMention mention = new CodeCommentMention();
			mention.setComment(comment);
			mention.setUser(user);
			dao.persist(mention);
		}
	}

}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Membership;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;
import java.util.List;

public interface MembershipManager extends EntityManager<Membership> {
	
	void delete(Collection<Membership> memberships);

	void syncMemberships(User user, Collection<String> groupNames);

    void create(Membership membership);

    List<User> queryMembers(User user);
	
}

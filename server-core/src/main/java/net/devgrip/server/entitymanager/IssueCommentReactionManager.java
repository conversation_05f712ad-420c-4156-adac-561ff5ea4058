package net.devgrip.server.entitymanager;

import net.devgrip.server.model.IssueComment;
import net.devgrip.server.model.IssueCommentReaction;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueCommentReactionManager extends EntityManager<IssueCommentReaction> {

    void create(IssueCommentReaction reaction);

    void toggleEmoji(User user, IssueComment comment, String emoji);

} 

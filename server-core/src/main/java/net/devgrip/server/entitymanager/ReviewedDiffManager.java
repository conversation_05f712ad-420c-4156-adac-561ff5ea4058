package net.devgrip.server.entitymanager;

import net.devgrip.server.model.ReviewedDiff;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Map;

public interface ReviewedDiffManager extends EntityManager<ReviewedDiff> {
	
	Map<String, ReviewedDiff> query(User user, String oldCommitHash, String newCommitHash);

	void createOrUpdate(ReviewedDiff status);
	
}

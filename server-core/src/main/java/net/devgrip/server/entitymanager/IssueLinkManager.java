package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueLink;
import net.devgrip.server.model.LinkSpec;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

public interface IssueLinkManager extends EntityManager<IssueLink> {

	void syncLinks(LinkSpec spec, Issue issue, Collection<Issue> linkedIssues, boolean opposite);

    void create(IssueLink link);

    void populateLinks(Collection<Issue> issues);
	
	void loadDeepLinks(Issue issue);
	
}

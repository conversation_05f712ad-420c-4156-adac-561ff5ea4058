package net.devgrip.server.entitymanager;

import net.devgrip.server.model.CommitQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface CommitQueryPersonalizationManager extends EntityManager<CommitQueryPersonalization> {
	
	CommitQueryPersonalization find(Project project, User user);

    void createOrUpdate(CommitQueryPersonalization commitQueryPersonalization);
	
}

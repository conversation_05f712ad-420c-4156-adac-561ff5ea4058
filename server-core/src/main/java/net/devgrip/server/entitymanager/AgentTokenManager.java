package net.devgrip.server.entitymanager;

import net.devgrip.server.model.AgentToken;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;
import java.util.List;

public interface AgentTokenManager extends EntityManager<AgentToken> {
	
	void createOrUpdate(AgentToken token);
	
	@Nullable
	AgentToken find(String value);
	
	List<AgentToken> queryUnused();
	
	void deleteUnused();
	
}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Iteration;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;

public interface IterationManager extends EntityManager<Iteration> {
	
	@Nullable
    Iteration findInHierarchy(Project project, String name);
	
	void delete(Iteration iteration);

	Iteration findInHierarchy(String iterationFQN);

    void createOrUpdate(Iteration iteration);
	
}

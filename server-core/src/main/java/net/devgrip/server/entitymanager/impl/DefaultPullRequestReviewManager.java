package net.devgrip.server.entitymanager.impl;

import java.util.ArrayList;
import java.util.Collection;

import javax.inject.Inject;
import javax.inject.Singleton;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Root;

import com.google.common.base.Preconditions;

import net.devgrip.server.entitymanager.PullRequestChangeManager;
import net.devgrip.server.entitymanager.PullRequestReviewManager;
import net.devgrip.server.event.ListenerRegistry;
import net.devgrip.server.event.project.pullrequest.PullRequestReviewRequested;
import net.devgrip.server.event.project.pullrequest.PullRequestReviewerRemoved;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestChange;
import net.devgrip.server.model.PullRequestReview;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestApproveData;
import net.devgrip.server.model.support.pullrequest.changedata.PullRequestRequestedForChangesData;
import net.devgrip.server.model.PullRequestReview.Status;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.security.SecurityUtils;

@Singleton
public class DefaultPullRequestReviewManager extends BaseEntityManager<PullRequestReview> 
		implements PullRequestReviewManager {

	private final PullRequestChangeManager changeManager;
	
	private final ListenerRegistry listenerRegistry;
	
	@Inject
	public DefaultPullRequestReviewManager(Dao dao, PullRequestChangeManager changeManager, 
			ListenerRegistry listenerRegistry) {
		super(dao);
		this.changeManager = changeManager;
		this.listenerRegistry = listenerRegistry;
	}

	@Transactional
	@Override
	public void createOrUpdate(PullRequestReview review) {
 		review.setDirty(false);
		dao.persist(review);
		
		if (review.getStatus() == Status.PENDING) {
			listenerRegistry.post(new PullRequestReviewRequested(
					SecurityUtils.getUser(), review.getStatusDate(), 
					review.getRequest(), review.getUser()));
		} else if (review.getStatus() == Status.EXCLUDED) {
			listenerRegistry.post(new PullRequestReviewerRemoved(
					SecurityUtils.getUser(), review.getStatusDate(), 
					review.getRequest(), review.getUser()));
		}
	}

	@Sessional
	@Override
	public void populateReviews(Collection<PullRequest> requests) {
		CriteriaBuilder builder = getSession().getCriteriaBuilder();
		CriteriaQuery<PullRequestReview> query = builder.createQuery(PullRequestReview.class);
		
		Root<PullRequestReview> root = query.from(PullRequestReview.class);
		query.select(root);
		Join<PullRequest, PullRequest> join = root.join(PullRequestReview.PROP_REQUEST);
		query.where(join.in(requests));
		
		for (PullRequest request: requests) 
			request.setReviews(new ArrayList<>());
		
		for (PullRequestReview review: getSession().createQuery(query).getResultList())
			review.getRequest().getReviews().add(review);
	}
 	
	@Transactional
	@Override
	public void review(PullRequest request, boolean approved, String note) {
		User user = SecurityUtils.getAuthUser();
		PullRequestReview review = request.getReview(user);
		Preconditions.checkState(review != null && review.getStatus() == PullRequestReview.Status.PENDING);
		if (approved)
			review.setStatus(PullRequestReview.Status.APPROVED);
		else
			review.setStatus(PullRequestReview.Status.REQUESTED_FOR_CHANGES);
			
		createOrUpdate(review);
		
		PullRequestChange change = new PullRequestChange();
		change.setDate(review.getStatusDate());
		change.setRequest(request);
		change.setUser(user);
		if (approved)
			change.setData(new PullRequestApproveData());
		else
			change.setData(new PullRequestRequestedForChangesData());
		
		changeManager.create(change, note);
	}

}

package net.devgrip.server.entitymanager;

import javax.annotation.Nullable;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestWatch;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.util.watch.WatchStatus;

import java.util.Collection;

public interface PullRequestWatchManager extends EntityManager<PullRequestWatch> {
	
	@Nullable
	PullRequestWatch find(PullRequest request, User user);
	
	void watch(PullRequest request, User user, boolean watching);

    void createOrUpdate(PullRequestWatch watch);

	void setWatchStatus(User user, Collection<PullRequest> requests, WatchStatus watchStatus);
	
}

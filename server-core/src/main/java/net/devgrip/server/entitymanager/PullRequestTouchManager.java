package net.devgrip.server.entitymanager;

import java.util.List;

import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequestTouch;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PullRequestTouchManager extends EntityManager<PullRequestTouch> {
	
	void touch(Project project, Long requestId, boolean newRequest);
	
	List<PullRequestTouch> queryTouchesAfter(Long projectId, Long afterTouchId, int count);
	
}

package net.devgrip.server.entitymanager.impl;

import java.util.Collection;

import javax.inject.Inject;
import javax.inject.Singleton;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.LinkAuthorizationManager;
import net.devgrip.server.model.LinkAuthorization;
import net.devgrip.server.model.LinkSpec;
import net.devgrip.server.model.Role;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

@Singleton
public class DefaultLinkAuthorizationManager extends BaseEntityManager<LinkAuthorization> implements LinkAuthorizationManager {

	@Inject
	public DefaultLinkAuthorizationManager(Dao dao) {
		super(dao);
	}

	@Transactional
	@Override
	public void syncAuthorizations(Role role, Collection<LinkSpec> authorizedLinks) {
		for (LinkAuthorization authorization: role.getLinkAuthorizations()) {
			if (!authorizedLinks.contains(authorization.getLink()))
				delete(authorization);
		}

		for (LinkSpec link: authorizedLinks) {
			boolean found = false;
			for (LinkAuthorization authorization: role.getLinkAuthorizations()) {
				if (authorization.getLink().equals(link)) {
					found = true;
					break;
				}
			}
			if (!found) {
				LinkAuthorization authorization = new LinkAuthorization();
				authorization.setLink(link);
				authorization.setRole(role);
				create(authorization);
			}
		}
	}

	@Transactional
	@Override
	public void create(LinkAuthorization authorization) {
		Preconditions.checkState(authorization.isNew());
		dao.persist(authorization);
	}

}

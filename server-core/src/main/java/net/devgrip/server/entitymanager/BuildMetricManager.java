package net.devgrip.server.entitymanager;

import net.devgrip.server.model.AbstractEntity;
import net.devgrip.server.model.Build;
import net.devgrip.server.model.Project;
import net.devgrip.server.search.buildmetric.BuildMetricQuery;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.Map;

public interface BuildMetricManager {
	
	@Nullable
	<T extends AbstractEntity> T find(Class<T> metricClass, Build build, String reportName);
	
	<T extends AbstractEntity> Map<Integer, T> queryStats(Project project, Class<T> metricClass, BuildMetricQuery query);
	
	Map<String, Collection<String>> getAccessibleReportNames(Project project, Class<?> metricClass);
	
}

package net.devgrip.server.entitymanager;

import java.util.Collection;

import javax.annotation.Nullable;

import net.devgrip.server.model.Build;
import net.devgrip.server.model.BuildParam;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;

public interface BuildParamManager extends EntityManager<BuildParam> {
	
	void create(BuildParam param);
	
	void deleteParams(Build build);
	
	Collection<String> getParamNames(@Nullable Project project);

}

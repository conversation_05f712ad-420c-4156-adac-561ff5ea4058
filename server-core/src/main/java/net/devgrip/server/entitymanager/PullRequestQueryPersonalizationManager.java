package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequestQueryPersonalization;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PullRequestQueryPersonalizationManager extends EntityManager<PullRequestQueryPersonalization> {
	
	PullRequestQueryPersonalization find(Project project, User user);

    void createOrUpdate(PullRequestQueryPersonalization personalization);
	
    void delete(PullRequestQueryPersonalization personalization);
}

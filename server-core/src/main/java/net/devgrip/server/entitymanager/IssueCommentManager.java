package net.devgrip.server.entitymanager;

import java.util.Collection;

import net.devgrip.server.model.IssueComment;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueCommentManager extends EntityManager<IssueComment> {

	void create(IssueComment comment);

	void create(IssueComment comment, Collection<String> notifiedEmailAddresses);
	
	void delete(IssueComment comment);
	
	void update(IssueComment comment);
	
}

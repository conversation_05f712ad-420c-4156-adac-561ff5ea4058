package net.devgrip.server.entitymanager;

import java.util.List;

import javax.annotation.Nullable;

import net.devgrip.server.model.UserInvitation;
import net.devgrip.server.persistence.dao.EntityManager;

public interface UserInvitationManager extends EntityManager<UserInvitation> {
	
	@Nullable
	UserInvitation findByEmailAddress(String emailAddress);
	
	@Nullable
	UserInvitation findByInvitationCode(String invitationCode);
	
	void sendInvitationEmail(UserInvitation invitation);

	int count(@Nullable String term);
	
	List<UserInvitation> query(@Nullable String term, int firstResult, int maxResults);

    void create(UserInvitation invitation);
}

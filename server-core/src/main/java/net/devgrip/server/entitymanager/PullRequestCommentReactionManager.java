package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PullRequestComment;
import net.devgrip.server.model.PullRequestCommentReaction;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PullRequestCommentReactionManager extends EntityManager<PullRequestCommentReaction> {

    void create(PullRequestCommentReaction reaction);

    void toggleEmoji(User user, PullRequestComment comment, String emoji);

} 

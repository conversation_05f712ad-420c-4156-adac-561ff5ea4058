package net.devgrip.server.entitymanager.impl;

import javax.inject.Inject;
import javax.inject.Singleton;

import com.google.common.base.Preconditions;

import net.devgrip.server.entitymanager.IssueCommentReactionManager;
import net.devgrip.server.model.IssueComment;
import net.devgrip.server.model.IssueCommentReaction;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

@Singleton
public class DefaultIssueCommentReactionManager extends BaseEntityManager<IssueCommentReaction> 
        implements IssueCommentReactionManager {

    @Inject
    public DefaultIssueCommentReactionManager(Dao dao) {
        super(dao);
    }
    
    @Transactional
    @Override
    public void create(IssueCommentReaction reaction) {
        Preconditions.checkState(reaction.isNew());
        dao.persist(reaction);
    }

    @Transactional
    @Override
    public void toggleEmoji(User user, IssueComment comment, String emoji) {
        var reaction = comment.getReactions().stream()
                .filter(r -> r.getUser().equals(user) && r.getEmoji().equals(emoji))
                .findFirst()
                .orElse(null);
        if (reaction == null) {
            reaction = new IssueCommentReaction();
            reaction.setComment(comment);
            reaction.setUser(user);
            reaction.setEmoji(emoji);
            create(reaction);
            comment.getReactions().add(reaction);
        } else {
            comment.getReactions().remove(reaction);
            dao.remove(reaction);
        }
    }

} 

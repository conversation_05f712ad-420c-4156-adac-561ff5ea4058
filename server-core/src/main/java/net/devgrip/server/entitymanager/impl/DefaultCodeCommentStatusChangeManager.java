package net.devgrip.server.entitymanager.impl;

import java.util.Collection;

import javax.inject.Inject;
import javax.inject.Singleton;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.CodeCommentStatusChangeManager;
import net.devgrip.server.event.ListenerRegistry;
import net.devgrip.server.event.project.codecomment.CodeCommentStatusChanged;
import net.devgrip.server.event.project.pullrequest.PullRequestCodeCommentStatusChanged;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.CodeCommentReply;
import net.devgrip.server.model.CodeCommentStatusChange;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

@Singleton
public class DefaultCodeCommentStatusChangeManager extends BaseEntityManager<CodeCommentStatusChange>
		implements CodeCommentStatusChangeManager {

	private final ListenerRegistry listenerRegistry;
	
	@Inject
	public DefaultCodeCommentStatusChangeManager(Dao dao, ListenerRegistry listenerRegistry) {
		super(dao);
		this.listenerRegistry = listenerRegistry;
	}

	@Transactional
	@Override
	public void create(CodeCommentStatusChange change, String note) {
		Preconditions.checkState(change.isNew());
		
		CodeComment comment = change.getComment();
		comment.setResolved(change.isResolved());
		
		dao.persist(change);
		
		if (note != null) {
			CodeCommentReply reply = new CodeCommentReply();
			reply.setComment(comment);
			reply.setCompareContext(change.getCompareContext());
			reply.setContent(note);
			reply.setDate(change.getDate());
			reply.setUser(change.getUser());
			dao.persist(reply);
			
			comment.setReplyCount(comment.getReplyCount()+1);
		}
		listenerRegistry.post(new CodeCommentStatusChanged(change, note));
		
		PullRequest request = comment.getCompareContext().getPullRequest();
		if (request != null) 
			listenerRegistry.post(new PullRequestCodeCommentStatusChanged(request, change, note));
	}
	
	@Transactional
	@Override
	public void create(Collection<CodeCommentStatusChange> changes, String note) {
		for (CodeCommentStatusChange  change: changes)
			create(change, note);
	}
	
}

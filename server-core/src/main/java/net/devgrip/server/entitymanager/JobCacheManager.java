package net.devgrip.server.entitymanager;

import net.devgrip.server.model.JobCache;
import net.devgrip.server.persistence.dao.EntityManager;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Nullable;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.function.Consumer;

public interface JobCacheManager extends EntityManager<JobCache> {

	@Nullable
	Pair<Long, Long> getCacheInfoForDownload(Long projectId, String cacheKey);

	@Nullable
	Pair<Long, Long> getCacheInfoForDownload(Long projectId, List<String> loadKeys);
	
	@Nullable Long getCacheIdForUpload(Long projectId, String cacheKey);
	
	boolean downloadCache(Long projectId, Long cacheId, List<String> cachePaths,
						  Consumer<InputStream> cacheStreamHandler);
	
	void downloadCache(Long projectId, Long cacheId, List<String> cachePaths,
					   OutputStream cacheStream);

	void uploadCache(Long projectId, Long cacheId, List<String> cachePaths, InputStream cacheStream);

	void uploadCache(Long projectId, Long cacheId, List<String> cachePaths,
					 Consumer<OutputStream> cacheStreamHandler);
	
	@Nullable
	Long getCacheSize(Long projectId, Long cacheId);
	
}

package net.devgrip.server.entitymanager;

import net.devgrip.commons.utils.PlanarRange;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.util.criteria.Criteria;
import org.eclipse.jgit.lib.ObjectId;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface CodeCommentManager extends EntityManager<CodeComment> {
	
	Collection<CodeComment> query(Project project, ObjectId commitId, @Nullable String path);
	
	Collection<CodeComment> query(Project project, ObjectId...commitIds);
	
	Map<CodeComment, PlanarRange> queryInHistory(Project project, ObjectId commitId, String path);
		
	List<CodeComment> query(Project project, @Nullable PullRequest request, 
			EntityQuery<CodeComment> commentQuery, int firstResult, int maxResults);
	
	int count(Project project, @Nullable PullRequest request, Criteria<CodeComment> commentCriteria);

	void delete(Collection<CodeComment> comments, Project project);

	void delete(CodeComment comment);
	
    @Nullable
    CodeComment findByUUID(String uuid);

    void create(CodeComment comment);

	void update(CodeComment comment);

	Collection<Long> getProjectIds();
	
}

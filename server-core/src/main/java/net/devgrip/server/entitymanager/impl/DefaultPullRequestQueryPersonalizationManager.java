package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.PullRequestQueryPersonalizationManager;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.PullRequestQueryPersonalization;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.NamedQuery;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.persistence.dao.EntityCriteria;
import org.hibernate.criterion.Restrictions;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.Collection;
import java.util.HashSet;
import java.util.stream.Collectors;

@Singleton
public class DefaultPullRequestQueryPersonalizationManager extends BaseEntityManager<PullRequestQueryPersonalization> 
		implements PullRequestQueryPersonalizationManager {

	@Inject
	public DefaultPullRequestQueryPersonalizationManager(Dao dao) {
		super(dao);
	}

	@Sessional
	@Override
	public PullRequestQueryPersonalization find(Project project, User user) {
		EntityCriteria<PullRequestQueryPersonalization> criteria = newCriteria();
		criteria.add(Restrictions.and(Restrictions.eq("project", project), Restrictions.eq("user", user)));
		criteria.setCacheable(true);
		return find(criteria);
	}

	@Transactional
	@Override
	public void createOrUpdate(PullRequestQueryPersonalization personalization) {
		Collection<String> retainNames = new HashSet<>();
		retainNames.addAll(personalization.getQueries().stream()
				.map(it->NamedQuery.PERSONAL_NAME_PREFIX+it.getName()).collect(Collectors.toSet()));
		retainNames.addAll(personalization.getProject().getNamedPullRequestQueries().stream()
				.map(it->NamedQuery.COMMON_NAME_PREFIX+it.getName()).collect(Collectors.toSet()));
		personalization.getQueryWatchSupport().getQueryWatches().keySet().retainAll(retainNames);
		
		if (personalization.getQueryWatchSupport().getQueryWatches().isEmpty() && personalization.getQueries().isEmpty()) {
			if (!personalization.isNew())
				delete(personalization);
		} else {
			dao.persist(personalization);
		}
	}

}

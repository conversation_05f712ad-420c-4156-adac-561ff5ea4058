package net.devgrip.server.entitymanager.impl;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.entitymanager.PullRequestCommentManager;
import net.devgrip.server.entitymanager.PullRequestUpdateManager;
import net.devgrip.server.event.ListenerRegistry;
import net.devgrip.server.event.project.pullrequest.PullRequestUpdated;
import net.devgrip.server.git.service.GitService;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestUpdate;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.persistence.dao.EntityCriteria;
import org.eclipse.jgit.lib.ObjectId;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.List;

@Singleton
public class DefaultPullRequestUpdateManager extends BaseEntityManager<PullRequestUpdate> 
		implements PullRequestUpdateManager {
	
	private final ProjectManager projectManager;
	
	private final GitService gitService;
	
	private final ListenerRegistry listenerRegistry;
	
	@Inject
	public DefaultPullRequestUpdateManager(Dao dao, ProjectManager projectManager, ListenerRegistry listenerRegistry, 
										   PullRequestCommentManager commentManager, GitService gitService) {
		super(dao);

		this.projectManager = projectManager;
		this.gitService = gitService;
		this.listenerRegistry = listenerRegistry;
	}

	@Transactional
	@Override
	public void create(PullRequestUpdate update) {
		Preconditions.checkState(update.isNew());
		dao.persist(update);
		PullRequest request = update.getRequest();
		if (!request.getTargetProject().equals(request.getSourceProject())) {
			if (projectManager.hasLfsObjects(request.getSourceProject().getId())) {
				gitService.pushLfsObjects(
						request.getSourceProject(), request.getSourceRef(),
						request.getTargetProject(), update.getHeadRef(),
						ObjectId.fromString(update.getHeadCommitHash()));
			}
			gitService.push(request.getSourceProject(), update.getHeadCommitHash(), 
					request.getTargetProject(), update.getHeadRef());
		} else {
			ObjectId headCommitId = ObjectId.fromString(update.getHeadCommitHash());
			gitService.updateRef(request.getTargetProject(), update.getHeadRef(), headCommitId, null);
		}
	}

	@Transactional
	@Override
	public void checkUpdate(PullRequest request) {
		if (!request.getLatestUpdate().getHeadCommitHash().equals(request.getSource().getObjectName())) {
			request.getAutoMerge().setEnabled(false);
			ObjectId mergeBase = gitService.getMergeBase(
					request.getTargetProject(), request.getTarget().getObjectId(), 
					request.getSourceProject(), request.getSource().getObjectId());
			if (mergeBase != null) {
				PullRequestUpdate update = new PullRequestUpdate();
				update.setRequest(request);
				update.setHeadCommitHash(request.getSource().getObjectName());
				update.setTargetHeadCommitHash(request.getTarget().getObjectName());
				request.getUpdates().add(update);
				create(update);

				gitService.updateRef(request.getTargetProject(), request.getHeadRef(), 
						ObjectId.fromString(request.getLatestUpdate().getHeadCommitHash()), null);
				listenerRegistry.post(new PullRequestUpdated(update));
			}
		}
	}
	
	@Sessional
	@Override
	public List<PullRequestUpdate> queryAfter(Long projectId, Long afterUpdateId, int count) {
		EntityCriteria<PullRequestUpdate> criteria = newCriteria();
		criteria.createCriteria("request").add(Restrictions.eq("targetProject.id", projectId));
		criteria.add(Restrictions.gt("id", afterUpdateId));
		criteria.addOrder(Order.asc("id"));
		return query(criteria, 0, count);
	}

}

package net.devgrip.server.entitymanager;

import java.util.List;

import javax.annotation.Nullable;

import org.eclipse.jgit.lib.ObjectId;

import net.devgrip.server.model.PendingSuggestionApply;
import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PendingSuggestionApplyManager extends EntityManager<PendingSuggestionApply> {
	
	ObjectId apply(User user, PullRequest request, String commitMessage);

	void discard(@Nullable User user, PullRequest request);

	List<PendingSuggestionApply> query(User user, PullRequest request);

    void create(PendingSuggestionApply pendingApply);
}

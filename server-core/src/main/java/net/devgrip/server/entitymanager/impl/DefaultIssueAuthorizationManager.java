package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.IssueAuthorizationManager;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueAuthorization;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.List;

@Singleton
public class DefaultIssueAuthorizationManager extends BaseEntityManager<IssueAuthorization> 
		implements IssueAuthorizationManager {

	@Inject
	public DefaultIssueAuthorizationManager(Dao dao) {
		super(dao);
	}
	
	@Override
	public List<IssueAuthorization> query() {
		return query(true);
	}

	@Override
	public int count() {
		return count(true);
	}

	@Transactional
	@Override
	public void authorize(Issue issue, User user) {
		boolean authorized = false;
		for (IssueAuthorization authorization: issue.getAuthorizations()) {
			if (authorization.getUser().equals(user)) {
				authorized = true;
				break;
			}
		}
		if (!authorized) {
			IssueAuthorization authorization = new IssueAuthorization();
			authorization.setIssue(issue);
			authorization.setUser(user);
			issue.getAuthorizations().add(authorization);
			createOrUpdate(authorization);
		}
	}

	@Transactional
	@Override
	public void createOrUpdate(IssueAuthorization authorization) {
		dao.persist(authorization);
	}
	
}

package net.devgrip.server.entitymanager;

import java.util.Collection;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueSchedule;
import net.devgrip.server.model.Iteration;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueScheduleManager extends EntityManager<IssueSchedule> {
	
 	void syncIterations(Issue issue, Collection<Iteration> iterations);

    void create(IssueSchedule schedule);

    void populateSchedules(Collection<Issue> issues);
	
}

package net.devgrip.server.entitymanager;

import java.util.Date;
import java.util.Map;

import org.jetbrains.annotations.Nullable;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueStateHistory;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.util.ProjectScope;
import net.devgrip.server.util.criteria.Criteria;
import net.devgrip.server.web.util.StatsGroup;

public interface IssueStateHistoryManager extends EntityManager<IssueStateHistory> {
	
	Map<Integer, Map<String, Integer>> queryDurationStats(
			ProjectScope projectScope, @Nullable Criteria<Issue> issueFilter,  
			@Nullable Date startDate, @Nullable Date endDate, StatsGroup statsGroup);

	Map<Integer, Map<String, Integer>> queryFrequencyStats(
			ProjectScope projectScope, @Nullable Criteria<Issue> issueFilter, 
			@Nullable Date startDate, @Nullable Date endDate, StatsGroup statsGroup);
	
	Map<Integer, Map<String, Integer>> queryTrendStats(
			ProjectScope projectScope, @Nullable Criteria<Issue> issueFilter, 
			@Nullable Date startDate, @Nullable Date endDate, StatsGroup statsGroup);

}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueAuthorization;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueAuthorizationManager extends EntityManager<IssueAuthorization> {

	void authorize(Issue issue, User user);

    void createOrUpdate(IssueAuthorization authorization);
	
}

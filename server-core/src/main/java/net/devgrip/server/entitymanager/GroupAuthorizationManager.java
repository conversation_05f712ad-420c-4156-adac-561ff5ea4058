package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Group;
import net.devgrip.server.model.GroupAuthorization;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

public interface GroupAuthorizationManager extends EntityManager<GroupAuthorization> {

	void syncAuthorizations(Group group, Collection<GroupAuthorization> authorizations);

	void syncAuthorizations(Project project, Collection<GroupAuthorization> authorizations);
	
    void createOrUpdate(GroupAuthorization authorization);
	
}

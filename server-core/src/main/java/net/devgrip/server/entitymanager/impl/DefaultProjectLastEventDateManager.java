package net.devgrip.server.entitymanager.impl;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.ProjectLastEventDateManager;
import net.devgrip.server.event.Listen;
import net.devgrip.server.event.project.ProjectCreated;
import net.devgrip.server.event.project.ProjectEvent;
import net.devgrip.server.event.project.RefUpdated;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.ProjectLastEventDate;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.Date;

@Singleton
public class DefaultProjectLastEventDateManager extends BaseEntityManager<ProjectLastEventDate> implements ProjectLastEventDateManager {

	@Inject
	public DefaultProjectLastEventDateManager(Dao dao) {
		super(dao);
	}

	@Transactional
	@Listen
	public void on(ProjectEvent event) {
		Project project = event.getProject();
		if (event instanceof RefUpdated) {
			project.getLastEventDate().setActivity(new Date());
			project.getLastEventDate().setCommit(new Date());
		} else if (!(event instanceof ProjectCreated) 
				&& event.getUser() != null 
				&& !event.getUser().isSystem()) {
			project.getLastEventDate().setActivity(new Date());
		}
	}
	
	@Transactional
	@Override
	public void create(ProjectLastEventDate lastEventDate) {
		Preconditions.checkState(lastEventDate.isNew());
		dao.persist(lastEventDate);
	}
	
}

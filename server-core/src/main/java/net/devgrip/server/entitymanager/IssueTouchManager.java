package net.devgrip.server.entitymanager;

import net.devgrip.server.model.IssueTouch;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;
import java.util.List;

public interface IssueTouchManager extends EntityManager<IssueTouch> {
	
	void touch(Project project, Collection<Long> issueIds, boolean newIssues);
	
	List<IssueTouch> queryTouchesAfter(Long projectId, Long afterTouchId, int count);
	
}

package net.devgrip.server.entitymanager;

import java.util.Collection;

import javax.annotation.Nullable;

import net.devgrip.server.model.CodeCommentStatusChange;
import net.devgrip.server.persistence.dao.EntityManager;

public interface CodeCommentStatusChangeManager extends EntityManager<CodeCommentStatusChange> {

	void create(CodeCommentStatusChange change, @Nullable String note);
	
	void create(Collection<CodeCommentStatusChange> changes, @Nullable String note);
	
}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.IssueQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.util.ProjectScope;

import java.util.Collection;

public interface IssueQueryPersonalizationManager extends EntityManager<IssueQueryPersonalization> {
	
	IssueQueryPersonalization find(Project project, User user);

    void createOrUpdate(IssueQueryPersonalization personalization);
	
	Collection<IssueQueryPersonalization> query(ProjectScope projectScope);
	
}

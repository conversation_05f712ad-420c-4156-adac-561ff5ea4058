package net.devgrip.server.entitymanager;

import net.devgrip.server.model.LinkSpec;
import net.devgrip.server.model.Role;
import net.devgrip.server.model.support.administration.GlobalIssueSettingConfig;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.web.component.issue.workflowreconcile.UndefinedFieldResolution;

import javax.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface RoleManager extends EntityManager<Role> {
	
	void replicate(Role role);
	
	void create(Role role, Collection<LinkSpec> authorizedLinks);
	
	void update(Role role, Collection<LinkSpec> authorizedLinks, @Nullable String oldName);
	
	@Nullable
	Role find(String name);
	
	Role getOwner();
	
	void setupDefaults(GlobalIssueSettingConfig roleConfig);
	
	Collection<String> getUndefinedIssueFields();
	
	void fixUndefinedIssueFields(Map<String, UndefinedFieldResolution> resolutions);
	
	List<Role> query(@Nullable String term, int firstResult, int maxResult);
	
	int count(@Nullable String term);
	
}

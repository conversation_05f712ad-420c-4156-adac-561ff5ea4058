package net.devgrip.server.entitymanager;

import java.util.Collection;

import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.model.UserAuthorization;
import net.devgrip.server.persistence.dao.EntityManager;

public interface UserAuthorizationManager extends EntityManager<UserAuthorization> {

	void syncAuthorizations(User user, Collection<UserAuthorization> authorizations);
	
	void syncAuthorizations(Project project, Collection<UserAuthorization> authorizations);
	
    void createOrUpdate(UserAuthorization authorization);
	
}

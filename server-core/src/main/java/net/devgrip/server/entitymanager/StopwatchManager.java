package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.Stopwatch;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;

public interface StopwatchManager extends EntityManager<Stopwatch> {
	
	@Nullable
	Stopwatch find(User user, Issue issue);
	
	Stopwatch startWork(User user, Issue issue);
	
	void stopWork(Stopwatch stopwatch);
	
}

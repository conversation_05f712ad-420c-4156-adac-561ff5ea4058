package net.devgrip.server.entitymanager.impl;

import java.util.Date;

import javax.inject.Inject;
import javax.inject.Singleton;

import com.google.common.base.Preconditions;
import net.devgrip.server.entitymanager.PullRequestAssignmentManager;
import net.devgrip.server.event.ListenerRegistry;
import net.devgrip.server.event.project.pullrequest.PullRequestAssigned;
import net.devgrip.server.event.project.pullrequest.PullRequestUnassigned;
import net.devgrip.server.model.PullRequestAssignment;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.security.SecurityUtils;

@Singleton
public class DefaultPullRequestAssignmentManager extends BaseEntityManager<PullRequestAssignment> 
		implements PullRequestAssignmentManager {

	private final ListenerRegistry listenerRegistry;
	
	@Inject
	public DefaultPullRequestAssignmentManager(Dao dao, ListenerRegistry listenerRegistry) {
		super(dao);
		this.listenerRegistry = listenerRegistry;
	}

	@Transactional
	@Override
	public void create(PullRequestAssignment assignment) {
		Preconditions.checkState(assignment.isNew());
		dao.persist(assignment);

		listenerRegistry.post(new PullRequestAssigned(
				SecurityUtils.getUser(), new Date(), 
				assignment.getRequest(), assignment.getUser()));
	}

	@Transactional
	@Override
	public void delete(PullRequestAssignment assignment) {
		super.delete(assignment);
		
		listenerRegistry.post(new PullRequestUnassigned(
				SecurityUtils.getUser(), new Date(), 
				assignment.getRequest(), assignment.getUser()));
	}
		
}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestChange;
import net.devgrip.server.model.support.pullrequest.AutoMerge;
import net.devgrip.server.model.support.pullrequest.MergeStrategy;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;

public interface PullRequestChangeManager extends EntityManager<PullRequestChange> {

	void create(PullRequestChange change, @Nullable String note);
	
	void changeMergeStrategy(PullRequest request, MergeStrategy mergeStrategy);
	
	void changeAutoMerge(PullRequest request, AutoMerge autoMerge);
	
	void changeTitle(PullRequest request, String title);

	void changeDescription(PullRequest request, @Nullable String description);
	
	void changeTargetBranch(PullRequest request, String targetBranch);
	
}

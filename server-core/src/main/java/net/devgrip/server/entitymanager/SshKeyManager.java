package net.devgrip.server.entitymanager;

import java.util.Collection;

import javax.annotation.Nullable;

import net.devgrip.server.model.SshKey;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface SshKeyManager extends EntityManager<SshKey> {

    @Nullable
    SshKey findByFingerprint(String fingerprint);
    
    void syncSshKeys(User user, Collection<String> sshKeys);

    void create(SshKey sshKey);
	
}

package net.devgrip.server.entitymanager;

import net.devgrip.server.model.PackQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PackQueryPersonalizationManager extends EntityManager<PackQueryPersonalization> {
	
	PackQueryPersonalization find(Project project, User user);

    void createOrUpdate(PackQueryPersonalization personalization);
	
}

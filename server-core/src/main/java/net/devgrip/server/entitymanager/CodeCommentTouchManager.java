package net.devgrip.server.entitymanager;

import java.util.List;

import net.devgrip.server.model.CodeCommentTouch;
import net.devgrip.server.model.Project;
import net.devgrip.server.persistence.dao.EntityManager;

public interface CodeCommentTouchManager extends EntityManager<CodeCommentTouch> {
	
	void touch(Project project, Long commentId, boolean newComment);
	
	List<CodeCommentTouch> queryTouchesAfter(Long projectId, Long afterTouchId, int count);
	
}

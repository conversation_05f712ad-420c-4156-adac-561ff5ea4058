package net.devgrip.server.entitymanager;

import net.devgrip.server.model.IssueReaction;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueReactionManager extends EntityManager<IssueReaction> {

    void create(IssueReaction reaction);
    
    void toggleEmoji(User user, Issue issue, String emoji);

}

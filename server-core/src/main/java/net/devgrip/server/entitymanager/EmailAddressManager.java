package net.devgrip.server.entitymanager;

import javax.annotation.Nullable;

import org.eclipse.jgit.lib.PersonIdent;

import net.devgrip.server.model.EmailAddress;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.util.facade.EmailAddressCache;
import net.devgrip.server.util.facade.EmailAddressFacade;

public interface EmailAddressManager extends EntityManager<EmailAddress> {

	@Nullable
	EmailAddress findByValue(String value);
	
	@Nullable
	EmailAddressFacade findFacadeByValue(String value);
	
	@Nullable 
	EmailAddressFacade findPrimaryFacade(Long userId);
	
	@Nullable
	EmailAddress findPrimary(User user);

	@Nullable
	EmailAddress findGit(User user);
	
	@Nullable
	EmailAddress findByPersonIdent(PersonIdent personIdent);
	
	void setAsPrimary(EmailAddress emailAddress);
	
	void useForGitOperations(EmailAddress emailAddress);
	
	void sendVerificationEmail(EmailAddress emailAddress);

	EmailAddressCache cloneCache();

    void create(EmailAddress address);

	void update(EmailAddress address);
	
}

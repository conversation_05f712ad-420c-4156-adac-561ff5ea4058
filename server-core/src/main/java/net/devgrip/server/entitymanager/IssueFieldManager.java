package net.devgrip.server.entitymanager;

import java.util.Collection;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueField;
import net.devgrip.server.persistence.dao.EntityManager;

public interface IssueFieldManager extends EntityManager<IssueField> {
	
	void saveFields(Issue issue);
	
	void onRenameUser(String oldName, String newName);

    void create(IssueField entity);

    void onRenameGroup(String oldName, String newName);
			
	void populateFields(Collection<Issue> issues);
	
}

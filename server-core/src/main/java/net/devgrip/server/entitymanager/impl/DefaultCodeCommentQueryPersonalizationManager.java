package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.CodeCommentQueryPersonalizationManager;
import net.devgrip.server.model.CodeCommentQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.persistence.dao.EntityCriteria;
import org.hibernate.criterion.Restrictions;

import javax.inject.Inject;
import javax.inject.Singleton;

@Singleton
public class DefaultCodeCommentQueryPersonalizationManager extends BaseEntityManager<CodeCommentQueryPersonalization> 
		implements CodeCommentQueryPersonalizationManager {

	@Inject
	public DefaultCodeCommentQueryPersonalizationManager(Dao dao) {
		super(dao);
	}

	@Sessional
	@Override
	public CodeCommentQueryPersonalization find(Project project, User user) {
		EntityCriteria<CodeCommentQueryPersonalization> criteria = newCriteria();
		criteria.add(Restrictions.and(Restrictions.eq("project", project), Restrictions.eq("user", user)));
		criteria.setCacheable(true);
		return find(criteria);
	}

	@Transactional
	@Override
	public void createOrUpdate(CodeCommentQueryPersonalization personalization) {
		if (personalization.getQueries().isEmpty()) {
			if (!personalization.isNew())
				delete(personalization);
		} else {
			dao.persist(personalization);
		}
	}
	
}

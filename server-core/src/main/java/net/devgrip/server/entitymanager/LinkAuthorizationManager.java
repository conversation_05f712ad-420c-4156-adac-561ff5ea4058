package net.devgrip.server.entitymanager;

import net.devgrip.server.model.LinkAuthorization;
import net.devgrip.server.model.LinkSpec;
import net.devgrip.server.model.Role;
import net.devgrip.server.persistence.dao.EntityManager;

import java.util.Collection;

public interface LinkAuthorizationManager extends EntityManager<LinkAuthorization> {

	void syncAuthorizations(Role role, Collection<LinkSpec> authorizedLinks);

    void create(LinkAuthorization authorization);
}

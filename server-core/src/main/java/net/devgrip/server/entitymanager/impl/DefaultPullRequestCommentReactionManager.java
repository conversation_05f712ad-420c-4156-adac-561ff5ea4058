package net.devgrip.server.entitymanager.impl;

import javax.inject.Inject;
import javax.inject.Singleton;

import com.google.common.base.Preconditions;

import net.devgrip.server.entitymanager.PullRequestCommentReactionManager;
import net.devgrip.server.model.PullRequestComment;
import net.devgrip.server.model.PullRequestCommentReaction;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;

@Singleton
public class DefaultPullRequestCommentReactionManager extends BaseEntityManager<PullRequestCommentReaction> 
        implements PullRequestCommentReactionManager {

    @Inject
    public DefaultPullRequestCommentReactionManager(Dao dao) {
        super(dao);
    }
    
    @Transactional
    @Override
    public void create(PullRequestCommentReaction reaction) {
        Preconditions.checkState(reaction.isNew());
        dao.persist(reaction);
    }

    @Transactional
    @Override
    public void toggleEmoji(User user, PullRequestComment comment, String emoji) {
        var reaction = comment.getReactions().stream()
                .filter(r -> r.getUser().equals(user) && r.getEmoji().equals(emoji))
                .findFirst()
                .orElse(null);
        if (reaction == null) {
            reaction = new PullRequestCommentReaction();
            reaction.setUser(user);
            reaction.setComment(comment);
            reaction.setEmoji(emoji);
            create(reaction);
            comment.getReactions().add(reaction);
        } else {
            comment.getReactions().remove(reaction);
            dao.remove(reaction);
        }
    }

} 

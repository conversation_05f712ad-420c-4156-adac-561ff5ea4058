package net.devgrip.server.entitymanager;

import java.util.List;

import net.devgrip.server.model.PullRequest;
import net.devgrip.server.model.PullRequestUpdate;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PullRequestUpdateManager extends EntityManager<PullRequestUpdate> {
	
	void checkUpdate(PullRequest request);
	
	List<PullRequestUpdate> queryAfter(Long projectId, Long afterUpdateId, int count);

    void create(PullRequestUpdate update);
	
}

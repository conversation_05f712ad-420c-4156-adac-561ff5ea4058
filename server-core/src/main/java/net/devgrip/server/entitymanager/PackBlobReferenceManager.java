package net.devgrip.server.entitymanager;

import net.devgrip.server.model.Pack;
import net.devgrip.server.model.PackBlob;
import net.devgrip.server.model.PackBlobReference;
import net.devgrip.server.persistence.dao.EntityManager;

public interface PackBlobReferenceManager extends EntityManager<PackBlobReference> {

	void create(PackBlobReference blobReference);
	
	void createIfNotExist(Pack pack, PackBlob packBlob);
		
}

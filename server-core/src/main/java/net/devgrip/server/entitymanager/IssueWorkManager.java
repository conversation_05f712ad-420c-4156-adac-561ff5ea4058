package net.devgrip.server.entitymanager;

import java.util.Date;
import java.util.List;

import javax.annotation.Nullable;

import net.devgrip.server.model.Issue;
import net.devgrip.server.model.IssueWork;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;
import net.devgrip.server.search.entity.EntityQuery;
import net.devgrip.server.util.ProjectScope;

public interface IssueWorkManager extends EntityManager<IssueWork> {
	
	void createOrUpdate(IssueWork work);
	
	List<IssueWork> query(User user, Issue issue, Date fromDate, Date toDate);
	
	List<IssueWork> query(@Nullable ProjectScope projectScope, EntityQuery<Issue> issueQuery, Date fromDate, Date toDate);
	
}

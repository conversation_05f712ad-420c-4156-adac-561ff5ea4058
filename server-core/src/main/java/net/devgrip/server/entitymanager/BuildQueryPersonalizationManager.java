package net.devgrip.server.entitymanager;

import net.devgrip.server.model.BuildQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface BuildQueryPersonalizationManager extends EntityManager<BuildQueryPersonalization> {
	
	BuildQueryPersonalization find(Project project, User user);

    void createOrUpdate(BuildQueryPersonalization buildQueryPersonalization);
	
}

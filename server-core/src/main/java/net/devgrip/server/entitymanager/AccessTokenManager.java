package net.devgrip.server.entitymanager;

import net.devgrip.server.model.AccessToken;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

import javax.annotation.Nullable;

public interface AccessTokenManager extends EntityManager<AccessToken> {

	@Nullable
	AccessToken findByOwnerAndName(User owner, String name);
	
	@Nullable
    AccessToken findByValue(String value);

	void createOrUpdate(AccessToken projectToken);

	String createTemporal(Long userId, long secondsToExpire);
	
}

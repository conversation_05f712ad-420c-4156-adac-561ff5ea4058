package net.devgrip.server.entitymanager;

import net.devgrip.server.model.CodeCommentQueryPersonalization;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.dao.EntityManager;

public interface CodeCommentQueryPersonalizationManager extends EntityManager<CodeCommentQueryPersonalization> {
	
	CodeCommentQueryPersonalization find(Project project, User user);

    void createOrUpdate(CodeCommentQueryPersonalization personalization);

}

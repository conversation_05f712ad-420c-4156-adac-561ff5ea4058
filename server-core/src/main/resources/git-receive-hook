#!/usr/bin/env bash
unset http_proxy
unset https_proxy
IFS=$'\r\n'; 
lines=($(${DEVGRIP_CURL} -k -s -S -f -X POST --data-urlencode "ENV_GIT_ALTERNATE_OBJECT_DIRECTORIES=${GIT_ALTERNATE_OBJECT_DIRECTORIES}" --data-urlencode "ENV_GIT_OBJECT_DIRECTORY=${GIT_OBJECT_DIRECTORY}" --data-urlencode "ENV_GIT_QUARANTINE_PATH=${GIT_QUARANTINE_PATH}" -d @- ${DEVGRIP_URL}/%s/${DEVGRIP_REPOSITORY_ID}/${DEVGRIP_USER_ID}/${DEVGRIP_HOOK_TOKEN} 2>&1))

returnCode=0;

for i in ${lines[@]}
do
  if [ "$i" = "ERROR" ]; then 
    returnCode=1
  else
    if [[ $i == curl:* ]]; then
      returnCode=1
    fi;
    echo "$i"
  fi;
done

exit $returnCode

AccessTokenEditBean.Authorizations.message=少なくとも1つ選択してください
MayNotBeEmpty.message=空であってはなりません
MustNotBeEmpty.message=空にすることはできません
GroovyScript.Name.message=名前は'builtin:'で始まってはいけません
Authenticator.UserSearchBases.message=少なくとも1つ指定してください
LdapAuthenticator.gr.message=空であってはなりません
MailService.timeout.message=5未満にすることはできません
InboxPollSetting.pollInterval.message=10未満にすることはできません
AtLeastOneEntryShouldBeSpecified=少なくとも1つ選択してください
ChooseAColor=色を選択してください
NeedTwoColums=少なくとも2つの値を選択してください
AtLeastOneStateNeedToSpecified=少なくとも1つの状態を選択してください
AtLeastOneValueNeedToSpecified=少なくとも1つの値を選択してください
AtLeastOneChoiceNeedToSpecified=少なくとも1つ選択してください
ChooseRevision=まずブランチを作成するための基準バージョンを選択してください
ChooseRevisionForCreateTag=まずgitタグを作成するための基準バージョンを選択してください
AtLeastOneRepoShoudBeSelected=少なくとも1つのリポジトリを選択してください
AtLeastOneProjectShoudBeSelected=少なくとも1つのプロジェクトを選択してください
JobSecret.name.message=名前に'@'を含めることはできません
JobProperty.name.message=名前に'@'を含めることはできません
WebHook.eventType.message=少なくとも1つのイベントタイプを選択してください
ShouldNotBeLessThanOne=この値は1未満にできません
AtLeastOneOptionNeedsToBeSelected=少なくとも1つのオプションを選択する必要があります
Project.serviceDescEmailAddress.message=サービスデスクメールアドレスの名前部分に'~'を含めることはできません
Service.runAs.message=<uid>:<gid>の形式で指定してください
OnlyHttpOrHttpsProtocolIsSupported=http/httpsプロトコルのみサポートされています
Reserved.message=システムで予約されています
StartAndEndWithAlphanumericOrNnderscore=字母、数字、またはアンダースコアで始まり終わり、中間には字母、数字、アンダースコア(_)、ハイフン(-)、スペース、ドット(.)のみ使用可能です
TimesheetEditBean.nameAlreadyUsed.message=この名前はすでに使用されています
TrivyScanStep.detectOption.message=少なくとも1つの検出項目を指定してください
AtLeastOneStateShouldBeSpecified=少なくとも1つの状態を指定してください
MalformedQuery.message=クエリの形式が間違っています
StartAndEndWithAlphanumericOrNnderscoreButNoSpaceInMiddle=字母、数字、またはアンダースコアで始まり終わり、中間には字母、数字、アンダースコア(_)、ハイフン(-)、ドット(.)のみ使用可能です
ProjectPath.message=字母、数字、またはアンダースコアで始まり終わり、中間にはスラッシュ(/)、字母、数字、アンダースコア(_)、ハイフン(-)、ドット(.)のみ使用可能です
InvalidGitBranchName=無効なgitブランチ名
StartWithAlphanumericOrNnderscore=字母、数字、またはアンダースコアで始まり、字母、数字、アンダースコア(_)、ハイフン(-)、ドット(.)のみ含む必要があります
ShouldBeTwoOrMoreUppercaseLetters=2つ以上の大文字である必要があります
MalformedPatternSet.message=パターン文字列の形式が間違っています
MalformedGroovyTemplate.message=groovyテンプレートの形式が間違っています
FailedCompileGroovyScript.message=groovyスクリプトのコンパイルに失敗しました
NotAValidCommitHash=無効なコミットハッシュ
InvalidCronExpression=無効なcron式
CurrentPasswordDoesNotMatch=パスワードが正しくありません
DirectoryNotExist=ディレクトリが存在しません
SpecifiedDirectoryShouldUnderSite=指定されたディレクトリはサイトディレクトリの下かインストールディレクトリの外にある必要があります
InvalidDirectory=無効なディレクトリ
DnsName.message=字母、数字、またはハイフン(-)のみを含み、字母または数字で始まり終わる必要があります
EvnName.message=名前は字母で始まり、字母、数字、アンダースコアのみ含む必要があります
Interpolative.message=最後の@は無効です。@...@で変数を参照するか、@@で元の@をエスケープしてください
MalformedJobMatch=ビルドタスクのマッチング形式が間違っています
SpaceNotAllowed=スペースは使用できません
MalformedNotificationReceiver=通知受信者の形式が間違っています
InvalidNumber=無効な数字
SlashAndBackSlashNotAllowed=スラッシュとバックスラッシュは使用できません
NotMatchingRegularExpression=指定された正規表現と一致しません
Option.message=オプション
MalformedReviewRequirement=レビュアーの形式が間違っています
RoleName.message=役割名に'[', ']', '<', '>', '{', '}'を含めることはできません
AbsolutePathNotAllowed=ここでは絶対パスは使用できません
IsDisallowed.message=禁止されています
InvalidTagName=無効なgitタグ名
MalformedUserMatch=ユーザーマッチングの形式が間違っています
TextInput.max.message=テキストが長すぎます。最大500文字
TextInput.pattern.message=正規表現に一致する必要があります:
AtLeastOneIterationNeedsToBeSelected=少なくとも1つのイテレーションを選択してください
NoPermissionToImportAsRootProjects=ルートプロジェクトとしてインポートする権限がありません。このプロジェクトの親プロジェクトを指定してください
UrlMustBeginWithHttpOrHttps=正しいURLはhttpまたはhttpsで始まる必要があります
DuplicateWebhookUrl=重複したwebhook URL
ShouldBeStartWithSEC=钉钉通知の署名キーはSECで始まる必要があります
InvalidGpgPublicKey.message=無効なGPG公開鍵
InvalidCronExpression.message=cron式の形式が間違っています
ImportRepositories.noPermission.importRootProject=ルートプロジェクトをインポートする権限がありません。親プロジェクトを指定してください
AtLeastOneRoleIsRequired=少なくとも1つのロールを選択してください
MalformedRegularExpression=不正な正規表現
TheProvidedKeyIsNotValid=入力されたキーは無効です。ご確認のうえ、再度お試しください。

package net.devgrip.server.web.behavior.describer;

import net.devgrip.server.web.behavior.describer.ProjectQueryDescriber;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class ProjectQueryDescriberTest {

    @Test
    public void testGetValue() {
        assertEquals("owned by", ProjectQueryDescriber.D_OWNED_BY.getValue());
        assertEquals("owned by me", ProjectQueryDescriber.D_OWNED_BY_ME.getValue());
        assertEquals("owned by none", ProjectQueryDescriber.D_OWNED_BY_NONE.getValue());
        assertEquals("forks of", ProjectQueryDescriber.D_FORKS_OF.getValue());
        assertEquals("roots", ProjectQueryDescriber.D_ROOTS.getValue());
        assertEquals("leafs", ProjectQueryDescriber.D_LEAFS.getValue());
        assertEquals("fork roots", ProjectQueryDescriber.D_FORK_ROOTS.getValue());
        assertEquals("without enough replicas", ProjectQueryDescriber.D_WITHOUT_ENOUGH_REPLICAS.getValue());
        assertEquals("has outdated replicas", ProjectQueryDescriber.D_HAS_OUTDATED_REPLICAS.getValue());
        assertEquals("missing storage", ProjectQueryDescriber.D_MISSING_STORAGE.getValue());
        assertEquals("pending delete", ProjectQueryDescriber.D_PENDING_DELETE.getValue());
        assertEquals("children of", ProjectQueryDescriber.D_CHILDREN_OF.getValue());
        assertEquals("order by", ProjectQueryDescriber.OP_ORDER_BY.getValue());
        assertEquals("is", ProjectQueryDescriber.OP_IS.getValue());
        assertEquals("is not", ProjectQueryDescriber.OP_IS_NOT.getValue());
        assertEquals("contains", ProjectQueryDescriber.OP_CONTAINS.getValue());
        assertEquals("is greater than", ProjectQueryDescriber.OP_IS_GREATER_THAN.getValue());
        assertEquals("is less than", ProjectQueryDescriber.OP_IS_LESS_THAN.getValue());
        assertEquals("is since", ProjectQueryDescriber.OP_IS_SINCE.getValue());
        assertEquals("is until", ProjectQueryDescriber.OP_IS_UNTIL.getValue());
        assertEquals("and", ProjectQueryDescriber.OP_AND.getValue());
        assertEquals("or", ProjectQueryDescriber.OP_OR.getValue());
        assertEquals("not", ProjectQueryDescriber.OP_NOT.getValue());
        assertEquals("asc", ProjectQueryDescriber.OP_ASC.getValue());
        assertEquals("Name", ProjectQueryDescriber.F_NAME.getValue());
        assertEquals("Key", ProjectQueryDescriber.F_KEY.getValue());
        assertEquals("Path", ProjectQueryDescriber.F_PATH.getValue());
        assertEquals("Label", ProjectQueryDescriber.F_LABEL.getValue());
        assertEquals("Service Desk Email Address", ProjectQueryDescriber.F_SERVICE_DESK_EMAIL_ADDRESS.getValue());
        assertEquals("Id", ProjectQueryDescriber.F_ID.getValue());
        assertEquals("Description", ProjectQueryDescriber.F_DESCRIPTION.getValue());
        assertEquals("Last Activity Date", ProjectQueryDescriber.F_LAST_ACTIVITY_DATE.getValue());
        assertEquals("Last Commit Date", ProjectQueryDescriber.F_LAST_COMMIT_DATE.getValue());
    }

    @Test
    public void testGetDefaultDesc() {
        assertNull(ProjectQueryDescriber.D_OWNED_BY.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_OWNED_BY_ME.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_OWNED_BY_NONE.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_FORKS_OF.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_ROOTS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_LEAFS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_FORK_ROOTS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_WITHOUT_ENOUGH_REPLICAS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_HAS_OUTDATED_REPLICAS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_MISSING_STORAGE.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_PENDING_DELETE.getDefaultDesc());
        assertNull(ProjectQueryDescriber.D_CHILDREN_OF.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_ORDER_BY.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS_NOT.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_CONTAINS.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS_GREATER_THAN.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS_LESS_THAN.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS_SINCE.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_IS_UNTIL.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_AND.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_OR.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_NOT.getDefaultDesc());
        assertNull(ProjectQueryDescriber.OP_ASC.getDefaultDesc());
    }

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("ProjectQueryDescriber.ownedby", ProjectQueryDescriber.D_OWNED_BY.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.ownedByMe", ProjectQueryDescriber.D_OWNED_BY_ME.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.ownedByNone", ProjectQueryDescriber.D_OWNED_BY_NONE.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.forksOf", ProjectQueryDescriber.D_FORKS_OF.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.roots", ProjectQueryDescriber.D_ROOTS.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.leafs", ProjectQueryDescriber.D_LEAFS.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.forkRoots", ProjectQueryDescriber.D_FORK_ROOTS.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.withoutEnoughReplicas", ProjectQueryDescriber.D_WITHOUT_ENOUGH_REPLICAS.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.hasOutdatedReplicas", ProjectQueryDescriber.D_HAS_OUTDATED_REPLICAS.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.missingStorage", ProjectQueryDescriber.D_MISSING_STORAGE.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.pendingDelete", ProjectQueryDescriber.D_PENDING_DELETE.getDescriptionI18nKey());
        assertEquals("ProjectQueryDescriber.childrenOf", ProjectQueryDescriber.D_CHILDREN_OF.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.orderBy", ProjectQueryDescriber.OP_ORDER_BY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.is", ProjectQueryDescriber.OP_IS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNot", ProjectQueryDescriber.OP_IS_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.contains", ProjectQueryDescriber.OP_CONTAINS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isGreaterThan", ProjectQueryDescriber.OP_IS_GREATER_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isLessThan", ProjectQueryDescriber.OP_IS_LESS_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isSince", ProjectQueryDescriber.OP_IS_SINCE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isUntil", ProjectQueryDescriber.OP_IS_UNTIL.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.and", ProjectQueryDescriber.OP_AND.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.or", ProjectQueryDescriber.OP_OR.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.not", ProjectQueryDescriber.OP_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.asc", ProjectQueryDescriber.OP_ASC.getDescriptionI18nKey());
    }
}

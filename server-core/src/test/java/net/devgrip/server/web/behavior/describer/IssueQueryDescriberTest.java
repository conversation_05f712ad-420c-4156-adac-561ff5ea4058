package net.devgrip.server.web.behavior.describer;

import net.devgrip.server.web.behavior.describer.IssueQueryDescriber;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class IssueQueryDescriberTest {

    @Test
    public void testGetValue() {
        assertEquals("Number", IssueQueryDescriber.F_NUMER.getValue());
        assertEquals("State", IssueQueryDescriber.F_STATE.getValue());
        assertEquals("Title", IssueQueryDescriber.F_TITLE.getValue());
        assertEquals("Description", IssueQueryDescriber.F_DESCRIPTION.getValue());
        assertEquals("Estimated Time", IssueQueryDescriber.F_ESTIMATED_TIME.getValue());
        assertEquals("Spent Time", IssueQueryDescriber.F_SPENT_TIME.getValue());
        assertEquals("Progress", IssueQueryDescriber.F_PROGRESS.getValue());
        assertEquals("Comment", IssueQueryDescriber.F_COMMENT.getValue());
        assertEquals("Submit Date", IssueQueryDescriber.F_SUBMIT_DATE.getValue());
        assertEquals("Last Activity Date", IssueQueryDescriber.F_LAST_ACTIVITY_DATE.getValue());
        assertEquals("Vote Count", IssueQueryDescriber.F_VOTE_COUNT.getValue());
        assertEquals("Comment Count", IssueQueryDescriber.F_COMMENT_COUNT.getValue());
        assertEquals("Iteration", IssueQueryDescriber.F_ITERATION.getValue());
        assertEquals("Board Position", IssueQueryDescriber.F_BOARD_POSITION.getValue());
    }

    @Test
    public void testGetDefaultDesc() {
        assertNull(IssueQueryDescriber.F_NUMER.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_STATE.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_TITLE.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_DESCRIPTION.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_ESTIMATED_TIME.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_SPENT_TIME.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_PROGRESS.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_COMMENT.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_SUBMIT_DATE.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_LAST_ACTIVITY_DATE.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_VOTE_COUNT.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_COMMENT_COUNT.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_ITERATION.getDefaultDesc());
        assertNull(IssueQueryDescriber.F_BOARD_POSITION.getDefaultDesc());
    }

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("IssueQueryDescriber.number", IssueQueryDescriber.F_NUMER.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.state", IssueQueryDescriber.F_STATE.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.title", IssueQueryDescriber.F_TITLE.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.description", IssueQueryDescriber.F_DESCRIPTION.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.estimatedTime", IssueQueryDescriber.F_ESTIMATED_TIME.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.spentTime", IssueQueryDescriber.F_SPENT_TIME.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.progress", IssueQueryDescriber.F_PROGRESS.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.comment", IssueQueryDescriber.F_COMMENT.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.submitDate", IssueQueryDescriber.F_SUBMIT_DATE.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.lastActivityDate", IssueQueryDescriber.F_LAST_ACTIVITY_DATE.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.voteCount", IssueQueryDescriber.F_VOTE_COUNT.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.commentCount", IssueQueryDescriber.F_COMMENT_COUNT.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.iteration", IssueQueryDescriber.F_ITERATION.getDescriptionI18nKey());
        assertEquals("IssueQueryDescriber.boardPosition", IssueQueryDescriber.F_BOARD_POSITION.getDescriptionI18nKey());
    }
}

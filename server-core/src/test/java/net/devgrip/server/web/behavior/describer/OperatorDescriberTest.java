package net.devgrip.server.web.behavior.describer;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import net.devgrip.server.web.behavior.describer.OperatorDescriber;
import org.junit.Test;

public class OperatorDescriberTest {

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("OperatorDescriber.orderBy", OperatorDescriber.O_ORDER_BY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.is", OperatorDescriber.O_IS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNot", OperatorDescriber.O_IS_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.contains", OperatorDescriber.O_CONTAINS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isGreaterThan", OperatorDescriber.O_IS_GREATER_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isLessThan", OperatorDescriber.O_IS_LESS_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isSince", OperatorDescriber.O_IS_SINCE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isUntil", OperatorDescriber.O_IS_UNTIL.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.and", OperatorDescriber.O_AND.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.or", OperatorDescriber.O_OR.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.not", OperatorDescriber.O_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.any", OperatorDescriber.O_ANY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.hasAny", OperatorDescriber.O_HAS_ANY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.all", OperatorDescriber.O_ALL.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isMe", OperatorDescriber.O_IS_ME.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNotMe", OperatorDescriber.O_IS_NOT_ME.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isAfter", OperatorDescriber.O_IS_AFTER.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isBefore", OperatorDescriber.O_IS_BEFORE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isEmpty", OperatorDescriber.O_IS_EMPTY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNotEmpty", OperatorDescriber.O_IS_NOT_EMPTY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.matching", OperatorDescriber.O_MATCHING.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.asc", OperatorDescriber.O_ASC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.desc", OperatorDescriber.O_DESC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.reference", OperatorDescriber.O_REFERENCE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.fuzzy", OperatorDescriber.O_FUZZY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.space", OperatorDescriber.O_SPACE.getDescriptionI18nKey());
        assertEquals("OrMatchAnotherValue", OperatorDescriber.O_COMMA.getDescriptionI18nKey());
    }

    @Test
    public void testGetDefaultDesc() {
        assertNull(OperatorDescriber.O_ORDER_BY.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_NOT.getDefaultDesc());
        assertNull(OperatorDescriber.O_CONTAINS.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_GREATER_THAN.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_LESS_THAN.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_SINCE.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_UNTIL.getDefaultDesc());
        assertNull(OperatorDescriber.O_AND.getDefaultDesc());
        assertNull(OperatorDescriber.O_OR.getDefaultDesc());
        assertNull(OperatorDescriber.O_NOT.getDefaultDesc());
        assertNull(OperatorDescriber.O_ANY.getDefaultDesc());
        assertNull(OperatorDescriber.O_HAS_ANY.getDefaultDesc());
        assertNull(OperatorDescriber.O_ALL.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_ME.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_NOT_ME.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_AFTER.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_BEFORE.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_EMPTY.getDefaultDesc());
        assertNull(OperatorDescriber.O_IS_NOT_EMPTY.getDefaultDesc());
        assertNull(OperatorDescriber.O_MATCHING.getDefaultDesc());
        assertNull(OperatorDescriber.O_ASC.getDefaultDesc());
        assertNull(OperatorDescriber.O_DESC.getDefaultDesc());
        assertNull(OperatorDescriber.O_REFERENCE.getDefaultDesc());
        assertNull(OperatorDescriber.O_FUZZY.getDefaultDesc());
        assertEquals("space", OperatorDescriber.O_SPACE.getDefaultDesc());
        assertEquals("or match another value", OperatorDescriber.O_COMMA.getDefaultDesc());
    }
}

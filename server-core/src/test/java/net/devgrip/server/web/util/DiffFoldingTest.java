package net.devgrip.server.web.util;

import net.devgrip.server.web.util.DiffFolding;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 *
 */

public class DiffFoldingTest {

    // 测试文件：包括测试类文件
    @Test
    public void testTestFiles() {
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/java/com/example/MyServiceTest.java"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/service_spec.ts"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/__tests__/App.test.tsx"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("main_test.go"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("C:\\projects\\test\\MyServiceTest.java")); // Windows路径
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("D:/projects/test/SpecTest.rb")); // Windows路径
    }
    @Test
    public void testTestFilePattern() {
        // 后端语言测试文件
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/java/com/example/MyClassTest.java"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/kt/com/example/MyClassTest.kt"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/go/MyClass_test.go"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/ruby/my_class_test.rb"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/csharp/MyClassTest.cs"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/python/test_my_class.py"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/php/MyClassTest.php"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("codec_test.go"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("grpc_authz_end2end_test.go"));

        // 前端语言测试文件
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/js/MyComponent.test.js"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/ts/myComponent.spec.ts"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/tsx/MyComponent.test.tsx"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/jsx/MyComponent.test.jsx"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/vue/MyComponent.spec.vue"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/svelte/MyComponent.spec.svelte"));

        // 测试路径匹配
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/__tests__/MyClassTest.js"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/unittest/test_my_class.py"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/e2e/myClassTest.ts"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/test/java/com/example/MyClassTest.java"));

        //不匹配
        assertNotEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/spec/myClassSpec.ts"));
        // 不匹配普通文件
        assertNotEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/main/java/com/example/MyClass.java"));
        assertNotEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/main/go/MyClass.go"));
        assertNotEquals(DiffFolding.FoldingKind.TEST_FILE,DiffFolding.fold("src/main/js/MyClass.js"));
    }

    @Test
    public void testIsTestFilePerLanguage() {
        // Java
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/java/com/example/UserTest.java"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/java/com/example/UserTests.java"));

        // Kotlin
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/kotlin/com/example/UserTest.kt"));

        // JavaScript / TypeScript
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/__tests__/user.test.js"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/__tests__/user.spec.ts"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("user.test.tsx"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("user.spec.jsx"));

        // Python
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/test_utils.py"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/utils_test.py"));

        // Go
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("service/user_test.go"));

        // PHP
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/UserTest.php"));

        // C#
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("UserTests.cs"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("Tests/UserTests.cs"));

        // Ruby
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("foo_spec.rb"));

        // Rust
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/tests/test_user.rs"));

        // Dart
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("test/user_test.dart"));

        // Swift
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("UserTests.swift"));

        // Lua
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/user_test.lua"));

        // Scala
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/scala/UserSpec.scala"));

        // Objective-C
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("UserTests.m"));

        // VB
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("Tests/UserTests.vb"));

        // Pascal
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/user_test.pas"));

        // Matlab
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/test_user.m"));

        // R
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/testthat/test-user.R"));

        // C / C++
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/user_test.c"));
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/user_test.cpp"));

        // Shell
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("tests/test_install.sh"));
    }


    // 锁文件：包含多种包管理工具的锁文件
    @Test
    public void testLockFiles() {
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("package-lock.json"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("yarn.lock"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("composer.lock"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("Pipfile.lock"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("project.csproj"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("C:\\projects\\config\\Pipfile.lock")); // Windows路径
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("D:/projects/config/package-lock.json")); // Windows路径
    }

    // 自动生成文件：包括多种语言的自动生成代码文件
    @Test
    public void testGeneratedFiles() {
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("models.pb.go"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("schema.gen.ts"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("api/generated.swagger.json"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("__generated__/graphql.ts"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("types.d.ts"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("generated/ApiClient.cs"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("swagger.json"));
    }

    // 压缩或构建产物：检查生成的压缩文件和构建文件
    @Test
    public void testMinifiedOrDistFiles() {
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("dist/bundle.min.js"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("build/output.js.map"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("out/classes.dex"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("target/classes/com/Foo.class"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("__pycache__/main.cpython-38.pyc"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("C:\\projects\\build\\output.js.map")); // Windows路径
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("D:/projects/out/classes.dex")); // Windows路径
    }

    // 需要不折叠的文件：这些文件应保持不折叠状态
    @Test
    public void testDoNotFolding() {
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("src/main/java/com/example/Service.java"));
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("src/components/Button.tsx"));
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("app/controllers/home_controller.rb"));
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("src/main/java/com/example/Controller.java"));
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("src/components/Header.vue"));
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("C:\\projects\\main\\Controller.java")); // Windows路径
        assertEquals(DiffFolding.FoldingKind.DO_NOT_FOLDING, DiffFolding.fold("D:/projects/main/Button.tsx")); // Windows路径
    }

    // 测试路径为null的情况
    @Test
    public void testNullOrEmptyPathThrows() {
        try {
            DiffFolding.fold(null);
        } catch (IllegalArgumentException e) {
            assertEquals("path is null or empty", e.getMessage());
        }
        try {
            DiffFolding.fold("");
        } catch (IllegalArgumentException e) {
            assertEquals("path is null or empty", e.getMessage());
        }
        try {
            DiffFolding.fold("    ");
        } catch (IllegalArgumentException e) {
            assertEquals("path is null or empty", e.getMessage());
        }
    }

    // 测试文件名包含特殊字符
    @Test
    public void testSpecialCharactersInPath() {
        assertEquals(DiffFolding.FoldingKind.TEST_FILE, DiffFolding.fold("src/test/java/com/example/UnitTest#123.java"));
        assertEquals(DiffFolding.FoldingKind.LOCK_FILE, DiffFolding.fold("config/<EMAIL>"));
        assertEquals(DiffFolding.FoldingKind.GENERATED_FILE, DiffFolding.fold("api/generated/<EMAIL>"));
    }
}

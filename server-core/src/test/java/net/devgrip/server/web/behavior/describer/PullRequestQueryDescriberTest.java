package net.devgrip.server.web.behavior.describer;

import net.devgrip.server.web.behavior.describer.PullRequestQueryDescriber;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class PullRequestQueryDescriberTest {

    @Test
    public void testGetValue() {
        assertEquals("order by", PullRequestQueryDescriber.OP_ORDER_BY.getValue());
        assertEquals("is", PullRequestQueryDescriber.OP_IS.getValue());
        assertEquals("is not", PullRequestQueryDescriber.OP_IS_NOT.getValue());
        assertEquals("contains", PullRequestQueryDescriber.OP_CONTAINS.getValue());
        assertEquals("is greater than", PullRequestQueryDescriber.OP_IS_GREATER_THAN.getValue());
        assertEquals("is less than", PullRequestQueryDescriber.OP_IS_LESS_THAN.getValue());
        assertEquals("is since", PullRequestQueryDescriber.OP_IS_SINCE.getValue());
        assertEquals("is until", PullRequestQueryDescriber.OP_IS_UNTIL.getValue());
        assertEquals("and", PullRequestQueryDescriber.OP_AND.getValue());
        assertEquals("or", PullRequestQueryDescriber.OP_OR.getValue());
        assertEquals("not", PullRequestQueryDescriber.OP_NOT.getValue());
        assertEquals("asc", PullRequestQueryDescriber.OP_ASC.getValue());
        assertEquals("desc", PullRequestQueryDescriber.OP_DESC.getValue());
        assertEquals("reference", PullRequestQueryDescriber.OP_REFERENCE.getValue());
        assertEquals("~", PullRequestQueryDescriber.OP_FUZZY.getValue());
        assertEquals(" ", PullRequestQueryDescriber.OP_SPACE.getValue());
        assertEquals(",", PullRequestQueryDescriber.OP_COMMA.getValue());
        assertEquals("Number", PullRequestQueryDescriber.F_NUMBER.getValue());
        assertEquals("Status", PullRequestQueryDescriber.F_STATUS.getValue());
        assertEquals("Title", PullRequestQueryDescriber.F_TITLE.getValue());
        assertEquals("Target Project", PullRequestQueryDescriber.F_TARGET_PROJECT.getValue());
        assertEquals("Target Branch", PullRequestQueryDescriber.F_TARGET_BRANCH.getValue());
        assertEquals("Source Project", PullRequestQueryDescriber.F_SOURCE_PROJECT.getValue());
        assertEquals("Source Branch", PullRequestQueryDescriber.F_SOURCE_BRANCH.getValue());
        assertEquals("Label", PullRequestQueryDescriber.F_LABEL.getValue());
        assertEquals("Description", PullRequestQueryDescriber.F_DESCRIPTION.getValue());
        assertEquals("Comment", PullRequestQueryDescriber.F_COMMENT.getValue());
        assertEquals("Submit Date", PullRequestQueryDescriber.F_SUBMIT_DATE.getValue());
        assertEquals("Last Activity Date", PullRequestQueryDescriber.F_LAST_ACTIVITY_DATE.getValue());
        assertEquals("Close Date", PullRequestQueryDescriber.F_CLOSE_DATE.getValue());
        assertEquals("Merge Strategy", PullRequestQueryDescriber.F_MERGE_STRATEGY.getValue());
        assertEquals("Comment Count", PullRequestQueryDescriber.F_COMMENT_COUNT.getValue());
    }

    @Test
    public void testGetDefaultDesc() {
        assertNull(PullRequestQueryDescriber.OP_ORDER_BY.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS_NOT.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_CONTAINS.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS_GREATER_THAN.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS_LESS_THAN.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS_SINCE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_IS_UNTIL.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_AND.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_OR.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_NOT.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_ASC.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_DESC.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_REFERENCE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.OP_FUZZY.getDefaultDesc());
        assertEquals("space", PullRequestQueryDescriber.OP_SPACE.getDefaultDesc());
        assertEquals("or match another value", PullRequestQueryDescriber.OP_COMMA.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_NUMBER.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_STATUS.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_TITLE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_TARGET_PROJECT.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_TARGET_BRANCH.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_SOURCE_PROJECT.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_SOURCE_BRANCH.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_LABEL.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_DESCRIPTION.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_COMMENT.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_SUBMIT_DATE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_LAST_ACTIVITY_DATE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_CLOSE_DATE.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_MERGE_STRATEGY.getDefaultDesc());
        assertNull(PullRequestQueryDescriber.F_COMMENT_COUNT.getDefaultDesc());
    }

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("OperatorDescriber.orderBy", PullRequestQueryDescriber.OP_ORDER_BY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.is", PullRequestQueryDescriber.OP_IS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNot", PullRequestQueryDescriber.OP_IS_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.contains", PullRequestQueryDescriber.OP_CONTAINS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isGreaterThan", PullRequestQueryDescriber.OP_IS_GREATER_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isLessThan", PullRequestQueryDescriber.OP_IS_LESS_THAN.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isSince", PullRequestQueryDescriber.OP_IS_SINCE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isUntil", PullRequestQueryDescriber.OP_IS_UNTIL.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.and", PullRequestQueryDescriber.OP_AND.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.or", PullRequestQueryDescriber.OP_OR.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.not", PullRequestQueryDescriber.OP_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.asc", PullRequestQueryDescriber.OP_ASC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.desc", PullRequestQueryDescriber.OP_DESC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.reference", PullRequestQueryDescriber.OP_REFERENCE.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.fuzzy", PullRequestQueryDescriber.OP_FUZZY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.space", PullRequestQueryDescriber.OP_SPACE.getDescriptionI18nKey());
        assertEquals("OrMatchAnotherValue", PullRequestQueryDescriber.OP_COMMA.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.number", PullRequestQueryDescriber.F_NUMBER.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.status", PullRequestQueryDescriber.F_STATUS.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.title", PullRequestQueryDescriber.F_TITLE.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.targetProject", PullRequestQueryDescriber.F_TARGET_PROJECT.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.targetBranch", PullRequestQueryDescriber.F_TARGET_BRANCH.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.sourceProject", PullRequestQueryDescriber.F_SOURCE_PROJECT.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.sourceBranch", PullRequestQueryDescriber.F_SOURCE_BRANCH.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.label", PullRequestQueryDescriber.F_LABEL.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.description", PullRequestQueryDescriber.F_DESCRIPTION.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.comment", PullRequestQueryDescriber.F_COMMENT.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.submitDate", PullRequestQueryDescriber.F_SUBMIT_DATE.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.lastActivityDate", PullRequestQueryDescriber.F_LAST_ACTIVITY_DATE.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.closeDate", PullRequestQueryDescriber.F_CLOSE_DATE.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.mergeStrategy", PullRequestQueryDescriber.F_MERGE_STRATEGY.getDescriptionI18nKey());
        assertEquals("PullRequestQueryDescriber.field.commentCount", PullRequestQueryDescriber.F_COMMENT_COUNT.getDescriptionI18nKey());
    }
}

package net.devgrip.server.web.behavior.describer;

import net.devgrip.server.web.behavior.describer.AgentQueryDescriber;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 *
 */

public class AgentQueryDescriberTest {
    @Test
    public void testGetValue() {
        assertEquals("online", AgentQueryDescriber.D_ONLINE.getValue());
        assertEquals("offline", AgentQueryDescriber.D_OFFLINE.getValue());
        assertEquals("paused", AgentQueryDescriber.D_PAUSED.getValue());
        assertEquals("has running builds", AgentQueryDescriber.D_HAS_RUNNING_BUILDS.getValue());
        assertEquals("has attribute", AgentQueryDescriber.D_HAS_ATTRIBUTE.getValue());
        assertEquals("not used since", AgentQueryDescriber.D_NOT_USED_SINCE.getValue());
        assertEquals("ever used since", AgentQueryDescriber.D_EVER_USED_SINCE.getValue());
        assertEquals("selected by executor", AgentQueryDescriber.D_SELECTED_BY_EXECUTOR.getValue());
        assertEquals("ran build", AgentQueryDescriber.D_RAN_BUILD.getValue());
        assertEquals("order by", AgentQueryDescriber.OP_ORDER_BY.getValue());
        assertEquals("is", AgentQueryDescriber.OP_IS.getValue());
        assertEquals("is not", AgentQueryDescriber.OP_IS_NOT.getValue());
        assertEquals("and", AgentQueryDescriber.OP_AND.getValue());
        assertEquals("or", AgentQueryDescriber.OP_OR.getValue());
        assertEquals("not", AgentQueryDescriber.OP_NOT.getValue());
        assertEquals("asc", AgentQueryDescriber.OP_ASC.getValue());
        assertEquals("desc", AgentQueryDescriber.OP_DESC.getValue());
        assertEquals("~", AgentQueryDescriber.OP_FUZZY.getValue());
        assertEquals(" ", AgentQueryDescriber.OP_SPACE.getValue());
        assertEquals(",", AgentQueryDescriber.OP_COMMA.getValue());
        assertEquals("Name", AgentQueryDescriber.F_NAME.getValue());
        assertEquals("Ip Address", AgentQueryDescriber.F_IP_ADDRESS.getValue());
        assertEquals("Os", AgentQueryDescriber.F_OS_NAME.getValue());
        assertEquals("Os Version", AgentQueryDescriber.F_OS_VERSION.getValue());
        assertEquals("Os Arch", AgentQueryDescriber.F_OS_ARCH.getValue());
        assertEquals("Last Used Date", AgentQueryDescriber.F_LAST_USED_DATE.getValue());
    }

    @Test
    public void testGetDefaultDesc() {
        assertNull(AgentQueryDescriber.D_ONLINE.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_OFFLINE.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_PAUSED.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_HAS_RUNNING_BUILDS.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_HAS_ATTRIBUTE.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_NOT_USED_SINCE.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_EVER_USED_SINCE.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_SELECTED_BY_EXECUTOR.getDefaultDesc());
        assertNull(AgentQueryDescriber.D_RAN_BUILD.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_ORDER_BY.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_IS.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_IS_NOT.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_AND.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_OR.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_NOT.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_ASC.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_DESC.getDefaultDesc());
        assertNull(AgentQueryDescriber.OP_FUZZY.getDefaultDesc());
        assertEquals("space", AgentQueryDescriber.OP_SPACE.getDefaultDesc());
        assertEquals("or match another value", AgentQueryDescriber.OP_COMMA.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_NAME.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_IP_ADDRESS.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_OS_NAME.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_OS_VERSION.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_OS_ARCH.getDefaultDesc());
        assertNull(AgentQueryDescriber.F_LAST_USED_DATE.getDefaultDesc());
    }

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("AgentQueryDescriber.online", AgentQueryDescriber.D_ONLINE.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.offline", AgentQueryDescriber.D_OFFLINE.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.paused", AgentQueryDescriber.D_PAUSED.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.hasRunningBuilds", AgentQueryDescriber.D_HAS_RUNNING_BUILDS.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.hasAttribute", AgentQueryDescriber.D_HAS_ATTRIBUTE.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.notUsedSince", AgentQueryDescriber.D_NOT_USED_SINCE.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.everUsedSince", AgentQueryDescriber.D_EVER_USED_SINCE.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.selectedByExecutor", AgentQueryDescriber.D_SELECTED_BY_EXECUTOR.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.ranBuild", AgentQueryDescriber.D_RAN_BUILD.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.orderBy", AgentQueryDescriber.OP_ORDER_BY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.is", AgentQueryDescriber.OP_IS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNot", AgentQueryDescriber.OP_IS_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.and", AgentQueryDescriber.OP_AND.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.or", AgentQueryDescriber.OP_OR.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.not", AgentQueryDescriber.OP_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.asc", AgentQueryDescriber.OP_ASC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.desc", AgentQueryDescriber.OP_DESC.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.fuzzy", AgentQueryDescriber.OP_FUZZY.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.space", AgentQueryDescriber.OP_SPACE.getDescriptionI18nKey());
        assertEquals("OrMatchAnotherValue", AgentQueryDescriber.OP_COMMA.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.name", AgentQueryDescriber.F_NAME.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.ipAddress", AgentQueryDescriber.F_IP_ADDRESS.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.osName", AgentQueryDescriber.F_OS_NAME.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.osVersion", AgentQueryDescriber.F_OS_VERSION.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.osArch", AgentQueryDescriber.F_OS_ARCH.getDescriptionI18nKey());
        assertEquals("AgentQueryDescriber.field.lastUsedDate", AgentQueryDescriber.F_LAST_USED_DATE.getDescriptionI18nKey());
    }

}

package net.devgrip.server.web.behavior.describer;

import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class CommandPaletteDescriberTest {

    @Test
    public void testAntMatch() {
        String url1 = "/~boards";
        String url2 = "~boards";
        String url3 = "/project/path/~boards";

        String pattern1 = "/~boards";
        String pattern2 = "^(?!/~boards$).*~boards$";

        assertTrue(url1.matches(pattern1));
        assertFalse(url1.matches(pattern2));
        assertTrue(url2.matches(pattern2));
        assertTrue(url3.matches(pattern2));

    }


}

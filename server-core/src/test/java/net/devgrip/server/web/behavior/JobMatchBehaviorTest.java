package net.devgrip.server.web.behavior;

import net.devgrip.server.web.behavior.describer.JobMatchDescriber;
import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

/**
 *
 */

public class JobMatchBehaviorTest {
    @Test
    public void testGetValue() {
        assertEquals("on branch", JobMatchDescriber.D_ON_BRANCH.getValue());
        assertEquals("submitted by user", JobMatchDescriber.D_SUBMITTED_BY_USER.getValue());
        assertEquals("submitted by group", JobMatchDescriber.D_SUBMITTED_BY_GROUP.getValue());
        assertEquals("not", JobMatchDescriber.OP_NOT.getValue());
        assertEquals("is", JobMatchDescriber.OP_IS.getValue());
        assertEquals("is not", JobMatchDescriber.OP_IS_NOT.getValue());
        assertEquals("and", JobMatchDescriber.OP_AND.getValue());
        assertEquals("or", JobMatchDescriber.OP_OR.getValue());
    }

    @Test
    public void testGetDefaultDesc() {
        assertEquals("the target branch where the build commit was merged", JobMatchDescriber.D_ON_BRANCH.getDefaultDesc());
        assertNull(JobMatchDescriber.D_SUBMITTED_BY_USER.getDefaultDesc());
        assertNull(JobMatchDescriber.D_SUBMITTED_BY_GROUP.getDefaultDesc());
        assertNull(JobMatchDescriber.OP_NOT.getDefaultDesc());
        assertNull(JobMatchDescriber.OP_IS.getDefaultDesc());
        assertNull(JobMatchDescriber.OP_IS_NOT.getDefaultDesc());
        assertNull(JobMatchDescriber.OP_AND.getDefaultDesc());
        assertNull(JobMatchDescriber.OP_OR.getDefaultDesc());
    }

    @Test
    public void testGetDescriptionI18nKey() {
        assertEquals("JobMatchDescriber.onBranch", JobMatchDescriber.D_ON_BRANCH.getDescriptionI18nKey());
        assertEquals("JobMatchDescriber.submittedByUser", JobMatchDescriber.D_SUBMITTED_BY_USER.getDescriptionI18nKey());
        assertEquals("JobMatchDescriber.submittedByGroup", JobMatchDescriber.D_SUBMITTED_BY_GROUP.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.not", JobMatchDescriber.OP_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.is", JobMatchDescriber.OP_IS.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.isNot", JobMatchDescriber.OP_IS_NOT.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.and", JobMatchDescriber.OP_AND.getDescriptionI18nKey());
        assertEquals("OperatorDescriber.or", JobMatchDescriber.OP_OR.getDescriptionI18nKey());
    }

}

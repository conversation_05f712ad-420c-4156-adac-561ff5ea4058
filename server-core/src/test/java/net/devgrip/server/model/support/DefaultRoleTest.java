package net.devgrip.server.model.support;

import net.devgrip.server.model.support.DefaultRole;
import org.junit.Test;

import java.util.Optional;

import static org.junit.Assert.*;

/**
 *
 */

public class DefaultRoleTest {

    @Test
    public void testNameAndI18nKey() {

        assertEquals("Project Owner", DefaultRole.ProjectOwner.getName());
        assertEquals("RoleProjectOwner", DefaultRole.ProjectOwner.getI18nKey());
        assertEquals("Code Writer", DefaultRole.CodeWriter.getName());
        assertEquals("RoleCodeWriter", DefaultRole.CodeWriter.getI18nKey());
        assertEquals("Code Reader", DefaultRole.CodeReader.getName());
        assertEquals("RoleCodeReader", DefaultRole.CodeReader.getI18nKey());
        assertEquals("Package Writer", DefaultRole.PackageWriter.getName());
        assertEquals("RolePackageWriter", DefaultRole.PackageWriter.getI18nKey());
        assertEquals("Package Reader", DefaultRole.PackageReader.getName());
        assertEquals("RolePackageReader", DefaultRole.PackageReader.getI18nKey());
        assertEquals("Issue Reporter", DefaultRole.IssueReporter.getName());
        assertEquals("RoleIssueReporter", DefaultRole.IssueReporter.getI18nKey());
        assertEquals("Issue Manager", DefaultRole.IssueManager.getName());
        assertEquals("RoleIssueManager", DefaultRole.IssueManager.getI18nKey());

    }

    @Test
    public void testOf() {
        Optional<DefaultRole> projectOwner = DefaultRole.of("Project Owner");
        assertTrue("Project Owner", projectOwner.isPresent());
        assertEquals("Project Owner", projectOwner.get().getName());
        assertEquals("RoleProjectOwner", DefaultRole.of("Project Owner").get().getI18nKey());
        assertEquals("Code Writer",DefaultRole.of("Code Writer").get().getName());
        assertEquals("RoleCodeWriter", DefaultRole.of("Code Writer").get().getI18nKey());
        assertEquals("Code Reader", DefaultRole.of("Code Reader").get().getName());
        assertEquals("RoleCodeReader", DefaultRole.of("Code Reader").get().getI18nKey());

    }
}

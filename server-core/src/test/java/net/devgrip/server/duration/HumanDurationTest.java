package net.devgrip.server.duration;

import net.devgrip.commons.loader.AppLoader;
import net.devgrip.commons.loader.AppLoaderMocker;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.duration.HumanDuration;
import org.junit.Test;
import org.mockito.Mockito;

import java.time.Duration;
import java.time.LocalDateTime;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;

/**
 *
 */

public class HumanDurationTest extends AppLoaderMocker {
	private HumanDuration duration;
	private HumanDuration durationCN;
	
	@Test
	public void test() {
		// 测试基本时间单位转换
		assertEquals("1 minutes", duration.applyTo(60L));
		assertEquals("2 minutes", duration.applyTo(120L));
		assertEquals("1 hours", duration.applyTo(3600L));
		assertEquals("2 hours", duration.applyTo(7200L));
		assertEquals("1 days", duration.applyTo(86400L));
		assertEquals("2 days", duration.applyTo(172800L));
		assertEquals("1 weeks", duration.applyTo(604800L));
		assertEquals("4 weeks", duration.applyTo(2419200L));
		assertEquals("1 months", duration.applyTo(2592000L));

		// 测试复合时间单位
		assertEquals("1 hours 30 minutes", duration.applyTo(5400L));
		assertEquals("2 days 3 hours", duration.applyTo(183600L));
		assertEquals("1 hours 1 minutes 30 seconds", duration.applyTo(3690L));
		assertEquals("2 days 4 hours 15 minutes", duration.applyTo(188100L));
		assertEquals("1 weeks 2 days 5 hours", duration.applyTo(795600L));
		assertEquals("1 months 1 weeks 1 days", duration.applyTo(3283200L));
		assertEquals("1 months 1 weeks 3 days", duration.applyTo(3456000L));

		// 测试边界情况
		assertEquals("No time.", duration.applyTo(0L));
		assertEquals("1 minutes", duration.applyTo(60L));
		assertEquals("1 minutes 1 seconds", duration.applyTo(61L));
	}

	@Test
	public void testHourMinutesSecondsDuration() {
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime localDateTime = now.plusSeconds(59);
		LocalDateTime localDateTime2 = now.plusSeconds(61);
		Duration d = Duration.between(now, localDateTime);
		Duration d2 = Duration.between(now, localDateTime2);
		long seconds59 = d.getSeconds();
		long seconds61 = d2.getSeconds();
		String s = duration.applyTo(seconds59);
		assertEquals("59 seconds", s);
		String s1 = duration.applyTo(seconds61);
		assertEquals("1 minutes 1 seconds", s1);

		
		LocalDateTime localDateTime3 = now.plusMinutes(61).plusSeconds(1);
		Duration d3 = Duration.between(now, localDateTime3);
		long minutes61Seconds = d3.getSeconds();
		String s21 = duration.applyTo(minutes61Seconds);
		assertEquals("1 hours 1 minutes 1 seconds", s21);

		LocalDateTime localDateTime4 = now.plusHours(23).plusMinutes(59).plusSeconds(59);
		Duration d4 = Duration.between(now, localDateTime4);
		long H23M59S59 = d4.getSeconds();
		String s_H23M59S59 = duration.applyTo(H23M59S59);
		assertEquals("23 hours 59 minutes 59 seconds", s_H23M59S59);

		LocalDateTime localDateTime5 = now.plusHours(23).plusMinutes(59).plusSeconds(60);
		Duration d5 = Duration.between(now, localDateTime5);
		long H23M59S60 = d5.getSeconds();
		String s_H23M59S60 = duration.applyTo(H23M59S60);
		assertNotEquals("23 hours 59 minutes 59 seconds", s_H23M59S60);
		assertEquals("1 days", s_H23M59S60);
	}

	@Test
	public void testCompoundDurationCN() {
		// 测试基本时间单位转换（中文）
		assertEquals("1分钟", durationCN.applyTo(60L));
		assertEquals("2分钟", durationCN.applyTo(120L));
		assertEquals("1小时", durationCN.applyTo(3600L));
		assertEquals("2小时", durationCN.applyTo(7200L));
		assertEquals("1天", durationCN.applyTo(86400L));
		assertEquals("2天", durationCN.applyTo(172800L));
		
		// 测试复合时间单位（中文）
		assertEquals("1小时30分钟", durationCN.applyTo(5400L));
		assertEquals("2天3小时", durationCN.applyTo(183600L));
		assertEquals("1小时1分钟30秒", durationCN.applyTo(3690L));
		assertEquals("2天4小时15分钟", durationCN.applyTo(188100L));
		assertEquals("1周2天5小时", durationCN.applyTo(795600L));
		assertEquals("1月1周3天", durationCN.applyTo(3456000L));
		
		// 测试边界情况（中文）
		assertEquals("时间过短忽略不计", durationCN.applyTo(0L));
		assertEquals("1分钟", durationCN.applyTo(60L));
		assertEquals("1分钟1秒", durationCN.applyTo(61L));
	}

	@Override
	protected void setup() {
		I18nManager i18nManager = Mockito.mock(I18nManager.class);
		Mockito.when(AppLoader.getInstance(I18nManager.class)).thenReturn(i18nManager);
		Mockito.when(i18nManager.get("HumanDuration.delimiter")).thenReturn(" ");
		Mockito.when(i18nManager.get("HumanDuration.noTime")).thenReturn("No time.");
		Mockito.when(i18nManager.get("HumanDuration.na")).thenReturn("n/a");
		Mockito.when(i18nManager.get("HumanDuration.year")).thenReturn("%d years");
		Mockito.when(i18nManager.get("HumanDuration.month")).thenReturn("%d months");
		Mockito.when(i18nManager.get("HumanDuration.week")).thenReturn("%d weeks");
		Mockito.when(i18nManager.get("HumanDuration.day")).thenReturn("%d days");
		Mockito.when(i18nManager.get("HumanDuration.hour")).thenReturn("%d hours");
		Mockito.when(i18nManager.get("HumanDuration.minute")).thenReturn("%d minutes");
		Mockito.when(i18nManager.get("HumanDuration.second")).thenReturn("%d seconds");
		duration = new HumanDuration(i18nManager);
		duration.initTimeUnits();

		I18nManager i18nManagerCN = Mockito.mock(I18nManager.class);
		Mockito.when(AppLoader.getInstance(I18nManager.class)).thenReturn(i18nManagerCN);
		Mockito.when(i18nManagerCN.get("HumanDuration.noTime")).thenReturn("时间过短忽略不计");
		Mockito.when(i18nManagerCN.get("HumanDuration.na")).thenReturn("不详");
		Mockito.when(i18nManagerCN.get("HumanDuration.delimiter")).thenReturn("");
		Mockito.when(i18nManagerCN.get("HumanDuration.year")).thenReturn("%d年");
		Mockito.when(i18nManagerCN.get("HumanDuration.month")).thenReturn("%d月");
		Mockito.when(i18nManagerCN.get("HumanDuration.week")).thenReturn("%d周");
		Mockito.when(i18nManagerCN.get("HumanDuration.day")).thenReturn("%d天");
		Mockito.when(i18nManagerCN.get("HumanDuration.hour")).thenReturn("%d小时");
		Mockito.when(i18nManagerCN.get("HumanDuration.minute")).thenReturn("%d分钟");
		Mockito.when(i18nManagerCN.get("HumanDuration.second")).thenReturn("%d秒");
		durationCN = new HumanDuration(i18nManagerCN);
		durationCN.initTimeUnits();
	}

	@Override
	protected void teardown() {

	}
}
